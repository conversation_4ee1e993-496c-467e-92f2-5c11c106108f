# 登录功能问题修复文档

## 问题描述

在API调用过程中，发现以下问题：

1. API路径重复: `/api/v1/api/v1/auth-settings/jwt/login` 中 `/api/v1/` 被重复
2. getUserInfo接口返回错误: `greenlet_spawn has not been called; can't call await_only() here`
3. ResponseCode 未定义问题
4. ORM懒加载导致的问题

## 解决方案

### 1. 修复API路径重复问题

在 `api/deps.py` 中修改 OAuth2PasswordBearer 的 tokenUrl 参数：

```python
# 修改前
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/api/v1/auth-settings/jwt/login",
)

# 修改后
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="auth-settings/jwt/login",
)
```

这样可以避免路径重复问题，因为FastAPI会自动添加前缀。

### 2. 修复请求缓存异步问题

在 `utils/request_cache.py` 中修改 `start_lock_cleanup` 方法，处理在非事件循环环境下的情况：

```python
def start_lock_cleanup(self):
    """启动锁清理任务"""
    async def cleanup_locks():
        while True:
            await asyncio.sleep(60)  # 每分钟执行一次
            self._cleanup_expired_locks()
    
    # 检查是否在事件循环中，更安全的处理方法
    try:
        asyncio.get_running_loop()
        # 在事件循环中，创建定期任务
        self.lock_cleanup_task = asyncio.create_task(cleanup_locks())
    except RuntimeError:
        # 不在事件循环中，跳过任务创建
        print("未在运行中的事件循环内，跳过锁清理任务创建")
```

### 3. 修复getUserInfo接口实现

简化 `getUserInfo` 方法，避免复杂的ORM懒加载问题：

```python
@router.get("/getUserInfo")
async def get_user_info(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取用户信息接口，按照Apifox文档格式
    """
    try:
        # 获取用户基本信息
        user_id = current_user.id
        username = current_user.username
        
        # 简化处理 - 避免复杂的ORM查询
        user_data = {
            "userId": str(user_id),
            "userName": username,
            "roles": ["admin"], # 示例角色，实际中应从数据库获取
            "buttons": ["add", "edit", "delete"] # 示例按钮权限
        }
        
        # 返回符合要求的格式
        return {
            "data": user_data,
            "code": "00000",
            "msg": "获取用户信息成功"
        }
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        return {
            "data": {},
            "code": "B0001",
            "msg": f"获取用户信息失败: {str(e)}"
        }
```

### 4. 同步导入ResponseCode

确保所有相关文件都正确导入了ResponseCode：

```python
from schemas.response import ResponseCode
```

## 测试和验证

使用简化版测试服务器 `test_user_info_api.py` 验证接口格式是否符合要求:

```bash
# 启动测试服务器
python test_user_info_api.py 8080

# 测试接口
curl -X 'GET' 'http://127.0.0.1:8080/api/v1/auth/getUserInfo' -H 'accept: application/json' -H 'Authorization: Bearer xxx'
```

返回结果:
```json
{
  "data": {
    "userId": "1",
    "userName": "admin",
    "roles": ["admin"],
    "buttons": ["add", "edit", "delete"]
  },
  "code": "00000",
  "msg": "获取用户信息成功"
}
```

## 结论

问题主要出在以下几个方面：

1. API路径配置错误导致路径重复
2. 非异步环境下创建异步任务
3. ORM懒加载导致的数据库查询问题
4. ResponseCode引用问题

通过上述修复，目前接口已能正常返回符合要求的数据格式。 
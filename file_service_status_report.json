{"timestamp": "2025-06-03T09:13:24.160000", "overall_status": "degraded", "issues": ["文件上传下载测试失败"], "database": {"status": "connected"}, "storage": {"default": {"type": "minio", "service_class": "MinioFileStorageService", "status": "available"}, "local": {"base_path": "/Users/<USER>/projects/admin-server/app/storage/files", "temp_dir": "temp", "base_path_exists": true, "temp_dir_exists": true, "base_path_writable": true, "temp_dir_writable": true, "status": "available"}, "minio": {"endpoint": "192.168.2.201:19000", "bucket": "file-storage", "bucket_ready": true, "status": "connected"}}, "configuration": {"storage_type": "minio", "storage_path": "storage", "temp_dir": "temp", "minio": {"endpoint": "192.168.2.201", "api_port": 19000, "bucket": "file-storage", "use_ssl": false}, "database": {"url": "postgresql+asyncpg://postgres:xfPCP8FzG4jT7d5Z@192..."}}, "api_endpoints": {"/health": {"status": "warning", "code": 500}, "/api/v1/files/status": {"status": "warning", "code": 401}, "/api/v1/files/": {"status": "warning", "code": 401}}, "upload_test": {"status": "failed"}}
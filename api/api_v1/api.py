from fastapi import APIRouter
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from api.api_v1.endpoints import (
    auth, users, roles, permissions, sms_configs, menus, configs, 
    custom_auth, oauth_providers, cas_configs, audit_logs, 
    data_processing, api_clients, files, routes, simple_auth
)
from core.users import fastapi_users, jwt_backend, cookie_backend
from schemas.user import User, UserCreate, UserUpdate
from models.user import User as UserModel
from api.deps import get_current_user
from db.session import get_db

api_router = APIRouter()


# 注册各模块路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
# api_router.include_router(upload.router, prefix="/upload", tags=["文件上传"])
# api_router.include_router(system.router, prefix="/system", tags=["系统信息"])
# api_router.include_router(dashboards.router, prefix="/dashboards", tags=["仪表盘"])
# api_router.include_router(maintenance.router, prefix="/maintenance", tags=["系统维护"])
api_router.include_router(roles.router, prefix="/roles", tags=["角色管理"])
api_router.include_router(permissions.router, prefix="/permissions", tags=["权限管理"])
api_router.include_router(sms_configs.router, prefix="/sms-configs", tags=["短信配置"])

# 菜单管理
api_router.include_router(
    menus.router,
    prefix="/menus",
    tags=["菜单管理"]
)

# 系统配置
api_router.include_router(
    configs.router,
    prefix="/configs",
    tags=["系统配置"]
)

# 认证配置管理
api_router.include_router(
    custom_auth.router,
    prefix="/auth-settings",
    tags=["认证配置"]
)

api_router.include_router(
    oauth_providers.router,
    prefix="/oauth-providers",
    tags=["OAuth提供商"]
)

api_router.include_router(
    cas_configs.router,
    prefix="/cas-configs",
    tags=["CAS配置"]
)

# 新增审计日志路由
api_router.include_router(
    audit_logs.router,
    prefix="/audit-logs",
    tags=["审计日志"]
)

# 新增数据处理路由
api_router.include_router(
    data_processing.router,
    prefix="/processing",
    tags=["数据处理"]
)

# API客户端管理
api_router.include_router(
    api_clients.router,
    prefix="/api-clients",
    tags=["API客户端"]
)

# 文件管理
api_router.include_router(
    files.router,
    prefix="/files",
    tags=["文件管理"]
)

# 添加simple_auth路由
api_router.include_router(
    simple_auth.router,
    prefix="/simple-auth",
    tags=["简化认证"]
)

# 路由管理
api_router.include_router(routes.router, prefix="/route", tags=["路由管理"])

from fastapi import APIRouter, Depends, HTTPException, Request, Response, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional
import logging

from db.session import get_db
from api.deps import get_current_user
from models.user import User
from schemas.response import ResponseCode

router = APIRouter()
logger = logging.getLogger(__name__)

# 常量路由数据
CONSTANT_ROUTES = [
    {
        "path": "/dashboard",
        "name": "Dashboard",
        "component": "LAYOUT",
        "redirect": "/dashboard/analysis",
        "meta": {
            "title": "仪表盘",
            "icon": "DashboardOutlined",
            "hideChildrenInMenu": False,
            "orderNo": 1
        },
        "children": [
            {
                "path": "analysis",
                "name": "Analysis",
                "component": "/dashboard/analysis/index",
                "meta": {
                    "title": "分析页",
                    "hideMenu": False,
                    "orderNo": 1
                }
            },
            {
                "path": "workbench",
                "name": "Workbench",
                "component": "/dashboard/workbench/index",
                "meta": {
                    "title": "工作台",
                    "hideMenu": False,
                    "orderNo": 2
                }
            }
        ]
    },
    {
        "path": "/system",
        "name": "System",
        "component": "LAYOUT",
        "redirect": "/system/account",
        "meta": {
            "title": "系统管理",
            "icon": "SettingOutlined",
            "orderNo": 2
        },
        "children": [
            {
                "path": "account",
                "name": "AccountManagement",
                "component": "/system/account/index",
                "meta": {
                    "title": "账号管理",
                    "orderNo": 1
                }
            },
            {
                "path": "role",
                "name": "RoleManagement",
                "component": "/system/role/index",
                "meta": {
                    "title": "角色管理",
                    "orderNo": 2
                }
            }
        ]
    }
]

@router.get("/getUserRoutes")
async def get_user_routes(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户路由数据，基于用户权限生成可访问的路由
    """
    try:
        # 这里需要根据用户权限生成路由
        # 简化实现: 根据用户角色确定可访问的路由
        
        # 1. 获取用户角色
        roles = []
        if hasattr(current_user, 'roles') and current_user.roles:
            roles = [role.name for role in current_user.roles]
        
        # 2. 根据角色生成路由
        # 示例: 默认基础路由 + 管理员特有路由
        user_routes = CONSTANT_ROUTES.copy()
        
        # 如果是管理员，添加管理员特有路由
        if "admin" in roles:
            admin_routes = [
                {
                    "path": "/admin",
                    "name": "Admin",
                    "component": "LAYOUT",
                    "meta": {
                        "title": "管理员页面",
                        "icon": "UserOutlined",
                        "orderNo": 3
                    },
                    "children": [
                        {
                            "path": "users",
                            "name": "UserList",
                            "component": "/admin/users/index",
                            "meta": {
                                "title": "用户列表",
                                "orderNo": 1
                            }
                        },
                        {
                            "path": "settings",
                            "name": "SystemSettings",
                            "component": "/admin/settings/index",
                            "meta": {
                                "title": "系统设置",
                                "orderNo": 2
                            }
                        }
                    ]
                }
            ]
            user_routes.extend(admin_routes)
        
        return {
            "data": user_routes,
            "code": "00000",
            "msg": "获取用户路由成功"
        }
    except Exception as e:
        logger.error(f"获取用户路由失败: {str(e)}")
        return {
            "data": [],
            "code": "B0001",
            "msg": f"获取用户路由失败: {str(e)}"
        }

@router.get("/isRouteExist")
async def is_route_exist(
    path: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    检查路由是否存在，并且用户是否有权限访问
    """
    try:
        # 检查路由是否存在
        # 1. 获取用户所有可访问路由
        user_routes_response = await get_user_routes(current_user, db)
        if user_routes_response["code"] != "00000":
            return {
                "data": {"exist": False},
                "code": "B0001",
                "msg": "获取路由信息失败"
            }
        
        # 2. 检查路径是否在可访问路由中
        user_routes = user_routes_response["data"]
        route_exists = check_route_exists(user_routes, path)
        
        return {
            "data": {"exist": route_exists},
            "code": "00000",
            "msg": "检查路由成功"
        }
    except Exception as e:
        logger.error(f"检查路由失败: {str(e)}")
        return {
            "data": {"exist": False},
            "code": "B0001",
            "msg": f"检查路由失败: {str(e)}"
        }

@router.get("/getConstantRoutes")
async def get_constant_routes():
    """
    获取固定的路由数据(不需要权限)
    """
    try:
        return {
            "data": CONSTANT_ROUTES,
            "code": "00000",
            "msg": "获取固定路由成功"
        }
    except Exception as e:
        logger.error(f"获取固定路由失败: {str(e)}")
        return {
            "data": [],
            "code": "B0001",
            "msg": f"获取固定路由失败: {str(e)}"
        }

def check_route_exists(routes, target_path):
    """递归检查路由是否存在"""
    for route in routes:
        # 检查当前路由
        if route.get("path") == target_path:
            return True
            
        # 检查子路由
        if "children" in route and route["children"]:
            if check_route_exists(route["children"], target_path):
                return True
                
    return False 
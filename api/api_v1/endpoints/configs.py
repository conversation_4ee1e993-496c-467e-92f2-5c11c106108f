from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from api.deps import get_current_superuser, get_current_active_user
from auth.users import User
from crud.crud_config import config, get_all_config_settings
from db.session import get_db
from schemas.config import Config, ConfigCreate, ConfigUpdate
from schemas.response import ResponseModel, PageResponseModel

router = APIRouter()


@router.get("/", response_model=ResponseModel[List[Config]])
async def read_configs(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_superuser),  # 只有超级管理员可以查看所有配置
) -> Any:
    """
    获取配置列表
    """
    configs = await config.get_multi(db, skip=skip, limit=limit)
    
    return ResponseModel(data=configs)


@router.get("/dict", response_model=ResponseModel[Dict[str, Any]])
async def read_configs_dict(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取所有配置，以字典形式返回
    """
    configs_dict = await get_all_config_settings(db)
    
    # 过滤超级管理员专属配置
    if not current_user.is_superuser:
        # 移除敏感配置
        for key in list(configs_dict.keys()):
            if key.startswith("ADMIN_") or key.endswith("_SECRET") or key.endswith("_KEY"):
                configs_dict.pop(key, None)
    
    return ResponseModel(data=configs_dict)


@router.post("/", response_model=ResponseModel[Config])
async def create_config(
    *,
    db: AsyncSession = Depends(get_db),
    config_in: ConfigCreate,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    创建新配置
    """
    # 检查是否已存在同名配置
    db_config = await config.get_by_key(db, key=config_in.key)
    if db_config:
        # 已存在则更新
        return ResponseModel(
            data=db_config,
            msg="配置已存在，已更新值"
        )
    
    # 创建配置
    created_config = await config.create(db=db, obj_in=config_in)
    
    return ResponseModel(data=created_config)


@router.get("/{key}", response_model=ResponseModel[Config])
async def read_config(
    *,
    db: AsyncSession = Depends(get_db),
    key: str,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取配置详情
    """
    db_config = await config.get_by_key(db, key=key)
    if not db_config:
        raise HTTPException(
            status_code=404,
            detail="配置不存在",
        )
    
    # 权限检查：非超级管理员不能读取敏感配置
    if not current_user.is_superuser and (key.startswith("ADMIN_") or key.endswith("_SECRET") or key.endswith("_KEY")):
        raise HTTPException(
            status_code=403,
            detail="权限不足",
        )
    
    return ResponseModel(data=db_config)


@router.put("/{key}", response_model=ResponseModel[Config])
async def update_config(
    *,
    db: AsyncSession = Depends(get_db),
    key: str,
    config_in: ConfigUpdate,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    更新配置
    """
    db_config = await config.get_by_key(db, key=key)
    if not db_config:
        raise HTTPException(
            status_code=404,
            detail="配置不存在",
        )
    
    # 更新配置
    updated_config = await config.update_by_key(db, key=key, value=config_in.value)
    
    return ResponseModel(data=updated_config)


@router.delete("/{key}", response_model=ResponseModel)
async def delete_config(
    *,
    db: AsyncSession = Depends(get_db),
    key: str,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    删除配置
    """
    db_config = await config.get_by_key(db, key=key)
    if not db_config:
        raise HTTPException(
            status_code=404,
            detail="配置不存在",
        )
    
    # 删除配置
    await config.remove(db, id=db_config.id)
    
    return ResponseModel(msg="配置删除成功") 
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from api.deps import get_current_superuser, get_current_active_user
from auth.users import User
from crud.crud_permission import permission
from db.session import get_db
from schemas.role import Permission, PermissionCreate, PermissionUpdate
from schemas.response import ResponseModel, PageResponseModel

router = APIRouter()


@router.get("/", response_model=PageResponseModel[Permission])
async def read_permissions(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取权限列表
    """
    permissions = await permission.get_multi(db, skip=skip, limit=limit)
    total = await permission.count(db)
    
    return PageResponseModel(
        data=permissions,
        page={
            "page": skip // limit + 1,
            "size": limit,
            "total": total,
            "pages": (total + limit - 1) // limit,
            "has_next": skip + limit < total,
            "has_prev": skip > 0,
        },
    )


@router.post("/", response_model=ResponseModel[Permission])
async def create_permission(
    *,
    db: AsyncSession = Depends(get_db),
    permission_in: PermissionCreate,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    创建新权限
    """
    # 检查同名权限是否已存在
    db_permission = await permission.get_by_name(db, name=permission_in.name)
    if db_permission:
        raise HTTPException(
            status_code=400,
            detail="同名权限已存在",
        )
    
    # 创建权限
    created_permission = await permission.create(db=db, obj_in=permission_in)
    
    return ResponseModel(data=created_permission)


@router.get("/{id}", response_model=ResponseModel[Permission])
async def read_permission(
    *,
    db: AsyncSession = Depends(get_db),
    id: int,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取权限详情
    """
    db_permission = await permission.get(db, id=id)
    if not db_permission:
        raise HTTPException(
            status_code=404,
            detail="权限不存在",
        )
    
    return ResponseModel(data=db_permission)


@router.put("/{id}", response_model=ResponseModel[Permission])
async def update_permission(
    *,
    db: AsyncSession = Depends(get_db),
    id: int,
    permission_in: PermissionUpdate,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    更新权限
    """
    db_permission = await permission.get(db, id=id)
    if not db_permission:
        raise HTTPException(
            status_code=404,
            detail="权限不存在",
        )
    
    # 检查是否尝试更新为同名权限
    if permission_in.name and permission_in.name != db_permission.name:
        existing_permission = await permission.get_by_name(db, name=permission_in.name)
        if existing_permission:
            raise HTTPException(
                status_code=400,
                detail="同名权限已存在",
            )
    
    # 更新权限
    updated_permission = await permission.update(db=db, db_obj=db_permission, obj_in=permission_in)
    
    return ResponseModel(data=updated_permission)


@router.delete("/{id}", response_model=ResponseModel)
async def delete_permission(
    *,
    db: AsyncSession = Depends(get_db),
    id: int,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    删除权限
    """
    db_permission = await permission.get(db, id=id)
    if not db_permission:
        raise HTTPException(
            status_code=404,
            detail="权限不存在",
        )
    
    # 检查是否有角色关联该权限
    if db_permission.roles and len(db_permission.roles) > 0:
        raise HTTPException(
            status_code=400,
            detail="该权限已关联角色，无法删除",
        )
    
    # 删除权限
    await permission.remove(db, id=id)
    
    return ResponseModel(msg="权限删除成功")


@router.get("/role/{role_id}", response_model=ResponseModel[List[Permission]])
async def read_role_permissions(
    *,
    db: AsyncSession = Depends(get_db),
    role_id: int,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取角色的权限列表
    """
    # 获取角色权限
    role_permissions = await permission.get_role_permissions(db, role_id=role_id)
    
    return ResponseModel(data=role_permissions)


@router.get("/user/me", response_model=ResponseModel[List[str]])
async def read_own_permissions(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取当前用户的权限列表
    """
    # 超级管理员拥有所有权限
    if current_user.is_superuser:
        all_permissions = await permission.get_multi(db)
        return ResponseModel(data=[p.name for p in all_permissions])
    
    # 获取用户权限
    user_permissions = await permission.get_user_permissions(db, user_id=current_user.id)
    
    return ResponseModel(data=[p.name for p in user_permissions]) 
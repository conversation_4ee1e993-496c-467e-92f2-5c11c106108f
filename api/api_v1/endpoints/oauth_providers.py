#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OAuth提供商配置API端点
"""
from typing import Any, List
import logging

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from api.deps import get_current_superuser # 假设只有超级管理员可以管理
from auth.users import User
from crud.crud_oauth_provider import oauth_provider_config
from db.session import get_db
from schemas.oauth_provider import OAuthProviderConfig as OAuthProviderSchema, OAuthProviderConfigCreate, OAuthProviderConfigUpdate, OAuthProviderConfigList
from schemas.response import ResponseModel, PageResponseModel # 假设有这些标准响应模型
from utils.auth_config_manager import auth_config_manager
from services.storage_factory import StorageFactory

router = APIRouter()
logger = logging.getLogger(__name__)

async def process_provider_icon(provider_obj: dict, db: AsyncSession) -> dict:
    """处理提供商图标，将文件ID转换为预签名URL"""
    if not provider_obj.get("icon"):
        return provider_obj
    
    try:
        # 如果图标已经是URL格式，则不处理
        if provider_obj["icon"].startswith('http://') or provider_obj["icon"].startswith('https://'):
            return provider_obj
        
        # 获取存储服务
        storage_service = StorageFactory.get_storage_service()
        
        # 尝试将icon作为文件ID处理
        file_id = provider_obj["icon"]
        # 获取文件信息
        file_info = await storage_service.get_file_info(file_id, db=db)
        if file_info and "file_url" in file_info:
            provider_obj["icon"] = file_info["file_url"]
    except Exception as e:
        logger.warning(f"处理提供商图标失败: {str(e)}")
    
    return provider_obj

@router.post(
    "/", 
    response_model=ResponseModel[OAuthProviderSchema],
    dependencies=[Depends(get_current_superuser)] # 权限控制
)
async def create_oauth_provider(
    *,
    db: AsyncSession = Depends(get_db),
    provider_in: OAuthProviderConfigCreate,
    background_tasks: BackgroundTasks,
) -> Any:
    """
    创建新的OAuth提供商配置。
    需要超级管理员权限。
    """
    existing_provider = await oauth_provider_config.get_by_provider_name(db, provider_name=provider_in.provider_name)
    if existing_provider:
        raise HTTPException(
            status_code=400,
            detail=f"名为 '{provider_in.provider_name}' 的OAuth提供商配置已存在。"
        )
    provider = await oauth_provider_config.create(db, obj_in=provider_in)
    
    # 在创建后自动刷新认证配置 (异步处理)
    background_tasks.add_task(auth_config_manager.refresh_all_configs, db)
    
    # 处理图标
    provider_dict = {
        "id": provider.id,
        "provider_name": provider.provider_name,
        "client_id": provider.client_id,
        "client_secret": provider.client_secret,
        "authorize_url": provider.authorize_url,
        "token_url": provider.token_url,
        "user_info_url": provider.user_info_url,
        "jwks_uri": provider.jwks_uri,
        "scopes": provider.scopes,
        "icon": provider.icon,
        "is_active": provider.is_active,
        "description": provider.description,
        "created_at": provider.created_at,
        "updated_at": provider.updated_at
    }
    provider_dict = await process_provider_icon(provider_dict, db)
    
    return ResponseModel(data=provider_dict, msg="OAuth提供商配置创建成功")

@router.get(
    "/", 
    response_model=ResponseModel[List[OAuthProviderSchema]],
    dependencies=[Depends(get_current_superuser)]
)
async def read_oauth_providers(
    *,
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取所有OAuth提供商配置列表。
    需要超级管理员权限。
    """
    providers = await oauth_provider_config.get_multi(db, skip=skip, limit=limit)
    
    # 处理所有提供商的图标
    processed_providers = []
    for p in providers:
        provider_dict = {
            "id": p.id,
            "provider_name": p.provider_name,
            "client_id": p.client_id,
            "client_secret": p.client_secret,
            "authorize_url": p.authorize_url,
            "token_url": p.token_url,
            "user_info_url": p.user_info_url,
            "jwks_uri": p.jwks_uri,
            "scopes": p.scopes,
            "icon": p.icon,
            "is_active": p.is_active,
            "description": p.description,
            "created_at": p.created_at,
            "updated_at": p.updated_at
        }
        provider_dict = await process_provider_icon(provider_dict, db)
        processed_providers.append(provider_dict)
    
    return ResponseModel(data=processed_providers)

@router.get(
    "/active", 
    response_model=ResponseModel[List[OAuthProviderConfigList]]
)
async def read_active_oauth_providers(
    db: AsyncSession = Depends(get_db),
    # 此接口通常不需要认证，因为前端登录页面需要展示可用的登录方式
) -> Any:
    """
    获取所有激活的OAuth提供商配置列表 (用于前端展示)。
    """
    providers = await oauth_provider_config.get_active_providers(db, limit=50) #限制数量
    
    # 处理所有提供商的图标
    processed_providers = []
    for p in providers:
        provider_dict = {
            "id": p.id,
            "provider_name": p.provider_name,
            "icon": p.icon,
            "is_active": p.is_active,
            "description": p.description
        }
        provider_dict = await process_provider_icon(provider_dict, db)
        processed_providers.append(provider_dict)
    
    return ResponseModel(data=processed_providers)

@router.get(
    "/{provider_id}", 
    response_model=ResponseModel[OAuthProviderSchema],
    dependencies=[Depends(get_current_superuser)]
)
async def read_oauth_provider(
    *,
    db: AsyncSession = Depends(get_db),
    provider_id: int,
) -> Any:
    """
    通过ID获取指定的OAuth提供商配置。
    需要超级管理员权限。
    """
    provider = await oauth_provider_config.get(db, id=provider_id)
    if not provider:
        raise HTTPException(status_code=404, detail="OAuth提供商配置未找到")
    
    # 处理图标
    provider_dict = {
        "id": provider.id,
        "provider_name": provider.provider_name,
        "client_id": provider.client_id,
        "client_secret": provider.client_secret,
        "authorize_url": provider.authorize_url,
        "token_url": provider.token_url,
        "user_info_url": provider.user_info_url,
        "jwks_uri": provider.jwks_uri,
        "scopes": provider.scopes,
        "icon": provider.icon,
        "is_active": provider.is_active,
        "description": provider.description,
        "created_at": provider.created_at,
        "updated_at": provider.updated_at
    }
    provider_dict = await process_provider_icon(provider_dict, db)
    
    return ResponseModel(data=provider_dict)

@router.put(
    "/{provider_id}", 
    response_model=ResponseModel[OAuthProviderSchema],
    dependencies=[Depends(get_current_superuser)]
)
async def update_oauth_provider(
    *,
    db: AsyncSession = Depends(get_db),
    provider_id: int,
    provider_in: OAuthProviderConfigUpdate,
    background_tasks: BackgroundTasks,
) -> Any:
    """
    更新指定的OAuth提供商配置。
    需要超级管理员权限。
    """
    provider = await oauth_provider_config.get(db, id=provider_id)
    if not provider:
        raise HTTPException(status_code=404, detail="OAuth提供商配置未找到")
    updated_provider = await oauth_provider_config.update(db, db_obj=provider, obj_in=provider_in)
    
    # 在更新后自动刷新认证配置 (异步处理)
    background_tasks.add_task(auth_config_manager.refresh_all_configs, db)
    
    # 处理图标
    provider_dict = {
        "id": updated_provider.id,
        "provider_name": updated_provider.provider_name,
        "client_id": updated_provider.client_id,
        "client_secret": updated_provider.client_secret,
        "authorize_url": updated_provider.authorize_url,
        "token_url": updated_provider.token_url,
        "user_info_url": updated_provider.user_info_url,
        "jwks_uri": updated_provider.jwks_uri,
        "scopes": updated_provider.scopes,
        "icon": updated_provider.icon,
        "is_active": updated_provider.is_active,
        "description": updated_provider.description,
        "created_at": updated_provider.created_at,
        "updated_at": updated_provider.updated_at
    }
    provider_dict = await process_provider_icon(provider_dict, db)
    
    return ResponseModel(data=provider_dict, msg="OAuth提供商配置更新成功")

@router.delete(
    "/{provider_id}", 
    response_model=ResponseModel,
    dependencies=[Depends(get_current_superuser)]
)
async def delete_oauth_provider(
    *,
    db: AsyncSession = Depends(get_db),
    provider_id: int,
    background_tasks: BackgroundTasks,
) -> Any:
    """
    删除指定的OAuth提供商配置。
    需要超级管理员权限。
    """
    provider = await oauth_provider_config.get(db, id=provider_id)
    if not provider:
        raise HTTPException(status_code=404, detail="OAuth提供商配置未找到")
    await oauth_provider_config.remove(db, id=provider_id)
    
    # 在删除后自动刷新认证配置 (异步处理)
    background_tasks.add_task(auth_config_manager.refresh_all_configs, db)
    
    return ResponseModel(msg="OAuth提供商配置删除成功")

@router.put(
    "/{provider_id}/toggle", 
    response_model=ResponseModel[OAuthProviderSchema],
    dependencies=[Depends(get_current_superuser)]
)
async def toggle_oauth_provider(
    *,
    db: AsyncSession = Depends(get_db),
    provider_id: int,
    active: bool = Query(..., description="是否激活"),
    background_tasks: BackgroundTasks,
) -> Any:
    """
    快速启用或禁用指定的OAuth提供商配置。
    需要超级管理员权限。
    """
    provider = await oauth_provider_config.get(db, id=provider_id)
    if not provider:
        raise HTTPException(status_code=404, detail="OAuth提供商配置未找到")
    
    # 只修改激活状态
    updated_provider = await oauth_provider_config.update(
        db, 
        db_obj=provider, 
        obj_in={"is_active": active}
    )
    
    # 在状态切换后自动刷新认证配置 (异步处理)
    background_tasks.add_task(auth_config_manager.refresh_all_configs, db)
    
    status = "启用" if active else "禁用"
    return ResponseModel(data=updated_provider, msg=f"OAuth提供商配置已{status}") 
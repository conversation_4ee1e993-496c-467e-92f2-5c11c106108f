"""
大型数据处理API端点 - 使用Polars处理大型数据
"""
import os
import json
import tempfile
import logging
import polars as pl
from typing import Any, Dict, List, Optional, Union
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from api.deps import get_current_user, get_db
from models.user import User
from services.data_processing import data_processor, DataProcessor
from core.config import settings
from utils.file_utils import save_upload_file_temp

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter()

# 定义请求和响应模型
class DataOperationBase(BaseModel):
    """数据操作基础模型"""
    type: str
    params: Dict[str, Any] = Field(default_factory=dict)

class DataProcessRequest(BaseModel):
    """数据处理请求"""
    operations: List[DataOperationBase]
    output_format: Optional[str] = "json"
    file_id: Optional[str] = None

class DataStatisticsResponse(BaseModel):
    """数据统计响应"""
    row_count: int
    column_count: int
    columns: List[str]
    dtypes: Dict[str, str]
    memory_usage: int
    numeric_stats: Optional[Dict[str, Dict[str, Any]]] = None

class ProcessingStatus(BaseModel):
    """处理状态"""
    task_id: str
    status: str
    progress: Optional[float] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

# 内存中文件存储
_temp_files = {}

# 上传数据文件
@router.post("/data/upload", summary="上传数据文件")
async def upload_data_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    use_lazy: bool = Query(True, description="是否使用懒加载模式（推荐用于大文件）"),
    current_user: User = Depends(get_current_user),
):
    """
    上传数据文件进行处理
    
    - 支持CSV、JSON、Excel、Parquet等格式
    - 默认使用懒加载模式处理大文件
    - 返回文件ID和基本统计信息
    """
    try:
        # 获取文件扩展名
        filename = file.filename
        file_extension = os.path.splitext(filename)[1].lower()
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp:
            temp_path = temp.name
            # 保存上传的文件
            await save_upload_file_temp(file, temp_path)
        
        # 计算文件哈希作为ID
        import hashlib
        file_id = hashlib.md5(f"{filename}_{os.path.getsize(temp_path)}_{os.path.getmtime(temp_path)}".encode()).hexdigest()
        
        # 在后台加载数据（避免阻塞响应）
        async def load_and_analyze():
            try:
                # 加载数据文件
                df = await data_processor.load_data(
                    file_path=temp_path, 
                    use_lazy=use_lazy,
                )
                
                # 获取数据统计信息
                stats = await data_processor.get_summary_stats(df)
                
                # 存储临时文件信息和数据对象
                _temp_files[file_id] = {
                    "file_path": temp_path,
                    "filename": filename,
                    "extension": file_extension,
                    "user_id": current_user.id,
                    "dataframe": df,
                    "stats": stats,
                    "created_at": os.path.getctime(temp_path),
                }
                
                # 设置超时清理（1小时后）
                background_tasks.add_task(_cleanup_temp_file, file_id, hours=1)
                
            except Exception as e:
                logger.error(f"加载文件 {filename} 失败: {str(e)}")
                if file_id in _temp_files:
                    _temp_files[file_id] = {
                        "error": str(e)
                    }
        
        # 启动后台任务
        background_tasks.add_task(load_and_analyze)
        
        return {
            "file_id": file_id,
            "filename": filename,
            "status": "processing",
            "message": "文件已上传，正在处理中"
        }
        
    except Exception as e:
        logger.error(f"上传文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"上传文件失败: {str(e)}")

# 获取数据处理状态
@router.get("/data/{file_id}/status", summary="获取数据处理状态")
async def get_processing_status(
    file_id: str,
    current_user: User = Depends(get_current_user),
):
    """
    获取上传的数据文件处理状态
    """
    if file_id not in _temp_files:
        raise HTTPException(status_code=404, detail="文件不存在或已过期")
    
    file_info = _temp_files[file_id]
    
    # 检查是否是当前用户的文件
    if "user_id" in file_info and file_info["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此文件")
    
    if "error" in file_info:
        return {
            "status": "error",
            "error": file_info["error"]
        }
    
    if "dataframe" not in file_info:
        return {
            "status": "processing",
            "message": "数据仍在处理中"
        }
    
    # 返回处理完成和基本统计信息
    return {
        "status": "completed",
        "filename": file_info["filename"],
        "statistics": file_info["stats"]
    }

# 处理数据
@router.post("/data/{file_id}/process", summary="处理数据")
async def process_data(
    file_id: str,
    request: DataProcessRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
):
    """
    处理上传的数据文件
    
    - 支持多种操作：过滤、排序、聚合、转换等
    - 返回处理后的数据或统计信息
    """
    if file_id not in _temp_files:
        raise HTTPException(status_code=404, detail="文件不存在或已过期")
    
    file_info = _temp_files[file_id]
    
    # 检查是否是当前用户的文件
    if "user_id" in file_info and file_info["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此文件")
    
    if "error" in file_info:
        raise HTTPException(status_code=400, detail=f"无法处理此文件: {file_info['error']}")
    
    if "dataframe" not in file_info:
        raise HTTPException(status_code=400, detail="数据仍在处理中，请稍后再试")
    
    # 获取数据框
    df = file_info["dataframe"]
    
    # 转换操作为列表
    operations = []
    for op in request.operations:
        operations.append({
            "type": op.type,
            "params": op.params
        })
    
    # 生成任务ID
    import uuid
    task_id = str(uuid.uuid4())
    
    # 在后台处理数据
    async def process_data_task():
        try:
            # 处理数据
            result_df = await data_processor.process_data(df, operations)
            
            # 根据输出格式处理结果
            if request.output_format == "stats":
                # 返回统计信息
                result = await data_processor.get_summary_stats(result_df)
                _temp_files[task_id] = {
                    "status": "completed",
                    "result": result,
                    "result_type": "stats",
                    "user_id": current_user.id
                }
            elif request.output_format == "schema":
                # 返回数据结构
                result = await data_processor.get_schema(result_df)
                _temp_files[task_id] = {
                    "status": "completed",
                    "result": result,
                    "result_type": "schema",
                    "user_id": current_user.id
                }
            else:
                # 返回数据（限制行数，避免返回过大的数据）
                if isinstance(result_df, pl.LazyFrame):
                    # 如果是懒加载模式，只收集前1000行
                    preview_df = result_df.limit(1000).collect()
                else:
                    # 如果是急切模式，也只取前1000行
                    preview_df = result_df.head(1000)
                
                # 将结果转换为JSON兼容格式
                result = {
                    "data": preview_df.to_dicts(),
                    "total_rows": len(preview_df),
                    "limited": True  # 标记已限制行数
                }
                
                # 保存处理后的数据框以供下载
                _temp_files[task_id] = {
                    "status": "completed",
                    "result": result,
                    "result_type": "data",
                    "full_result": result_df,  # 保存完整结果用于导出
                    "user_id": current_user.id
                }
            
        except Exception as e:
            logger.error(f"处理数据失败: {str(e)}")
            _temp_files[task_id] = {
                "status": "error",
                "error": str(e),
                "user_id": current_user.id
            }
    
    # 初始化任务状态
    _temp_files[task_id] = {
        "status": "processing",
        "user_id": current_user.id
    }
    
    # 启动后台任务
    background_tasks.add_task(process_data_task)
    
    return {
        "task_id": task_id,
        "status": "processing",
        "message": "数据处理已开始"
    }

# 获取处理结果
@router.get("/data/tasks/{task_id}", summary="获取处理结果")
async def get_processing_result(
    task_id: str,
    current_user: User = Depends(get_current_user),
):
    """
    获取数据处理任务的结果
    """
    if task_id not in _temp_files:
        raise HTTPException(status_code=404, detail="任务不存在或已过期")
    
    task_info = _temp_files[task_id]
    
    # 检查权限
    if "user_id" in task_info and task_info["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此任务")
    
    status = task_info.get("status", "unknown")
    
    if status == "error":
        return {
            "status": "error",
            "error": task_info.get("error", "未知错误")
        }
    
    if status == "processing":
        return {
            "status": "processing",
            "message": "数据仍在处理中"
        }
    
    # 返回处理结果
    return {
        "status": "completed",
        "result_type": task_info.get("result_type", "unknown"),
        "result": task_info.get("result", {})
    }

# 导出数据
@router.get("/data/tasks/{task_id}/export", summary="导出处理结果")
async def export_data(
    task_id: str,
    format: str = Query("csv", description="导出格式: csv, json, parquet, excel"),
    current_user: User = Depends(get_current_user),
):
    """
    导出数据处理结果为文件
    
    - 支持多种格式: CSV, JSON, Parquet, Excel
    - 返回下载链接
    """
    if task_id not in _temp_files:
        raise HTTPException(status_code=404, detail="任务不存在或已过期")
    
    task_info = _temp_files[task_id]
    
    # 检查权限
    if "user_id" in task_info and task_info["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此任务")
    
    if task_info.get("status") != "completed":
        raise HTTPException(status_code=400, detail="任务尚未完成，无法导出")
    
    if "full_result" not in task_info:
        raise HTTPException(status_code=400, detail="此任务结果不支持导出为文件")
    
    # 获取数据框
    df = task_info["full_result"]
    
    # 创建临时文件
    file_ext = f".{format}"
    if format == "excel":
        file_ext = ".xlsx"
    
    # 生成临时文件名
    output_file = await DataProcessor.generate_temp_filename(prefix="export", extension=file_ext)
    
    try:
        # 保存数据
        await data_processor.save_data(df, output_file, file_type=format)
        
        # 设置下载文件名
        filename = f"data_export_{task_id[:8]}{file_ext}"
        
        # 返回文件下载
        return FileResponse(
            path=output_file,
            filename=filename,
            media_type="application/octet-stream"
        )
    except Exception as e:
        logger.error(f"导出数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出数据失败: {str(e)}")

# 清理临时文件
async def _cleanup_temp_file(file_id: str, hours: int = 1):
    """
    定时清理临时文件
    
    Args:
        file_id: 文件ID
        hours: 保存时间（小时）
    """
    import asyncio
    
    # 等待指定时间
    await asyncio.sleep(hours * 3600)
    
    # 清理文件
    if file_id in _temp_files:
        file_info = _temp_files[file_id]
        
        # 删除物理文件
        if "file_path" in file_info:
            try:
                os.remove(file_info["file_path"])
            except:
                pass
        
        # 从内存中删除
        del _temp_files[file_id] 
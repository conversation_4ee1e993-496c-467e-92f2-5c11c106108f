from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Response, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.session import get_db
from models.audit_log import AuditLog
from schemas.audit_log import AuditLogResponse, AuditLogListResponse, AuditLogFilter, AuditLogStats
from crud.crud_audit_log import get_audit_logs, get_audit_logs_count, get_audit_log_stats
from auth.dependencies import has_permission

router = APIRouter()

@router.get("/", response_model=AuditLogListResponse, summary="获取审计日志列表")
async def read_audit_logs(
    request: Request,
    user_id: Optional[int] = Query(None, description="用户ID过滤"),
    username: Optional[str] = Query(None, description="用户名过滤"),
    action: Optional[str] = Query(None, description="操作类型过滤"),
    resource: Optional[str] = Query(None, description="资源类型过滤"),
    resource_id: Optional[str] = Query(None, description="资源ID过滤"),
    success: Optional[bool] = Query(None, description="是否成功"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    ip_address: Optional[str] = Query(None, description="IP地址过滤"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数限制"),
    current_user = Depends(has_permission("audit:read")),
    db: AsyncSession = Depends(get_db),
):
    """
    获取审计日志列表，支持多种过滤条件
    """
    # 获取符合条件的日志总数
    total = await get_audit_logs_count(
        db=db,
        user_id=user_id,
        username=username,
        action=action,
        resource=resource,
        resource_id=resource_id,
        success=success,
        start_date=start_date,
        end_date=end_date,
        ip_address=ip_address,
    )
    
    # 获取分页数据
    items = await get_audit_logs(
        db=db,
        user_id=user_id,
        username=username,
        action=action,
        resource=resource,
        resource_id=resource_id,
        success=success,
        start_date=start_date,
        end_date=end_date,
        ip_address=ip_address,
        skip=skip,
        limit=limit,
    )
    
    return {
        "total": total,
        "items": items
    }

@router.get("/stats", response_model=AuditLogStats, summary="获取审计日志统计信息")
async def get_stats(
    request: Request,
    days: int = Query(7, ge=1, le=30, description="统计的天数"),
    current_user = Depends(has_permission("audit:read")),
    db: AsyncSession = Depends(get_db),
):
    """
    获取审计日志统计信息，包括总数、按操作类型、资源类型和成功状态的统计，以及每日操作数量
    """
    stats = await get_audit_log_stats(db=db, days=days)
    return stats

@router.get("/export", summary="导出审计日志")
async def export_audit_logs(
    request: Request,
    format: str = Query("csv", description="导出格式：csv 或 json"),
    user_id: Optional[int] = Query(None, description="用户ID过滤"),
    username: Optional[str] = Query(None, description="用户名过滤"),
    action: Optional[str] = Query(None, description="操作类型过滤"),
    resource: Optional[str] = Query(None, description="资源类型过滤"),
    resource_id: Optional[str] = Query(None, description="资源ID过滤"),
    success: Optional[bool] = Query(None, description="是否成功"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    ip_address: Optional[str] = Query(None, description="IP地址过滤"),
    limit: int = Query(10000, ge=1, le=100000, description="最大导出记录数"),
    current_user = Depends(has_permission("audit:export")),
    db: AsyncSession = Depends(get_db),
):
    """
    导出审计日志，支持CSV和JSON格式
    """
    # 获取日志数据
    logs = await get_audit_logs(
        db=db,
        user_id=user_id,
        username=username,
        action=action,
        resource=resource,
        resource_id=resource_id,
        success=success,
        start_date=start_date,
        end_date=end_date,
        ip_address=ip_address,
        skip=0,
        limit=limit,
    )
    
    # 根据格式导出
    if format.lower() == "csv":
        import csv
        import io
        
        # 创建内存文件
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        writer.writerow([
            "ID", "用户ID", "用户名", "操作", "资源类型", "资源ID", "HTTP方法", 
            "路径", "状态码", "IP地址", "执行时间(秒)", "成功", "时间"
        ])
        
        # 写入数据
        for log in logs:
            writer.writerow([
                log.id, log.user_id, log.username, log.action, log.resource, 
                log.resource_id, log.method, log.path, log.response_code, 
                log.ip_address, log.execution_time, log.success, log.created_at
            ])
        
        # 构建响应
        content = output.getvalue()
        output.close()
        
        response = Response(content=content, media_type="text/csv")
        response.headers["Content-Disposition"] = f"attachment; filename=audit_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return response
        
    elif format.lower() == "json":
        import json
        from datetime import datetime
        
        # 自定义JSON序列化器
        class DateTimeEncoder(json.JSONEncoder):
            def default(self, obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                return super().default(obj)
        
        # 转换为可序列化的字典
        log_data = []
        for log in logs:
            log_dict = {c.name: getattr(log, c.name) for c in log.__table__.columns}
            log_data.append(log_dict)
        
        # 构建响应
        content = json.dumps(log_data, cls=DateTimeEncoder, ensure_ascii=False)
        
        response = Response(content=content, media_type="application/json")
        response.headers["Content-Disposition"] = f"attachment; filename=audit_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        return response
    
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的导出格式: {format}，可用格式: csv, json"
        )

@router.delete("/clear", summary="清理审计日志")
async def clear_audit_logs(
    request: Request,
    days: int = Query(90, ge=30, description="保留多少天内的日志，超过这个天数的将被删除"),
    current_user = Depends(has_permission("audit:delete")),
    db: AsyncSession = Depends(get_db),
):
    """
    清理指定天数之前的审计日志，默认清理90天前的日志
    """
    # 计算截止日期
    cutoff_date = datetime.now() - timedelta(days=days)
    
    # 删除旧日志
    from sqlalchemy import delete
    result = await db.execute(
        delete(AuditLog).where(AuditLog.created_at < cutoff_date)
    )
    
    # 提交事务
    await db.commit()
    
    # 获取删除的记录数
    deleted_count = result.rowcount
    
    return {
        "success": True,
        "deleted_count": deleted_count,
        "message": f"已删除 {deleted_count} 条 {days} 天前的审计日志记录"
    } 
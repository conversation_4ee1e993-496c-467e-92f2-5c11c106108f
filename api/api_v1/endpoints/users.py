"""
用户管理API端点
"""
from typing import Any, List, Optional
import logging
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from auth.users import User  # 保留User导入
from api.deps import get_current_user as current_active_user  # 使用新的认证依赖项
from api.deps import get_current_superuser as current_superuser  # 使用新的认证依赖项
from auth.dependencies import has_permission
from db.session import get_db
from crud.crud_user import user
from schemas.user import User as UserSchema, UserCreate, UserUpdate
from schemas.response import ResponseModel, PageResponseModel, PageInfo, success_response, page_response
from core.exceptions import ValidationException, NotFoundException, DuplicateException
from services.storage_factory import StorageFactory

router = APIRouter()
logger = logging.getLogger(__name__)

async def process_user_avatar(user_obj: dict, db: AsyncSession) -> dict:
    """处理用户头像，将文件ID转换为预签名URL"""
    if not user_obj.get("avatar"):
        return user_obj
    
    try:
        # 如果头像已经是URL格式，则不处理
        if user_obj["avatar"].startswith('http://') or user_obj["avatar"].startswith('https://'):
            return user_obj
        
        # 获取存储服务
        storage_service = StorageFactory.get_storage_service()
        
        # 尝试将avatar作为文件ID处理
        file_id = user_obj["avatar"]
        # 获取文件信息
        file_info = await storage_service.get_file_info(file_id, db=db)
        if file_info and "file_url" in file_info:
            user_obj["avatar"] = file_info["file_url"]
    except Exception as e:
        logger.warning(f"处理用户头像失败: {str(e)}")
    
    return user_obj

@router.get("/me", response_model=ResponseModel[UserSchema])
async def read_user_me(
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """获取当前用户信息"""
    # 转换用户对象为字典
    user_dict = {
        "id": current_user.id,
        "email": current_user.email,
        "username": current_user.username,
        "full_name": current_user.full_name,
        "phone": current_user.phone,
        "avatar": current_user.avatar,
        "is_active": current_user.is_active,
        "is_superuser": current_user.is_superuser,
        "is_verified": current_user.is_verified,
        "last_login": current_user.last_login,
        "created_at": current_user.created_at,
        "updated_at": current_user.updated_at,
        "roles": current_user.roles
    }
    
    # 处理头像
    user_dict = await process_user_avatar(user_dict, db)
    
    return success_response(
        data=user_dict,
        msg="获取用户信息成功"
    )

@router.patch("/me", response_model=ResponseModel[UserSchema])
async def update_user_me(
    user_in: UserUpdate,
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_db),
) -> Any:
    """更新当前用户信息"""
    # 普通用户不能设置自己为超级用户
    if user_in.is_superuser is not None and user_in.is_superuser != current_user.is_superuser:
        if not current_user.is_superuser:
            raise ValidationException("无权更改超级管理员状态")

    # 更新用户信息
    updated_user = await user.update(db, db_obj=current_user, obj_in=user_in)
    
    # 转换用户对象为字典
    user_dict = {
        "id": updated_user.id,
        "email": updated_user.email,
        "username": updated_user.username,
        "full_name": updated_user.full_name,
        "phone": updated_user.phone,
        "avatar": updated_user.avatar,
        "is_active": updated_user.is_active,
        "is_superuser": updated_user.is_superuser,
        "is_verified": updated_user.is_verified,
        "last_login": updated_user.last_login,
        "created_at": updated_user.created_at,
        "updated_at": updated_user.updated_at,
        "roles": updated_user.roles
    }
    
    # 处理头像
    user_dict = await process_user_avatar(user_dict, db)
    
    return success_response(
        data=user_dict,
        msg="更新用户信息成功"
    )

@router.get("", response_model=PageResponseModel[UserSchema])
async def read_users(
    db: AsyncSession = Depends(get_db),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页条数"),
    skip: int = Query(0, ge=0, description="跳过条数"),
    limit: int = Query(100, ge=1, description="返回条数"),
    sort_by: Optional[str] = Query(None, description="排序字段"),
    sort_desc: bool = Query(False, description="是否降序"),
    _: User = Depends(has_permission("user:read")),
) -> Any:
    """获取用户列表（分页）"""
    # 计算总数
    total = await user.count(db)
    
    # 计算实际的skip
    if page > 1:
        actual_skip = (page - 1) * page_size
    else:
        actual_skip = skip
    
    # 计算实际的limit
    actual_limit = page_size if page > 0 else limit
    
    # 获取用户列表
    users_list = await user.get_multi_with_roles(
        db, 
        skip=actual_skip, 
        limit=actual_limit,
    )
    
    # 处理所有用户的头像
    processed_users = []
    for u in users_list:
        user_dict = {
            "id": u.id,
            "email": u.email,
            "username": u.username,
            "full_name": u.full_name,
            "phone": u.phone,
            "avatar": u.avatar,
            "is_active": u.is_active,
            "is_superuser": u.is_superuser,
            "is_verified": u.is_verified,
            "last_login": u.last_login,
            "created_at": u.created_at,
            "updated_at": u.updated_at,
            "roles": u.roles
        }
        user_dict = await process_user_avatar(user_dict, db)
        processed_users.append(user_dict)
    
    # 返回分页响应
    return page_response(
        data=processed_users,
        page=page,
        size=page_size,
        total=total,
        msg="获取用户列表成功"
    )

@router.post("", response_model=ResponseModel[UserSchema])
async def create_user(
    user_in: UserCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(has_permission("user:create")),
) -> Any:
    """创建用户"""
    # 检查用户名是否已存在
    db_user = await user.get_by_username(db, username=user_in.username)
    if db_user:
        raise DuplicateException("用户名已存在")
    
    # 检查邮箱是否已存在
    db_user = await user.get_by_email(db, email=user_in.email)
    if db_user:
        raise DuplicateException("邮箱已存在")
    
    # 检查手机号是否已存在
    if user_in.phone:
        db_user = await user.get_by_phone(db, phone=user_in.phone)
        if db_user:
            raise DuplicateException("手机号已存在")
    
    # 创建用户，默认分配普通用户角色
    db_user = await user.create(db, obj_in=user_in, roles=["user"])
    
    # 处理头像
    user_dict = {
        "id": db_user.id,
        "email": db_user.email,
        "username": db_user.username,
        "full_name": db_user.full_name,
        "phone": db_user.phone,
        "avatar": db_user.avatar,
        "is_active": db_user.is_active,
        "is_superuser": db_user.is_superuser,
        "is_verified": db_user.is_verified,
        "last_login": db_user.last_login,
        "created_at": db_user.created_at,
        "updated_at": db_user.updated_at,
        "roles": db_user.roles
    }
    user_dict = await process_user_avatar(user_dict, db)
    
    # 返回成功响应
    return success_response(
        data=user_dict,
        msg="创建用户成功"
    )

@router.get("/{user_id}", response_model=ResponseModel[UserSchema])
async def read_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(has_permission("user:read")),
) -> Any:
    """获取用户详情"""
    db_user = await user.get_by_id_with_roles(db, user_id)
    if not db_user:
        raise NotFoundException("用户不存在")
    
    # 处理头像
    user_dict = {
        "id": db_user.id,
        "email": db_user.email,
        "username": db_user.username,
        "full_name": db_user.full_name,
        "phone": db_user.phone,
        "avatar": db_user.avatar,
        "is_active": db_user.is_active,
        "is_superuser": db_user.is_superuser,
        "is_verified": db_user.is_verified,
        "last_login": db_user.last_login,
        "created_at": db_user.created_at,
        "updated_at": db_user.updated_at,
        "roles": db_user.roles
    }
    user_dict = await process_user_avatar(user_dict, db)
    
    # 返回成功响应
    return success_response(
        data=user_dict,
        msg="获取用户详情成功"
    )

@router.put("/{user_id}", response_model=ResponseModel[UserSchema])
async def update_user(
    user_id: int,
    user_in: UserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(has_permission("user:update")),
) -> Any:
    """更新用户"""
    db_user = await user.get(db, id=user_id)
    if not db_user:
        raise NotFoundException("用户不存在")
    
    # 检查用户名是否重复
    if user_in.username and user_in.username != db_user.username:
        exists = await user.get_by_username(db, username=user_in.username)
        if exists:
            raise DuplicateException("用户名已存在")
    
    # 检查邮箱是否重复
    if user_in.email and user_in.email != db_user.email:
        exists = await user.get_by_email(db, email=user_in.email)
        if exists:
            raise DuplicateException("邮箱已存在")
    
    # 检查手机号是否重复
    if user_in.phone and user_in.phone != db_user.phone:
        exists = await user.get_by_phone(db, phone=user_in.phone)
        if exists:
            raise DuplicateException("手机号已存在")
    
    # 更新用户
    updated_user = await user.update(db, db_obj=db_user, obj_in=user_in)
    
    # 处理头像
    user_dict = {
        "id": updated_user.id,
        "email": updated_user.email,
        "username": updated_user.username,
        "full_name": updated_user.full_name,
        "phone": updated_user.phone,
        "avatar": updated_user.avatar,
        "is_active": updated_user.is_active,
        "is_superuser": updated_user.is_superuser,
        "is_verified": updated_user.is_verified,
        "last_login": updated_user.last_login,
        "created_at": updated_user.created_at,
        "updated_at": updated_user.updated_at,
        "roles": updated_user.roles
    }
    user_dict = await process_user_avatar(user_dict, db)
    
    # 返回成功响应
    return success_response(
        data=user_dict,
        msg="更新用户成功"
    )

@router.delete("/{user_id}", response_model=ResponseModel)
async def delete_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(has_permission("user:delete")),
) -> Any:
    """删除用户"""
    # 不能删除自己
    if current_user.id == user_id:
        raise ValidationException("不能删除当前登录的用户")
    
    db_user = await user.get(db, id=user_id)
    if not db_user:
        raise NotFoundException("用户不存在")
    
    # 不能删除超级管理员（除非当前用户也是超级管理员）
    if db_user.is_superuser and not current_user.is_superuser:
        raise ValidationException("不能删除超级管理员")
    
    # 删除用户
    await user.remove(db, id=user_id)
    
    # 返回成功响应
    return success_response(
        msg="删除用户成功"
    )

@router.put("/{user_id}/roles", response_model=ResponseModel[UserSchema])
async def update_user_roles(
    user_id: int,
    roles: List[str],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(has_permission(["user:update", "role:update"])),
) -> Any:
    """更新用户角色"""
    db_user = await user.get(db, id=user_id)
    if not db_user:
        raise NotFoundException("用户不存在")
    
    # 更新用户角色
    updated_user = await user.update(db, db_obj=db_user, obj_in={}, update_roles=roles)
    
    # 处理头像
    user_dict = {
        "id": updated_user.id,
        "email": updated_user.email,
        "username": updated_user.username,
        "full_name": updated_user.full_name,
        "phone": updated_user.phone,
        "avatar": updated_user.avatar,
        "is_active": updated_user.is_active,
        "is_superuser": updated_user.is_superuser,
        "is_verified": updated_user.is_verified,
        "last_login": updated_user.last_login,
        "created_at": updated_user.created_at,
        "updated_at": updated_user.updated_at,
        "roles": updated_user.roles
    }
    user_dict = await process_user_avatar(user_dict, db)
    
    # 返回成功响应
    return success_response(
        data=user_dict,
        msg="更新用户角色成功"
    ) 
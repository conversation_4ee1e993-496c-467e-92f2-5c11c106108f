from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from api.deps import get_current_superuser, get_current_active_user
from auth.users import User
from crud.crud_role import role
from db.session import get_db
from schemas.role import Role, RoleCreate, RoleUpdate
from schemas.response import ResponseModel, PageResponseModel

router = APIRouter()


@router.get("/", response_model=PageResponseModel[Role])
async def read_roles(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取角色列表
    """
    roles = await role.get_multi_with_permissions(db, skip=skip, limit=limit)
    total = await role.count(db)
    
    return PageResponseModel(
        data=roles,
        page={
            "page": skip // limit + 1,
            "size": limit,
            "total": total,
            "pages": (total + limit - 1) // limit,
            "has_next": skip + limit < total,
            "has_prev": skip > 0,
        },
    )


@router.post("/", response_model=ResponseModel[Role])
async def create_role(
    *,
    db: AsyncSession = Depends(get_db),
    role_in: RoleCreate,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    创建新角色
    """
    # 检查同名角色是否已存在
    db_role = await role.get_by_name(db, name=role_in.name)
    if db_role:
        raise HTTPException(
            status_code=400,
            detail="同名角色已存在",
        )
    
    # 创建角色
    created_role = await role.create_with_permissions(
        db=db, 
        obj_in=role_in, 
        permissions=role_in.permissions
    )
    
    return ResponseModel(data=created_role)


@router.get("/{id}", response_model=ResponseModel[Role])
async def read_role(
    *,
    db: AsyncSession = Depends(get_db),
    id: int,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取角色详情
    """
    db_role = await role.get_with_permissions(db, id=id)
    if not db_role:
        raise HTTPException(
            status_code=404,
            detail="角色不存在",
        )
    
    return ResponseModel(data=db_role)


@router.put("/{id}", response_model=ResponseModel[Role])
async def update_role(
    *,
    db: AsyncSession = Depends(get_db),
    id: int,
    role_in: RoleUpdate,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    更新角色
    """
    db_role = await role.get(db, id=id)
    if not db_role:
        raise HTTPException(
            status_code=404,
            detail="角色不存在",
        )
    
    # 检查是否尝试更新为同名角色
    if role_in.name and role_in.name != db_role.name:
        existing_role = await role.get_by_name(db, name=role_in.name)
        if existing_role:
            raise HTTPException(
                status_code=400,
                detail="同名角色已存在",
            )
    
    # 更新角色
    updated_role = await role.update_with_permissions(
        db=db, 
        db_obj=db_role, 
        obj_in=role_in,
        permissions=role_in.permissions if hasattr(role_in, "permissions") else None
    )
    
    return ResponseModel(data=updated_role)


@router.delete("/{id}", response_model=ResponseModel)
async def delete_role(
    *,
    db: AsyncSession = Depends(get_db),
    id: int,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    删除角色
    """
    db_role = await role.get(db, id=id)
    if not db_role:
        raise HTTPException(
            status_code=404,
            detail="角色不存在",
        )
    
    # 检查是否有用户关联该角色
    if db_role.users and len(db_role.users) > 0:
        raise HTTPException(
            status_code=400,
            detail="该角色已关联用户，无法删除",
        )
    
    # 删除角色
    await role.remove(db, id=id)
    
    return ResponseModel(msg="角色删除成功")


@router.get("/user/{user_id}", response_model=ResponseModel[List[Role]])
async def read_user_roles(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取用户的角色列表
    """
    # 检查访问权限（只能查看自己的或者超级管理员权限）
    if user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="权限不足",
        )
    
    # 获取用户角色
    user_roles = await role.get_user_roles(db, user_id=user_id)
    
    return ResponseModel(data=user_roles) 
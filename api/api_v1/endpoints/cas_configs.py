#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CAS服务配置API端点
"""
from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from api.deps import get_current_superuser 
from crud.crud_cas_config import cas_config_crud
from db.session import get_db
from schemas.cas_config import CASConfig as CASConfigSchema, CASConfigCreate, CASConfigUpdate
from schemas.response import ResponseModel, PageResponseModel
from utils.auth_config_manager import auth_config_manager

router = APIRouter()

@router.post("/", response_model=ResponseModel[CASConfigSchema], dependencies=[Depends(get_current_superuser)])
async def create_cas_config(
    *, db: AsyncSession = Depends(get_db), config_in: CASConfigCreate, background_tasks: BackgroundTasks
) -> Any:
    """创建新的CAS服务配置。仅允许一个激活的配置。"""
    existing_config = await cas_config_crud.get_by_config_name(db, config_name=config_in.config_name)
    if existing_config:
        raise HTTPException(status_code=400, detail=f"名为 '{config_in.config_name}' 的CAS配置已存在。")
    
    if config_in.is_active:
        active_config = await cas_config_crud.get_active_config(db)
        if active_config:
            raise HTTPException(status_code=400, detail="系统中已存在一个激活的CAS配置。请先禁用它。")
            
    cas_config = await cas_config_crud.create(db, obj_in=config_in)
    
    # 在创建后自动刷新认证配置 (异步处理)
    background_tasks.add_task(auth_config_manager.refresh_all_configs, db)
    
    return ResponseModel(data=cas_config, msg="CAS配置创建成功")

@router.get("/", response_model=PageResponseModel[CASConfigSchema], dependencies=[Depends(get_current_superuser)])
async def read_cas_configs(
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0), limit: int = Query(100, ge=1)
) -> Any:
    """获取CAS服务配置列表（分页）。"""
    configs = await cas_config_crud.get_multi(db, skip=skip, limit=limit)
    total = await cas_config_crud.count(db)
    return PageResponseModel(data=configs, page={"page": skip // limit + 1, "size": limit, "total": total})

@router.get("/active", response_model=ResponseModel[CASConfigSchema])
async def read_active_cas_config(db: AsyncSession = Depends(get_db)) -> Any:
    """获取当前激活的CAS服务配置。"""
    active_config = await cas_config_crud.get_active_config(db)
    if not active_config:
        # 对于前端来说，如果没配置，可能需要返回特定的空状态或错误，以便引导配置
        # 或者前端直接判断此接口是否有数据来决定是否显示CAS登录按钮
        raise HTTPException(status_code=404, detail="未找到激活的CAS配置。CAS登录不可用。")
    return ResponseModel(data=active_config)

@router.get("/{config_id}", response_model=ResponseModel[CASConfigSchema], dependencies=[Depends(get_current_superuser)])
async def read_cas_config(config_id: int, db: AsyncSession = Depends(get_db)) -> Any:
    """通过ID获取CAS服务配置。"""
    config = await cas_config_crud.get(db, id=config_id)
    if not config:
        raise HTTPException(status_code=404, detail="CAS配置未找到")
    return ResponseModel(data=config)

@router.put("/{config_id}", response_model=ResponseModel[CASConfigSchema], dependencies=[Depends(get_current_superuser)])
async def update_cas_config(
    *,
    db: AsyncSession = Depends(get_db),
    config_id: int,
    config_in: CASConfigUpdate,
    background_tasks: BackgroundTasks,
) -> Any:
    """更新CAS服务配置。"""
    config = await cas_config_crud.get(db, id=config_id)
    if not config:
        raise HTTPException(status_code=404, detail="CAS配置未找到")

    if config_in.is_active and config_in.is_active != config.is_active:
        active_config = await cas_config_crud.get_active_config(db)
        if active_config and active_config.id != config_id:
            raise HTTPException(status_code=400, detail="系统中已存在另一个激活的CAS配置。请先禁用它。")
            
    updated_config = await cas_config_crud.update(db, db_obj=config, obj_in=config_in)
    
    # 在更新后自动刷新认证配置 (异步处理)
    background_tasks.add_task(auth_config_manager.refresh_all_configs, db)
    
    return ResponseModel(data=updated_config, msg="CAS配置更新成功")

@router.delete("/{config_id}", response_model=ResponseModel, dependencies=[Depends(get_current_superuser)])
async def delete_cas_config(
    *,
    db: AsyncSession = Depends(get_db),
    config_id: int,
    background_tasks: BackgroundTasks,
) -> Any:
    """删除CAS服务配置。"""
    config = await cas_config_crud.get(db, id=config_id)
    if not config:
        raise HTTPException(status_code=404, detail="CAS配置未找到")
    await cas_config_crud.remove(db, id=config_id)
    
    # 在删除后自动刷新认证配置 (异步处理)
    background_tasks.add_task(auth_config_manager.refresh_all_configs, db)
    
    return ResponseModel(msg="CAS配置删除成功")

@router.put(
    "/{config_id}/toggle", 
    response_model=ResponseModel[CASConfigSchema],
    dependencies=[Depends(get_current_superuser)]
)
async def toggle_cas_config(
    *,
    db: AsyncSession = Depends(get_db),
    config_id: int,
    active: bool = Query(..., description="是否激活"),
    background_tasks: BackgroundTasks,
) -> Any:
    """
    快速启用或禁用指定的CAS配置。
    需要超级管理员权限。
    """
    config = await cas_config_crud.get(db, id=config_id)
    if not config:
        raise HTTPException(status_code=404, detail="CAS配置未找到")
    
    if active:
        # 如果要激活，需要先检查是否有其他激活的配置
        active_config = await cas_config_crud.get_active_config(db)
        if active_config and active_config.id != config_id:
            raise HTTPException(status_code=400, detail="系统中已存在另一个激活的CAS配置。请先禁用它。")
    
    # 只修改激活状态
    updated_config = await cas_config_crud.update(
        db, 
        db_obj=config, 
        obj_in={"is_active": active}
    )
    
    # 在状态切换后自动刷新认证配置 (异步处理)
    background_tasks.add_task(auth_config_manager.refresh_all_configs, db)
    
    status = "启用" if active else "禁用"
    return ResponseModel(data=updated_config, msg=f"CAS配置已{status}") 
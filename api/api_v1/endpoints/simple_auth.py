from fastapi import APIRouter, Depends, HTTPException, Request, Response, status
from fastapi.security import OAuth2PasswordBearer
import logging

router = APIRouter()

@router.get("/simple-user-info")
async def get_simple_user_info():
    """简化的用户信息接口"""
    # 简单的静态响应
    return {
        "data": {
            "userId": "1",
            "userName": "admin",
            "roles": ["admin"],
            "buttons": ["add", "edit", "delete"]
        },
        "code": "00000",
        "msg": "获取用户信息成功"
    } 
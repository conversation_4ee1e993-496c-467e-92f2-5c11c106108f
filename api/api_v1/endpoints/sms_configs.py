#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
短信配置管理API端点
"""
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete

from db.session import get_db
from models.sms_config import SMSConfig
from schemas.sms_config import SMSConfigCreate, SMSConfigUpdate, SMSConfigResponse
from schemas.response import ResponseModel, ResponseCode
from api.deps import get_current_superuser, get_current_active_user
from utils.aliyun_sms import send_sms_code
from auth.users import User
import logging
import urllib.parse

# 创建路由
router = APIRouter()

# 设置日志
logger = logging.getLogger(__name__)

@router.get("/", response_model=ResponseModel, tags=["短信配置"])
async def get_sms_configs(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
    config_name: Optional[str] = None,
    provider: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取短信配置列表
    """
    try:
        # 构建查询条件
        query = select(SMSConfig)
        
        # 应用过滤条件
        if is_active is not None:
            query = query.where(SMSConfig.is_active == is_active)
        if config_name:
            query = query.where(SMSConfig.config_name.ilike(f"%{config_name}%"))
        if provider:
            query = query.where(SMSConfig.provider == provider)
            
        # 应用分页
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        sms_configs = result.scalars().all()
        
        # 转换为列表
        configs_list = []
        for config in sms_configs:
            configs_list.append({
                "id": config.id,
                "config_name": config.config_name,
                "provider": config.provider,
                "sign_name": config.sign_name,
                "template_code": config.template_code,
                "auto_create_user": config.auto_create_user,
                "code_expire_minutes": config.code_expire_minutes,
                "code_length": config.code_length,
                "cooldown_seconds": config.cooldown_seconds,
                "is_active": config.is_active,
                "description": config.description,
                "created_at": config.created_at,
                "updated_at": config.updated_at,
                # 敏感信息不返回
                "access_key": "******" if config.access_key else None,
                "secret_key": "******" if config.secret_key else None,
            })
        
        return ResponseModel(
            code=ResponseCode.SUCCESS, 
            msg="获取短信配置列表成功", 
            data=configs_list
        )
    except Exception as e:
        logger.error(f"获取短信配置列表失败: {str(e)}", exc_info=True)
        return ResponseModel(
            code=ResponseCode.SYSTEM_ERROR,
            msg=f"获取短信配置列表失败: {str(e)}",
            data=None
        )

@router.get("/{config_id}", response_model=ResponseModel, tags=["短信配置"])
async def get_sms_config(
    config_id: int = Path(..., description="配置ID"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取单个短信配置详情
    """
    try:
        # 查询配置
        query = select(SMSConfig).where(SMSConfig.id == config_id)
        result = await db.execute(query)
        config = result.scalar_one_or_none()
        
        if not config:
            return ResponseModel(
                code=ResponseCode.NOT_FOUND,
                msg=f"未找到ID为{config_id}的短信配置",
                data=None
            )
        
        # 转换为响应格式
        config_data = {
            "id": config.id,
            "config_name": config.config_name,
            "provider": config.provider,
            "sign_name": config.sign_name,
            "template_code": config.template_code,
            "auto_create_user": config.auto_create_user,
            "code_expire_minutes": config.code_expire_minutes,
            "code_length": config.code_length,
            "cooldown_seconds": config.cooldown_seconds,
            "is_active": config.is_active,
            "description": config.description,
            "created_at": config.created_at,
            "updated_at": config.updated_at,
            # 敏感信息脱敏
            "access_key": "******" if config.access_key else None,
            "secret_key": "******" if config.secret_key else None,
        }
    
        return ResponseModel(
            code=ResponseCode.SUCCESS,
            msg="获取短信配置成功",
            data=config_data
        )
    except Exception as e:
        logger.error(f"获取短信配置详情失败: {str(e)}", exc_info=True)
        return ResponseModel(
            code=ResponseCode.SYSTEM_ERROR,
            msg=f"获取短信配置详情失败: {str(e)}",
            data=None
        )

@router.post("/", response_model=ResponseModel, tags=["短信配置"])
async def create_sms_config(
    config_data: SMSConfigCreate,
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_db),
):
    """
    创建新短信配置
    """
    try:
        # 检查名称是否已存在
        check_query = select(SMSConfig).where(SMSConfig.config_name == config_data.config_name)
        check_result = await db.execute(check_query)
        existing = check_result.scalar_one_or_none()
        if existing:
            return ResponseModel(
                code=ResponseCode.PARAM_ERROR,
                msg=f"配置名'{config_data.config_name}'已存在",
                data=None
            )
        
        # 如果设置为激活状态，需要先禁用其他配置
        if config_data.is_active:
            deactivate_stmt = (
                update(SMSConfig)
                .where(SMSConfig.id != 0)  # 匹配所有ID 
                .values(is_active=False)
            )
            await db.execute(deactivate_stmt)
        
        # 创建新配置
        new_config = SMSConfig(
            config_name=config_data.config_name,
            provider=config_data.provider,
            access_key=config_data.access_key,
            secret_key=config_data.secret_key,
            sign_name=config_data.sign_name,
            template_code=config_data.template_code,
            app_id=config_data.app_id,
            auto_create_user=config_data.auto_create_user,
            code_expire_minutes=config_data.code_expire_minutes,
            code_length=config_data.code_length,
            cooldown_seconds=config_data.cooldown_seconds,
            is_active=config_data.is_active,
            description=config_data.description,
        )
        db.add(new_config)
        await db.commit()
        await db.refresh(new_config)
        
        # 重新加载短信配置到内存
        from auth.backends.sms import load_sms_config
        await load_sms_config(db=db, config_name=config_data.config_name if config_data.is_active else None)
        
        return ResponseModel(
            code=ResponseCode.SUCCESS,
            msg="创建短信配置成功",
            data={"id": new_config.id}
        )
    except Exception as e:
        await db.rollback()
        logger.error(f"创建短信配置失败: {str(e)}", exc_info=True)
        return ResponseModel(
            code=ResponseCode.SYSTEM_ERROR,
            msg=f"创建短信配置失败: {str(e)}",
            data=None
        )

@router.put("/{config_id}", response_model=ResponseModel, tags=["短信配置"])
async def update_sms_config(
    config_id: int = Path(..., description="配置ID"),
    config_data: SMSConfigUpdate = None,
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_db),
):
    """
    更新短信配置
    """
    try:
        # 查询配置是否存在
        query = select(SMSConfig).where(SMSConfig.id == config_id)
        result = await db.execute(query)
        config = result.scalar_one_or_none()
        
        if not config:
                return ResponseModel(
                    code=ResponseCode.NOT_FOUND,
                    msg=f"未找到ID为{config_id}的短信配置",
                    data=None
                )
        
        # 如果要更新配置名称，检查是否冲突
        if config_data.config_name and config_data.config_name != config.config_name:
            check_query = select(SMSConfig).where(
                SMSConfig.config_name == config_data.config_name,
                SMSConfig.id != config_id
            )
            check_result = await db.execute(check_query)
            if check_result.scalar_one_or_none():
                return ResponseModel(
                    code=ResponseCode.PARAM_ERROR,
                    msg=f"配置名'{config_data.config_name}'已存在",
                    data=None
                )
        
        # 如果设置为激活，需要禁用其他配置
        if config_data.is_active and not config.is_active:
            deactivate_stmt = (
                update(SMSConfig)
                .where(SMSConfig.id != config_id)
                .values(is_active=False)
            )
            await db.execute(deactivate_stmt)
        
        # 更新配置 - 只更新非空值
        update_data = config_data.dict(exclude_unset=True)
        if update_data:
            stmt = (
                update(SMSConfig)
                .where(SMSConfig.id == config_id)
                .values(**update_data)
            )
            await db.execute(stmt)
            await db.commit()
    
            # 重新加载短信配置到内存
            from auth.backends.sms import load_sms_config
            await load_sms_config(db=db, config_name=config_data.config_name if config_data.is_active else None)
    
        return ResponseModel(
            code=ResponseCode.SUCCESS,
            msg="更新短信配置成功",
            data=None
        )
    except Exception as e:
        await db.rollback()
        logger.error(f"更新短信配置失败: {str(e)}", exc_info=True)
        return ResponseModel(
            code=ResponseCode.SYSTEM_ERROR,
            msg=f"更新短信配置失败: {str(e)}",
            data=None
        )

@router.delete("/{config_id}", response_model=ResponseModel, tags=["短信配置"])
async def delete_sms_config(
    config_id: int = Path(..., description="配置ID"),
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_db),
):
    """
    删除短信配置
    """
    try:
        # 查询配置是否存在
        query = select(SMSConfig).where(SMSConfig.id == config_id)
        result = await db.execute(query)
        config = result.scalar_one_or_none()
        
        if not config:
            return ResponseModel(
                code=ResponseCode.NOT_FOUND,
                msg=f"未找到ID为{config_id}的短信配置",
                data=None
            )
        
        # 检查是否为激活状态的配置
        if config.is_active:
            return ResponseModel(
                code=ResponseCode.PARAM_ERROR,
                msg="无法删除处于激活状态的配置，请先激活其他配置",
                data=None
            )
        
        # 删除配置
        stmt = delete(SMSConfig).where(SMSConfig.id == config_id)
        await db.execute(stmt)
        await db.commit()
        
        return ResponseModel(
            code=ResponseCode.SUCCESS,
            msg="删除短信配置成功",
            data=None
        )
    except Exception as e:
        await db.rollback()
        logger.error(f"删除短信配置失败: {str(e)}", exc_info=True)
        return ResponseModel(
            code=ResponseCode.SYSTEM_ERROR,
            msg=f"删除短信配置失败: {str(e)}",
            data=None
        )

@router.post("/{config_id}/activate", response_model=ResponseModel, tags=["短信配置"])
async def activate_sms_config(
    config_id: int = Path(..., description="配置ID"),
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_db),
):
    """
    激活指定短信配置
    """
    try:
        # 查询配置是否存在
        query = select(SMSConfig).where(SMSConfig.id == config_id)
        result = await db.execute(query)
        config = result.scalar_one_or_none()
        
        if not config:
            return ResponseModel(
                code=ResponseCode.NOT_FOUND,
                msg=f"未找到ID为{config_id}的短信配置",
                data=None
            )
        
        # 如果已经是激活状态，无需操作
        if config.is_active:
            return ResponseModel(
                code=ResponseCode.SUCCESS,
                msg="配置已处于激活状态",
                data=None
            )
        
        # 先将所有配置设为非激活
        deactivate_stmt = (
            update(SMSConfig)
            .where(SMSConfig.id != 0)  # 匹配所有ID
            .values(is_active=False)
        )
        await db.execute(deactivate_stmt)
        
        # 激活指定配置
        activate_stmt = (
            update(SMSConfig)
            .where(SMSConfig.id == config_id)
            .values(is_active=True)
        )
        await db.execute(activate_stmt)
        await db.commit()
        
        # 重新加载短信配置到内存
        from auth.backends.sms import load_sms_config
        await load_sms_config(db=db, config_name=config.config_name)
        
        return ResponseModel(
            code=ResponseCode.SUCCESS,
            msg="激活短信配置成功",
            data=None
        )
    except Exception as e:
        await db.rollback()
        logger.error(f"激活短信配置失败: {str(e)}", exc_info=True)
        return ResponseModel(
            code=ResponseCode.SYSTEM_ERROR,
            msg=f"激活短信配置失败: {str(e)}",
            data=None
        )

@router.post("/{config_id}/test", response_model=ResponseModel, tags=["短信配置"])
async def test_sms_config(
    config_id: int = Path(..., description="配置ID"),
    phone: str = Query(..., description="测试手机号码"),
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_db),
):
    """
    测试短信配置，发送测试短信
    """
    try:
        # 查询配置是否存在
        query = select(SMSConfig).where(SMSConfig.id == config_id)
        result = await db.execute(query)
        config = result.scalar_one_or_none()
        
        if not config:
            return ResponseModel(
                code=ResponseCode.NOT_FOUND,
                msg=f"未找到ID为{config_id}的短信配置",
                data=None
            )
        
        # 生成测试验证码
        import random
        test_code = ''.join(random.choices('0123456789', k=config.code_length or 6))
        
        # 使用配置发送测试短信
        result = await send_sms_code(
            phone_number=phone,
            code=test_code,
            template_param={"code": test_code},
            template_code=config.template_code,
            sign_name=config.sign_name,
            access_key_id=config.access_key,
            access_key_secret=config.secret_key
        )
        
        if result.get("Code") == "OK":
            return ResponseModel(
                code=ResponseCode.SUCCESS,
                msg="测试短信发送成功",
                data={
                    "request_id": result.get("RequestId"),
                    "biz_id": result.get("BizId"),
                    "code": test_code  # 仅测试时返回验证码
                }
            )
        else:
            return ResponseModel(
                code=ResponseCode.SYSTEM_ERROR,
                msg=f"测试短信发送失败: {result.get('Message')}",
                data={
                    "error_code": result.get("Code"),
                    "error_message": result.get("Message"),
                    "recommend": result.get("Recommend")
                }
            )
    except Exception as e:
        logger.error(f"测试短信配置失败: {str(e)}", exc_info=True)
        return ResponseModel(
            code=ResponseCode.SYSTEM_ERROR,
            msg=f"测试短信配置失败: {str(e)}",
            data=None
        )

@router.post("/{config_id}/fix-chinese-sign", response_model=ResponseModel, tags=["短信配置"])
async def fix_chinese_sign(
    config_id: int = Path(..., description="配置ID"),
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_db),
):
    """
    修复中文签名的编码问题，对中文签名进行URL编码并更新配置
    """
    try:
        # 查询配置是否存在
        query = select(SMSConfig).where(SMSConfig.id == config_id)
        result = await db.execute(query)
        config = result.scalar_one_or_none()
        
        if not config:
                return ResponseModel(
                    code=ResponseCode.NOT_FOUND,
                    msg=f"未找到ID为{config_id}的短信配置",
                    data=None
                )
        
        # 检查是否包含中文签名
        sign_name = config.sign_name
        contains_chinese = False
        for char in sign_name:
            if ord(char) > 127:
                contains_chinese = True
                break
                
        if not contains_chinese:
            return ResponseModel(
                code=ResponseCode.SUCCESS,
                msg="签名不包含中文字符，无需处理",
                data=None
            )
            
        # 对中文签名进行URL编码
        encoded_sign_name = urllib.parse.quote(sign_name)
        
        # 更新配置
        orig_sign = config.sign_name
        stmt = (
            update(SMSConfig)
            .where(SMSConfig.id == config_id)
            .values(sign_name=encoded_sign_name)
        )
        await db.execute(stmt)
        await db.commit()
        
        # 记录日志
        logger.info(f"已将短信配置 {config.config_name} 的签名从 '{orig_sign}' 更新为 URL编码版本 '{encoded_sign_name}'")
        
        # 重新加载短信配置到内存（如果是当前激活的配置）
        if config.is_active:
            from auth.backends.sms import load_sms_config
            await load_sms_config(db=db, config_name=config.config_name)
        
        return ResponseModel(
            code=ResponseCode.SUCCESS,
            msg=f"已将中文签名 '{orig_sign}' 转换为URL编码格式 '{encoded_sign_name}'",
            data={
                "original_sign": orig_sign,
                "encoded_sign": encoded_sign_name
            }
        )
    except Exception as e:
        await db.rollback()
        logger.error(f"修复中文签名失败: {str(e)}", exc_info=True)
        return ResponseModel(
            code=ResponseCode.SYSTEM_ERROR,
            msg=f"修复中文签名失败: {str(e)}",
            data=None
        )

@router.post("/fix-all-chinese-signs", response_model=ResponseModel, tags=["短信配置"])
async def fix_all_chinese_signs(
    current_user: User = Depends(get_current_superuser),
    db: AsyncSession = Depends(get_db),
):
    """
    批量修复所有包含中文的短信签名
    """
    try:
        # 查询所有短信配置
        query = select(SMSConfig)
        result = await db.execute(query)
        configs = result.scalars().all()
        
        # 跟踪修复的签名
        fixed_signs = []
        no_chinese_signs = []
        
        # 处理每个配置
        for config in configs:
            # 检查是否包含中文
            sign_name = config.sign_name
            contains_chinese = False
            
            if not sign_name:
                continue
                
            for char in sign_name:
                if ord(char) > 127:
                    contains_chinese = True
                    break
            
            if not contains_chinese:
                no_chinese_signs.append(sign_name)
                continue
                
            # URL编码中文签名
            orig_sign = sign_name
            encoded_sign_name = urllib.parse.quote(sign_name)
            
            # 更新配置
            stmt = (
                update(SMSConfig)
                .where(SMSConfig.id == config.id)
                .values(sign_name=encoded_sign_name)
            )
            await db.execute(stmt)
            
            # 记录变更
            fixed_signs.append({
                "id": config.id,
                "config_name": config.config_name,
                "original": orig_sign,
                "encoded": encoded_sign_name
            })
            
            # 记录日志
            logger.info(f"已将短信配置 {config.config_name} 的签名从 '{orig_sign}' 更新为 URL编码版本 '{encoded_sign_name}'")
        
        # 提交所有更改
        await db.commit()
        
        # 重新加载激活的短信配置到内存
        from auth.backends.sms import load_sms_config
        active_config_query = select(SMSConfig).where(SMSConfig.is_active == True)
        active_result = await db.execute(active_config_query)
        active_config = active_result.scalar_one_or_none()
        
        if active_config:
            await load_sms_config(db=db, config_name=active_config.config_name)
        
        # 返回结果
        return ResponseModel(
            code=ResponseCode.SUCCESS,
            msg=f"已修复 {len(fixed_signs)} 个中文签名",
            data={
                "fixed_count": len(fixed_signs),
                "no_chinese_count": len(no_chinese_signs),
                "fixed_signs": fixed_signs
            }
        )
    except Exception as e:
        await db.rollback()
        logger.error(f"批量修复中文签名失败: {str(e)}", exc_info=True)
        return ResponseModel(
            code=ResponseCode.SYSTEM_ERROR,
            msg=f"批量修复中文签名失败: {str(e)}",
            data=None
        ) 
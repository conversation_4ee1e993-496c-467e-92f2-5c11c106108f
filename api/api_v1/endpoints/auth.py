from typing import Any, List, Optional, Dict, Tuple, Union, Callable, Annotated
import random
import string
from datetime import datetime, timedelta, timezone
import asyncio
import pytz
from fastapi import APIRouter, Depends, HTTPException, Request, Response, status, Body, Path, Query, Form
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import RedirectResponse
from pydantic import EmailStr, Field, BaseModel

from auth.users import User, fastapi_users, jwt_backend, UserManager
from fastapi_users.exceptions import UserAlreadyExists, UserNotExists
from fastapi_users.router.common import ErrorCode
from api.deps import get_current_user, get_current_superuser

from schemas.auth import (
    Token, TokenRefresh, VerificationCodeRequest, SMSLogin, OAuth2Login, CASLogin
)
from schemas.response import ResponseModel, ResponseCode
from schemas.user import User as UserSchema, User<PERSON>reate, UserLogin, UserWithToken, APIResponse
from db.session import get_db
from sqlalchemy.ext.asyncio import AsyncSession
from models.user import User
from models import VerificationCode
from models.audit_log import AuditLog
from core.config import settings
from utils.aliyun_sms import send_sms_code, generate_random_code
from utils.error_handlers import sms_code_error_handler
from auth.backends.oauth import oauth_clients
from sqlalchemy import select, update
import httpx
from xml.etree import ElementTree
import logging
from core.users import get_user_manager
from utils.auth_cache import AuthCache
from services.redis_service import RedisService
from sqlalchemy.orm import selectinload
import json
import jwt
# 导入统一的异常类
from auth.exceptions import AuthError, InvalidCredentialsError, UserNotFoundError, ServiceNotEnabledError
import secrets

router = APIRouter()

# 添加日志记录器
logger = logging.getLogger(__name__)


# 添加短信验证请求模型
class SMSCodeRequest(BaseModel):
    phone: str = Field(..., description="手机号码")

# 辅助函数：处理认证异常
async def handle_auth_error(func: Callable, *args, **kwargs) -> Any:
    """
    处理认证过程中的异常
    
    Args:
        func: 要执行的函数
        args: 位置参数
        kwargs: 关键字参数
        
    Returns:
        函数执行结果
        
    Raises:
        HTTPException: 执行过程中出现异常
    """
    try:
        return await func(*args, **kwargs)
    except AuthError as e:
        # 认证相关异常
        raise HTTPException(
            status_code=e.status_code,
            detail=e.detail,
            headers={"WWW-Authenticate": "Bearer"} if e.status_code == status.HTTP_401_UNAUTHORIZED else None,
        )
    except Exception as e:
        # 其他异常
        error_detail = f"认证过程中出现错误: {str(e)}"
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_detail,
        )

# 辅助函数：生成JWT令牌
async def generate_tokens(user: User, request: Request = None, user_manager = None) -> Token:
    """
    为用户生成访问令牌和刷新令牌
    
    Args:
        user: 用户对象
        request: 请求对象，用于登录回调
        user_manager: 用户管理器，用于登录回调
        
    Returns:
        包含访问令牌和刷新令牌的Token对象
    """
    # 执行登录回调
    if user_manager and request:
        # 符合UserManager中on_after_login方法的参数要求
        await user_manager.on_after_login(user, request)
    
    # 导入令牌创建函数
    from core.security import create_access_token, create_refresh_token
    
    # 创建JWT令牌
    access_token = create_access_token(data=str(user.id))
    refresh_token = create_refresh_token(data=str(user.id))
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer"
    )

# 辅助函数：验证短信验证码
async def verify_sms_code(phone: str, code: str, db: AsyncSession) -> bool:
    """
    验证短信验证码
    
    Args:
        phone: 手机号
        code: 验证码
        db: 数据库会话
        
    Returns:
        验证码是否有效，如有效则同时将验证码标记为已使用
    """
    

    
    # 查询验证码记录
    query = (
        select(VerificationCode)
        .where(
            VerificationCode.phone == phone,
            VerificationCode.purpose == "login",
            VerificationCode.is_used == False,
            VerificationCode.expires_at > datetime.now(timezone.utc)
        )
        .order_by(VerificationCode.created_at.desc())
        .limit(1)  # 限制只返回一条记录，解决多结果问题
    )
    result = await db.execute(query)
    verification = result.scalar_one_or_none()
    
    # 验证码无效
    if not verification or verification.code != code:
        return False
    
    # 标记验证码为已使用
    verification.is_used = True
    db.add(verification)
    await db.commit()
    
    return True

def generate_random_password(length: int = 12) -> str:
    """
    生成随机密码
    
    Args:
        length: 密码长度
        
    Returns:
        随机密码
    """
    # 密码包含大小写字母和数字
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

# 辅助函数：创建或获取用户
async def create_or_get_user(
    user_manager: UserManager, 
    identifier: str, 
    identifier_type: str,
    auto_create: bool = False
) -> User:
    """
    根据标识符创建或获取用户
    
    Args:
        user_manager: 用户管理器
        identifier: 用户标识符（手机号/用户名）
        identifier_type: 标识符类型，支持 'phone' 和 'username'
        auto_create: 用户不存在时是否自动创建
        
    Returns:
        用户对象
        
    Raises:
        UserNotFoundError: 用户不存在且未配置自动创建
    """
    user = None
    
    # 根据标识符类型查询用户
    if identifier_type == 'phone':
        user = await user_manager.get_by_phone(identifier)
    elif identifier_type == 'username':
        user = await user_manager.get_by_username(identifier)
    else:
        raise ValueError(f"不支持的标识符类型: {identifier_type}")
    
    # 如果用户不存在且允许自动创建
    if user is None and auto_create:
        # 创建用户基本信息
        password = generate_random_password()
        
        # 创建用户数据字典
        user_data = {
            "password": password,
            "is_active": True,
            "is_verified": True,
        }
        
        # 根据标识类型设置属性
        if identifier_type == 'username':
            user_data["username"] = identifier
            user_data["email"] = f"{identifier}@example.com"
        elif identifier_type == 'phone':
            user_data["username"] = f"user_{identifier}"
            user_data["email"] = f"{identifier}@example.com"
            user_data["phone"] = identifier
        
        # 创建用户对象
        from models.user import UserCreate
        # 将字典转换为Pydantic模型
        user_create = UserCreate(**user_data)
        
        # 使用Pydantic模型创建用户
        try:
            user = await user_manager.create(user_create)
        except Exception as e:
            logger.error(f"创建用户失败: {str(e)}")
            raise
    
    # 检查是否获取到用户
    if user is None:
        raise UserNotFoundError("用户不存在且未配置自动创建")
    
    return user

# 辅助函数：验证CAS票据
async def validate_cas_ticket(ticket: str, service: str = None) -> str:
    """
    验证CAS票据
    
    Args:
        ticket: CAS票据
        service: 服务URL，默认使用配置的URL
        
    Returns:
        CAS认证成功返回的用户名
        
    Raises:
        ServiceNotEnabledError: CAS登录未启用
        InvalidCredentialsError: 票据验证失败
    """
    # 检查CAS是否启用
    if not settings.CAS_ENABLED:
        raise ServiceNotEnabledError("CAS登录")
    
    # 构建验证URL
    validate_url = f"{settings.CAS_SERVER_URL}/serviceValidate?ticket={ticket}&service={service or settings.CAS_SERVICE_URL}"
    
    # 发送请求验证票据
    async with httpx.AsyncClient() as client:
        response = await client.get(validate_url)
        
        if response.status_code != 200:
            raise InvalidCredentialsError("CAS票据验证失败")
        
        # 解析XML响应
        root = ElementTree.fromstring(response.text)
        namespace = "{http://www.yale.edu/tp/cas}"
        
        # 检查是否认证成功
        if root.find(f"{namespace}authenticationSuccess") is None:
            raise InvalidCredentialsError("CAS认证失败")
        
        # 获取用户名
        user_element = root.find(f"{namespace}authenticationSuccess/{namespace}user")
        if user_element is None or not user_element.text:
            raise InvalidCredentialsError("CAS返回的用户信息无效")
        
        return user_element.text

# 辅助函数：发送短信验证码
async def send_sms_verification(phone: str, purpose: str, db: AsyncSession) -> bool:
    """
    发送短信验证码并保存到数据库
    
    Args:
        phone: 手机号
        purpose: 用途
        db: 数据库会话
        
    Returns:
         是否成功发送
    """
    # 从后端模块导入短信发送函数
    from auth.backends.sms import send_sms_code as backend_send_sms, generate_random_code, sms_config
    
    # 日志上下文
    log_context = f"[短信验证码][{phone}]"
    logger.info(f"{log_context} 开始发送验证码流程")
    
    try:
        # 确保SMS配置已加载
        if not sms_config.get("provider"):
            logger.info(f"{log_context} SMS配置未加载，开始从数据库加载")
            from auth.backends.sms import load_sms_config
            await load_sms_config(db=db)
            
            # 检查配置是否成功加载
            if not sms_config.get("provider"):
                logger.error(f"{log_context} 无法加载SMS配置，发送验证码失败")
                return False
        
        # 生成随机验证码
        code = await generate_random_code(sms_config.get("code_length", 6))
        
        # 设置过期时间（UTC）
        expire_minutes = sms_config.get("code_expire_minutes", 10)
        expires_at = datetime.now(timezone.utc) + timedelta(minutes=expire_minutes)
        
        # 保存验证码到数据库
        verification = VerificationCode(
            phone=phone,
            code=code,
            purpose=purpose,
            expires_at=expires_at,
        )
        db.add(verification)
        await db.commit()
        
        # 发送短信验证码
        send_result = await backend_send_sms(phone, code, db=db)
        
        if send_result:
            logger.info(f"{log_context} 短信验证码发送成功")
            return True
        
        logger.error(f"{log_context} 短信验证码发送失败")
        return False
            
    except Exception as e:
        logger.error(f"{log_context} 发送验证码异常: {str(e)}", exc_info=True)
        try:
            await db.rollback()
        except Exception as rollback_error:
            logger.error(f"{log_context} 回滚事务失败: {str(rollback_error)}")
        return False

# 辅助函数：发送邮件验证码 (待实现完整功能)
async def send_email_verification(email: str, purpose: str, db: AsyncSession) -> str:
    """
    发送邮件验证码并保存到数据库
    
    Args:
        email: 电子邮箱
        purpose: 用途
        db: 数据库会话
        
    Returns:
        发送的验证码
    """
    # 生成6位随机验证码
    code = await generate_random_code(6)
    
    # 设置过期时间
    expires_at = datetime.now(timezone.utc) + timedelta(minutes=settings.SMS_CODE_EXPIRE_MINUTES)
    
    # 保存验证码到数据库
    verification = VerificationCode(
        code=code,
        purpose=purpose,
        email=email,
        expires_at=expires_at,
    )
    
    # TODO: 实现邮件发送功能
    # 暂时打印到控制台
    print(f"向 {email} 发送验证码: {code}")
    
    # 保存到数据库
    db.add(verification)
    await db.commit()
    
    return code

# 辅助函数：处理OAuth用户信息
async def handle_oauth_user(
    user_manager: UserManager,
    provider: str, 
    access_token: str, 
    account_id: str,
    account_email: str,
    username: str = None,
    db: AsyncSession = None
) -> User:
    """
    处理OAuth用户信息，创建或更新用户和OAuth账号
    
    Args:
        user_manager: 用户管理器
        provider: OAuth提供商
        access_token: 访问令牌
        account_id: 账号ID
        account_email: 账号邮箱
        username: 用户名
        db: 数据库会话
        
    Returns:
        用户对象
        
    Raises:
        UserNotFoundError: 用户不存在且未配置自动创建
    """
    # 使用实际数据库会话
    session = db if db else user_manager.session
    
    # 从models.auth导入OAuthAccount
    from models.auth import OAuthAccount
    
    # 查找是否已存在关联的OAuth账号
    stmt = (
        select(User)
        .join(User.oauth_accounts)
        .where(
            OAuthAccount.oauth_name == provider,
            OAuthAccount.account_id == account_id
        )
    )
    
    result = await session.execute(stmt)
    user = result.scalar_first()
        
    # 如果已存在用户，则更新OAuth账号信息
    if user:
        # 查找对应的OAuth账号，更新访问令牌
        for oauth_account in user.oauth_accounts:
            if oauth_account.oauth_name == provider and oauth_account.account_id == account_id:
                oauth_account.access_token = access_token
                oauth_account.updated_at = datetime.now(timezone.utc)
                session.add(oauth_account)
                await session.commit()
                break
        return user
        
    # 查找使用相同电子邮件的用户
    user = await user_manager.get_by_email(account_email)
    
    # 如果存在用户，则添加OAuth账号
    if user:
        oauth_account = OAuthAccount(
            user_id=user.id,
            oauth_name=provider,
            access_token=access_token,
            account_id=account_id,
            account_email=account_email,
        )
        
        user.oauth_accounts.append(oauth_account)
        session.add(user)
        await session.commit()
        return user
        
    # 如果不允许自动创建用户，抛出异常
    if not settings.OAUTH2_AUTO_CREATE_USER:
        raise UserNotFoundError("用户不存在且未配置自动创建")
    
    # 创建新用户并关联OAuth账号
    user = await user_manager.create_oauth_user(
        provider=provider,
        account_id=account_id,
        account_email=account_email,
        username=username
    )
    
    # 更新访问令牌（create_oauth_user中默认为空）
    for oauth_account in user.oauth_accounts:
        if oauth_account.oauth_name == provider and oauth_account.account_id == account_id:
            oauth_account.access_token = access_token
            session.add(oauth_account)
            await session.commit()
            break
    
    return user

@router.post("/refresh-token", response_model=APIResponse, tags=["认证"])
async def refresh_token(
    token: Annotated[str, Body(embed=True)],
    user_manager: Annotated[UserManager, Depends(get_user_manager)],
):
    """
    刷新访问令牌
    
    Args:
        token: 刷新令牌
        
    Returns:
        包含新访问令牌的响应
    """
    # 使用Redis缓存优化令牌验证
    try:
        # 导入令牌创建函数
        from core.security import decode_token
        
        # 使用RedisService获取缓存实例，而不是通过参数注入
        redis_cache = await RedisService.get_cache()
        auth_cache = AuthCache(redis_cache)
        
        # 验证令牌
        token_data = decode_token(token)
        user_id = int(token_data.get("sub"))
        
        # 获取用户
        user = await user_manager.get_user(user_id)
        if not user or not user.is_active:
            raise InvalidCredentialsError("令牌无效或已过期")
        
        # 生成新令牌
        new_token = create_access_token(data=str(user_id))
        
        return {
            "code": "00000",
            "msg": "刷新令牌成功",
            "data": {
                "token": new_token
            }
        }
    except Exception as e:
        # 捕获所有异常，避免令牌验证失败时暴露详细信息
        logger.error(f"刷新令牌失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

@router.post("/logout", response_model=APIResponse, tags=["认证"])
async def logout(
    response: Response,
    auth: Optional[str] = Body(None, embed=True),
):
    """
    退出登录
    
    Args:
        auth: 访问令牌，可选
        
    Returns:
        退出登录结果
    """
    try:
        # 使用RedisService获取缓存实例，而不是通过参数注入
        redis_cache = await RedisService.get_cache()
        auth_cache = AuthCache(redis_cache)
        
        # 如果提供了令牌，尝试使令牌缓存失效
        if auth:
            await auth_cache.invalidate_token(auth)
        
        # 删除cookie身份验证
        response.delete_cookie(
            key="fastapiusers_auth",
            domain=None if settings.DEBUG else settings.DOMAIN,
            secure=not settings.DEBUG,
            httponly=True
        )
        
        return {
            "code": "00000",
            "msg": "退出登录成功",
            "data": None
        }
    except Exception as e:
        logger.error(f"退出登录失败: {str(e)}")
        return {
            "code": "00000",
            "msg": "退出登录成功",
            "data": None
        }

@router.post("/send-verification-code", response_model=ResponseModel, tags=["认证"])
async def send_verification_code(
    request_data: VerificationCodeRequest,
    db: AsyncSession = Depends(get_db),
) -> ResponseModel:
    """发送验证码"""
    try:
        # 检查是否是手机号或邮箱
        is_email = '@' in request_data.target
        target = request_data.target
        
        # 检查格式是否正确
        if is_email:
            # 简单的邮箱格式验证
            import re
            if not re.match(r'^[\w.-]+@[\w.-]+\.\w+$', target):
                return ResponseModel(
                    code="A0151",
                    msg="邮箱格式不正确",
                    data=None
                )
        else:
            # 简单的手机号格式验证
            import re
            if not re.match(r'^1[3-9]\d{9}$', target):
                return ResponseModel(
                    code="A0151",
                    msg="手机号格式不正确",
                    data=None
                )
        
        # 检查发送频率限制
        query = (
            select(VerificationCode)
            .where(
                (VerificationCode.phone == target) if not is_email else (VerificationCode.email == target),
                VerificationCode.purpose == request_data.purpose,
                VerificationCode.created_at > datetime.now(timezone.utc) - timedelta(minutes=1)
            )
        )
        result = await db.execute(query)
        recent_code = result.scalar_one_or_none()
        
        if recent_code:
            # 计算剩余冷却时间
            from auth.backends.sms import sms_config
            cooldown_seconds = sms_config.get("cooldown_seconds", 60)
            elapsed = (datetime.now(timezone.utc) - recent_code.created_at).total_seconds()
            remaining = max(0, cooldown_seconds - elapsed)
            
            if remaining > 0:
                return ResponseModel(
                    code="A0152",
                    msg=f"发送过于频繁，请{int(remaining)}秒后再试",
                    data={"cooldown": int(remaining)}
                )
        
        # 根据目标类型调用短信或邮件发送服务
        success = False
        try:
            if is_email:
                # 发送邮件验证码
                await send_email_verification(
                    email=target,
                    purpose=request_data.purpose,
                    db=db
                )
                success = True
                logger.info(f"成功发送邮件验证码到: {target}")
            else:
                # 发送短信验证码
                await send_sms_verification(
                    phone=target,
                    purpose=request_data.purpose,
                    db=db
                )
                success = True
                logger.info(f"成功发送短信验证码到: {target}")
        except Exception as e:
            logger.error(f"发送验证码失败: {str(e)}")
            success = False
        
        # 根据发送结果返回响应
        if success:
            # 获取过期时间
            from auth.backends.sms import sms_config
            expire_minutes = sms_config.get("code_expire_minutes", 10)
            cooldown_seconds = sms_config.get("cooldown_seconds", 60)
            
            return ResponseModel(
                code="00000",
                msg="验证码已发送，请注意查收",
                data={
                    "expire_in": expire_minutes * 60,
                    "cooldown": cooldown_seconds
                }
            )
        else:
            return ResponseModel(
                code="B0001",
                msg="验证码发送失败，请稍后再试",
                data=None
            )
    except Exception as e:
        logger.error(f"发送验证码失败: {str(e)}")
        return ResponseModel(
            code="B0001",
            msg=f"发送验证码失败: {str(e)}",
            data=None
        )

@router.post("/sms/login", response_model=ResponseModel, tags=["认证"])
@sms_code_error_handler
async def sms_login(
    request: Request,
    phone: str = Form(...),
    code: str = Form(...),
    db: AsyncSession = Depends(get_db),
    user_manager: UserManager = Depends(get_user_manager)
) -> Dict[str, Any]:
    """
    短信验证码登录
    
    Args:
        request: 请求对象
        phone: 手机号
        code: 短信验证码
        db: 数据库会话
        user_manager: 用户管理器
        
    Returns:
        包含访问令牌和刷新令牌的响应
    """
    # 验证短信验证码
    if not await verify_sms_code(phone, code, db):
        raise HTTPException(
            status_code=400,
            detail="验证码无效或已过期"
        )
    
    # 查找或创建用户
    user = await create_or_get_user(
        user_manager=user_manager,
        identifier=phone,
        identifier_type='phone',
        auto_create=True  # 允许自动创建用户
    )
    
    # 生成令牌
    from core.security import create_access_token, create_refresh_token
    access_token = create_access_token(data=str(user.id))
    refresh_token = create_refresh_token(data=str(user.id))
    
    # 更新最后登录时间 - 使用UTC时间
    try:
        if hasattr(user, 'last_login'):
            user.last_login = datetime.now(timezone.utc)
            db.add(user)
            await db.commit()
    except Exception as e:
        # 记录错误但不中断流程
        print(f"更新最后登录时间失败: {str(e)}") 
    
    # 缓存令牌数据
    try:
        redis_cache = await RedisService.get_cache()
        if redis_cache:
            auth_cache = AuthCache(redis_cache)
            token_data = {"sub": str(user.id)}
            try:
                await auth_cache.set_token_data(access_token, token_data)
            except Exception as cache_err:
                logger.warning(f"缓存令牌数据失败(非致命错误): {str(cache_err)}")
    except Exception as e:
        logger.warning(f"获取Redis缓存失败(非致命错误): {str(e)}")
    
    # 执行登录后的回调
    if hasattr(user_manager, 'on_after_login'):
        await user_manager.on_after_login(user, request)
    
    # 记录登录历史
    await record_login_history(
        db=db,
        user_id=str(user.id),  # 使用已经安全获取的user_id
        login_method="sms",
        request=request,
        status=True,
        message="短信验证码登录成功"
    )
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }


async def record_login_history(
    db: AsyncSession, 
    user_id: str, 
    login_method: str,
    request: Request,
    status: bool = True,
    message: str = "登录成功"
) -> None:
    """
    记录用户登录历史到审计日志表
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        login_method: 登录方式(sms, password, oauth, cas等)
        request: FastAPI请求对象
        status: 登录状态(成功/失败)
        message: 登录消息
    """
    try:
        # 获取请求信息
        client_host = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent", "")
        query_params = dict(request.query_params) if request.query_params else {}
        
        # 创建审计日志记录
        audit_log = AuditLog(
            # 用户信息
            user_id=int(user_id) if user_id and user_id.isdigit() else None,
            
            # 操作信息
            action="login",         # 操作类型为登录
            resource="auth",        # 资源类型为认证
            resource_id=None,       # 登录操作没有特定资源ID
            
            # 请求信息
            method=request.method,
            path=request.url.path,
            query_params=query_params,
            request_body=None,     # 不记录请求体以保护敏感信息
            response_code=200 if status else 401,
            
            # IP和用户代理
            ip_address=client_host,
            user_agent=user_agent,
            
            # 详细信息
            details={
                "login_method": login_method,
                "timestamp": datetime.now(timezone.utc).isoformat()
            },
            message=message,
            
            # 成功标志
            success=status
        )
        
        # 保存到数据库
        db.add(audit_log)
        await db.commit()
    except Exception as e:
        # 记录错误但不抛出异常，不应影响登录流程
        print(f"记录登录历史失败: {str(e)}")
        try:
            await db.rollback()
        except:
            pass

@router.post("/cas/login", response_model=Token, tags=["认证"])
async def cas_login(
    login_data: CASLogin,
    request: Request,
    user_manager=Depends(fastapi_users.get_user_manager),
) -> Token:
    """CAS登录"""
    async def _cas_login():
        # 验证CAS票据
        username = await validate_cas_ticket(login_data.ticket, login_data.service)
        
        # 获取或创建用户
        user = await create_or_get_user(
            user_manager=user_manager,
            identifier=username,
            identifier_type='username',
            auto_create=settings.CAS_AUTO_CREATE_USER
        )
        
        if not user.is_active:
            raise InvalidCredentialsError("用户已被禁用")
        
        # 生成令牌 - 使用JWT策略直接写入令牌
        access_token = await jwt_backend.get_strategy().write_token(user)
        refresh_token = await jwt_backend.get_strategy().write_token(user)
        
        # 登录回调
        await user_manager.on_after_login(user, request)
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer"
        )
    
    return await handle_auth_error(_cas_login)

@router.get("/oauth/authorize/{provider}", tags=["认证"])
async def oauth_authorize(provider: str, request: Request):
    """获取OAuth授权URL"""
    async def _oauth_authorize():
        if not settings.OAUTH2_ENABLED:
            raise ServiceNotEnabledError("OAuth")
        
        if provider not in oauth_clients:
            raise AuthError(f"不支持的OAuth提供商: {provider}")
        
        # 获取redirect_uri (前端提供)
        redirect_uri = request.query_params.get("redirect_uri")
        if not redirect_uri:
            raise AuthError("缺少redirect_uri参数")
        
        # 构建授权URL
        client = oauth_clients[provider]
        authorization_url = await client.get_authorization_url(redirect_uri, state=provider)
        
        return {"authorization_url": authorization_url}
    
    return await handle_auth_error(_oauth_authorize)

@router.get("/oauth/callback/{provider}", tags=["认证"])
async def oauth_callback(
    provider: str,
    request: Request,
    user_manager=Depends(fastapi_users.get_user_manager),
    db: AsyncSession = Depends(get_db),
):
    """处理OAuth授权回调"""
    async def _oauth_callback():
        if not settings.OAUTH2_ENABLED:
            raise ServiceNotEnabledError("OAuth")
        
        if provider not in oauth_clients:
            raise AuthError(f"不支持的OAuth提供商: {provider}")
        
        # 获取code
        code = request.query_params.get("code")
        if not code:
            raise AuthError("缺少code参数")
        
        # 获取redirect_uri
        redirect_uri = request.query_params.get("redirect_uri")
        if not redirect_uri:
            raise AuthError("缺少redirect_uri参数")
        
        # 处理不同的OAuth提供商
        client = oauth_clients[provider]
        
        # 获取访问令牌
        access_token = await client.get_access_token(code, redirect_uri)
        
        # 获取用户信息
        user_info = await client.get_id_email(access_token["access_token"])
        
        # 处理用户信息
        user = await handle_oauth_user(
            user_manager=user_manager,
            provider=provider,
            access_token=access_token["access_token"],
            account_id=user_info.id,
            account_email=user_info.email,
            db=db
        )
        
        # 检查用户状态
        if not user.is_active:
            raise InvalidCredentialsError("用户已被禁用")
        
        # 生成JWT令牌 - 使用JWT策略直接写入令牌
        jwt_access_token = await jwt_backend.get_strategy().write_token(user)
        jwt_refresh_token = await jwt_backend.get_strategy().write_token(user)
        
        # 登录回调
        await user_manager.on_after_login(user, request)
        
        # 构建前端回调URL，附加令牌信息
        frontend_callback_url = f"{request.query_params.get('frontend_callback_url', settings.FRONTEND_URL)}"
        if "?" in frontend_callback_url:
            frontend_callback_url += f"&access_token={jwt_access_token}&refresh_token={jwt_refresh_token}"
        else:
            frontend_callback_url += f"?access_token={jwt_access_token}&refresh_token={jwt_refresh_token}"
        
        # 重定向到前端
        return RedirectResponse(url=frontend_callback_url)
    
    try:
        return await _oauth_callback()
    except Exception as e:
        # 构建错误回调URL
        error_url = f"{request.query_params.get('frontend_callback_url', settings.FRONTEND_URL)}"
        if "?" in error_url:
            error_url += f"&error={str(e)}"
        else:
            error_url += f"?error={str(e)}"
        
        # 重定向到前端错误页面
        return RedirectResponse(url=error_url)

@router.post("/oauth/login", response_model=Token, tags=["认证"])
async def oauth_login(
    login_data: OAuth2Login,
    request: Request,
    user_manager=Depends(fastapi_users.get_user_manager),
    db: AsyncSession = Depends(get_db),
) -> Token:
    """OAuth令牌登录（由前端直接获取令牌后调用）"""
    async def _oauth_login():
        # 检查OAuth服务是否启用
        if not settings.OAUTH2_ENABLED:
            raise ServiceNotEnabledError("OAuth")
        
        # 检查提供商是否支持
        if login_data.provider not in oauth_clients:
            raise AuthError(f"不支持的OAuth提供商: {login_data.provider}")
        
        # 获取客户端
        client = oauth_clients[login_data.provider]
        
        # 获取用户信息
        user_info = await client.get_id_email(login_data.access_token)
        
        # 处理用户信息
        user = await handle_oauth_user(
            user_manager=user_manager,
            provider=login_data.provider,
            access_token=login_data.access_token,
            account_id=user_info.id,
            account_email=user_info.email,
            db=db
        )
        
        # 检查用户状态
        if not user.is_active:
            raise InvalidCredentialsError("用户已被禁用")
        
        # 生成JWT令牌 - 使用JWT策略直接写入令牌
        access_token = await jwt_backend.get_strategy().write_token(user)
        refresh_token = await jwt_backend.get_strategy().write_token(user)
        
        # 登录回调
        await user_manager.on_after_login(user, request)
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer"
        )
    
    return await handle_auth_error(_oauth_login)

@router.get("/getUserInfo")
async def get_user_info(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取用户信息接口，按照Apifox文档格式
    
    Returns:
        统一格式的响应，包含用户ID、用户名、角色集合和按钮权限集合
    """
    try:
        # 获取用户信息
        user_id = current_user.id
        username = current_user.username
        
        # 获取用户角色
        roles = []
        if hasattr(current_user, 'roles') and current_user.roles:
            roles = [role.name for role in current_user.roles]
        
        # 获取用户按钮权限
        buttons = []
        if hasattr(current_user, 'roles') and current_user.roles:
            for role in current_user.roles:
                if hasattr(role, 'permissions') and role.permissions:
                    for perm in role.permissions:
                        if perm.type == 'button' and perm.name not in buttons:
                            buttons.append(perm.name)
        
        # 构建响应数据
        user_data = {
            "userId": str(user_id),
            "userName": username,
            "roles": roles,
            "buttons": buttons
        }
        
        # 返回符合要求的格式
        from schemas.response import ResponseCode
        return {
            "data": user_data,
            "code": ResponseCode.SUCCESS,
            "msg": "获取用户信息成功"
        }
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        from schemas.response import ResponseCode
        return {
            "data": {},
            "code": ResponseCode.SYSTEM_ERROR,
            "msg": f"获取用户信息失败: {str(e)}"
        }

@router.post("/sms/send-code", response_model=ResponseModel, tags=["认证"])
@sms_code_error_handler
async def send_sms_code(
    request_data: SMSCodeRequest,
    db: AsyncSession = Depends(get_db),
) -> Dict[str, Any]:
    """
    发送短信验证码
    
    Args:
        request_data: 包含手机号的请求数据
        db: 数据库会话
        
    Returns:
        包含发送结果的响应
    """
    # 检查是否为合法手机号
    import re
    if not re.match(r'^1[3-9]\d{9}$', request_data.phone):
        raise HTTPException(
            status_code=400,
            detail="手机号格式不正确"
        )
    
    # 检查是否在冷却时间内
    query = (
        select(VerificationCode)
        .where(
            VerificationCode.phone == request_data.phone,
            VerificationCode.purpose == "login",
            VerificationCode.created_at > datetime.now(timezone.utc) - timedelta(seconds=60)  # 1分钟冷却
        )
        .order_by(VerificationCode.created_at.desc())
        .limit(1)
    )
    result = await db.execute(query)
    recent_code = result.scalar_one_or_none()
    
    if recent_code:
        # 计算剩余冷却时间
        from auth.backends.sms import sms_config
        cooldown_seconds = sms_config.get("cooldown_seconds", 60)
        elapsed = (datetime.now(timezone.utc) - recent_code.created_at).total_seconds()
        remaining = max(0, cooldown_seconds - elapsed)
        
        if remaining > 0:
            raise HTTPException(
                status_code=429,  # Too Many Requests
                detail={
                    "msg": f"发送过于频繁，请{int(remaining)}秒后再试",
                    "cooldown": int(remaining)
                }
            )
    
    # 加载短信配置
    from auth.backends.sms import sms_config, load_sms_config
    if not sms_config.get("provider"):
        await load_sms_config(db=db)
        
        if not sms_config.get("provider"):
            raise HTTPException(
                status_code=503,  # Service Unavailable
                detail="短信服务未配置，请联系管理员"
            )
    
    # 检查凭据是否完整
    if not all([
        sms_config.get("access_key"),
        sms_config.get("secret_key"),
        sms_config.get("sign_name"),
        sms_config.get("template_code")
    ]):
        raise HTTPException(
            status_code=503,  # Service Unavailable
            detail="短信服务配置不完整，请联系管理员"
        )
    
    # 发送短信验证码
    success = await send_sms_verification(
        phone=request_data.phone,
        purpose="login",
        db=db
    )
    
    if success:
        # 获取配置的过期时间和冷却时间
        expire_minutes = sms_config.get("code_expire_minutes", 10)
        cooldown_seconds = sms_config.get("cooldown_seconds", 60)
        
        return {
            "expire_in": expire_minutes * 60,
            "cooldown": cooldown_seconds
        }
    else:
        # 获取最近创建的验证码，用于调试
        debug_query = (
            select(VerificationCode)
            .where(
                VerificationCode.phone == request_data.phone,
                VerificationCode.purpose == "login"
            )
            .order_by(VerificationCode.created_at.desc())
            .limit(1)
        )
        debug_result = await db.execute(debug_query)
        debug_code = debug_result.scalar_one_or_none()
        
        # 如果验证码已创建但发送失败，记录错误但返回成功响应
        # 这是为了避免泄露验证码信息，同时允许测试环境使用
        if debug_code:
            logger.warning(f"验证码已创建但发送失败: phone={request_data.phone}, code={debug_code.code}")
            
            if settings.DEBUG:
                # 在调试模式下仍然返回成功，但在日志中记录验证码
                logger.info(f"调试模式: 手机号 {request_data.phone} 的验证码是 {debug_code.code}")
                return {
                    "expire_in": sms_config.get("code_expire_minutes", 10) * 60,
                    "cooldown": sms_config.get("cooldown_seconds", 60),
                    "debug_code": debug_code.code if settings.DEBUG else None
                }
        
        # 正式环境返回错误
        raise HTTPException(
            status_code=500,
            detail="短信发送失败，请稍后再试"
        )

@router.get("/sms/debug-codes/{phone}", response_model=ResponseModel, tags=["工具"])
async def debug_sms_codes(
    phone: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_superuser),
) -> ResponseModel:
    """
    【仅管理员】查询手机号的验证码记录 - 仅用于调试
    """
    try:
        # 查询验证码记录
        from models.auth import VerificationCode
        
        query = (
            select(VerificationCode)
            .where(VerificationCode.phone == phone)
            .order_by(VerificationCode.created_at.desc())
            .limit(10)  # 最多返回10条记录
        )
        
        result = await db.execute(query)
        codes = result.scalars().all()
        
        if not codes:
            return ResponseModel(
                code="A0240",
                msg=f"未找到手机号 {phone} 的验证码记录",
                data=None
            )
            
        # 转换为可序列化格式
        code_list = []
        for code in codes:
            is_valid = not code.is_used and code.expires_at > datetime.now(timezone.utc)
            code_list.append({
                "code": code.code,
                "purpose": code.purpose,
                "created_at": code.created_at.isoformat(),
                "expires_at": code.expires_at.isoformat(),
                "is_used": code.is_used,
                "is_valid": is_valid
            })
            
        return ResponseModel(
            code="00000",
            msg=f"找到 {len(code_list)} 条验证码记录",
            data=code_list
        )
    except Exception as e:
        logger.error(f"查询验证码记录失败: {str(e)}")
        return ResponseModel(
            code="B0001",
            msg=f"查询验证码记录失败: {str(e)}",
            data=None
        )
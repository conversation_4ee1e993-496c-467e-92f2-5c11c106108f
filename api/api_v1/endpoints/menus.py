from typing import Any, List, Optional
import logging

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID

from api.deps import get_current_superuser, get_current_active_user, has_permissions
from auth.users import User
from db.session import get_db
from schemas.response import ResponseModel, PageResponseModel
from models.menu import Menu
from crud.base import CRUDBase
from schemas.menu import Menu as MenuSchema, MenuCreate, MenuUpdate, MenuTree
from services.storage_factory import StorageFactory

# 创建CRUD实例
menu = CRUDBase[Menu, MenuCreate, MenuUpdate](Menu)

router = APIRouter()
logger = logging.getLogger(__name__)


async def process_menu_icon(menu_obj: dict, db: AsyncSession) -> dict:
    """处理菜单图标，将文件ID转换为预签名URL"""
    if not menu_obj.get("icon"):
        return menu_obj
    
    try:
        # 如果图标已经是URL格式，则不处理
        if menu_obj["icon"].startswith('http://') or menu_obj["icon"].startswith('https://'):
            return menu_obj
        
        # 获取存储服务
        storage_service = StorageFactory.get_storage_service()
        
        # 尝试将icon作为文件ID处理
        file_id = menu_obj["icon"]
        # 获取文件信息
        file_info = await storage_service.get_file_info(file_id, db=db)
        if file_info and "file_url" in file_info:
            menu_obj["icon"] = file_info["file_url"]
    except Exception as e:
        logger.warning(f"处理菜单图标失败: {str(e)}")
    
    return menu_obj


@router.get("/", response_model=ResponseModel[List[MenuSchema]])
async def read_menus(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取菜单列表（平铺结构）
    """
    menus = await menu.get_multi(db, skip=skip, limit=limit)
    
    # 处理所有菜单的图标字段
    processed_menus = []
    for m in menus:
        menu_dict = {
            "id": m.id,
            "name": m.name,
            "path": m.path,
            "component": m.component,
            "redirect": m.redirect,
            "title": m.title,
            "icon": m.icon,
            "parent_id": m.parent_id,
            "sort_order": m.sort_order,
            "is_hidden": m.is_hidden,
            "is_cache": m.is_cache,
            "is_disabled": m.is_disabled,
            "permission": m.permission,
            "created_at": m.created_at,
            "updated_at": m.updated_at
        }
        # 处理图标
        menu_dict = await process_menu_icon(menu_dict, db)
        processed_menus.append(menu_dict)
    
    return ResponseModel(data=processed_menus)


@router.get("/tree", response_model=ResponseModel[List[MenuTree]])
async def read_menu_tree(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取菜单树形结构
    """
    # 获取所有菜单
    all_menus = await menu.get_multi(db)
    
    # 构建菜单树
    root_menus = []
    menu_map = {m.id: m for m in all_menus}
    
    for m in all_menus:
        # 转换为Pydantic模型
        menu_dict = {
            "id": m.id,
            "name": m.name,
            "path": m.path,
            "component": m.component,
            "redirect": m.redirect,
            "title": m.title,
            "icon": m.icon,
            "parent_id": m.parent_id,
            "sort_order": m.sort_order,
            "is_hidden": m.is_hidden,
            "is_cache": m.is_cache,
            "is_disabled": m.is_disabled,
            "permission": m.permission,
            "children": []
        }
        
        # 处理父子关系
        if m.parent_id is None:
            # 根菜单
            root_menus.append(menu_dict)
        elif m.parent_id in menu_map:
            # 子菜单，找到父菜单
            parent = None
            for rm in root_menus:
                if rm["id"] == m.parent_id:
                    parent = rm
                    break
            
            # 如果没找到，可能是多级菜单，需要递归查找
            if parent is None:
                def find_parent(menus, parent_id):
                    for menu_item in menus:
                        if menu_item["id"] == parent_id:
                            return menu_item
                        if menu_item["children"]:
                            found = find_parent(menu_item["children"], parent_id)
                            if found:
                                return found
                    return None
                
                parent = find_parent(root_menus, m.parent_id)
            
            # 添加到父菜单的children中
            if parent:
                parent["children"].append(menu_dict)
    
    # 按sort_order排序
    def sort_menus(menus):
        menus.sort(key=lambda x: x["sort_order"])
        for menu_item in menus:
            if menu_item["children"]:
                sort_menus(menu_item["children"])
        return menus
    
    root_menus = sort_menus(root_menus)
    
    return ResponseModel(data=root_menus)


@router.post("/", response_model=ResponseModel[MenuSchema])
async def create_menu(
    *,
    db: AsyncSession = Depends(get_db),
    menu_in: MenuCreate,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    创建新菜单
    """
    # 如果有父菜单，先检查父菜单是否存在
    if menu_in.parent_id:
        parent_menu = await menu.get(db, id=menu_in.parent_id)
        if not parent_menu:
            raise HTTPException(
                status_code=404,
                detail="父菜单不存在",
            )
    
    # 创建菜单
    created_menu = await menu.create(db=db, obj_in=menu_in)
    
    return ResponseModel(data=created_menu)


@router.get("/{id}", response_model=ResponseModel[MenuSchema])
async def read_menu(
    *,
    db: AsyncSession = Depends(get_db),
    id: int,
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """
    获取菜单详情
    """
    db_menu = await menu.get(db, id=id)
    if not db_menu:
        raise HTTPException(
            status_code=404,
            detail="菜单不存在",
        )
    
    return ResponseModel(data=db_menu)


@router.put("/{id}", response_model=ResponseModel[MenuSchema])
async def update_menu(
    *,
    db: AsyncSession = Depends(get_db),
    id: int,
    menu_in: MenuUpdate,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    更新菜单
    """
    db_menu = await menu.get(db, id=id)
    if not db_menu:
        raise HTTPException(
            status_code=404,
            detail="菜单不存在",
        )
    
    # 检查是否试图将菜单设置为自己的子菜单（避免循环引用）
    if menu_in.parent_id and menu_in.parent_id == id:
        raise HTTPException(
            status_code=400,
            detail="不能将菜单设置为自己的子菜单",
        )
    
    # 更新菜单
    updated_menu = await menu.update(db=db, db_obj=db_menu, obj_in=menu_in)
    
    return ResponseModel(data=updated_menu)


@router.delete("/{id}", response_model=ResponseModel)
async def delete_menu(
    *,
    db: AsyncSession = Depends(get_db),
    id: int,
    current_user: User = Depends(get_current_superuser),
) -> Any:
    """
    删除菜单
    """
    db_menu = await menu.get(db, id=id)
    if not db_menu:
        raise HTTPException(
            status_code=404,
            detail="菜单不存在",
        )
    
    # 检查是否有子菜单
    from sqlalchemy import select
    query = select(Menu).where(Menu.parent_id == id)
    result = await db.execute(query)
    children = result.scalars().all()
    
    if children:
        raise HTTPException(
            status_code=400,
            detail="请先删除子菜单",
        )
    
    # 删除菜单
    await menu.remove(db, id=id)
    
    return ResponseModel(msg="菜单删除成功") 
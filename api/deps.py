from typing import Annotated, List, Set, AsyncGenerator, Optional, Generator, Union

from fastapi import Depends, HTTPException, status, Request, Header, Security
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.security import O<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIKeyHeader, OAuth2PasswordRequestForm
from jose import jwt, JWTError
import logging
from sqlalchemy import select

from auth.users import current_active_user, current_superuser, User, get_user_permissions
from db.session import get_db
from core.config import settings
from core.users import optional_current_user, fastapi_users
from core.security import decode_token
from utils.auth_cache import AuthCache

logger = logging.getLogger(__name__)

# 重新导出FastAPI Users的依赖项
get_current_active_user = current_active_user
get_current_superuser = current_superuser

# 原有OAuth2认证
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="auth-settings/jwt/login",
)

# 定义API密钥头部
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)

# 获取当前用户(可选)
get_current_user_optional = fastapi_users.current_user(optional=True)

# 获取当前活跃用户
get_current_active_user = fastapi_users.current_user(active=True)

# 获取当前超级用户
get_current_superuser = fastapi_users.current_user(active=True, superuser=True)

# 自定义依赖项 - 从原始授权头获取令牌
async def get_token_from_authorization(
    authorization: Optional[str] = Header(None, description="JWT令牌，格式: Bearer <token>")
) -> Optional[str]:
    """从Authorization头获取令牌"""
    if not authorization:
        return None
    
    parts = authorization.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, 
            detail="无效的授权头",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    return parts[1]

# 从OAuth2中获取令牌 (备用方法)
async def get_token(token: str = Depends(oauth2_scheme)) -> str:
    """从OAuth2依赖获取令牌"""
    return token

# 解码JWT令牌
def decode_token(token: str):
    """解码JWT令牌"""
    try:
        return jwt.decode(token, settings.JWT_SECRET, algorithms=[settings.JWT_ALGORITHM])
    except JWTError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"无效的令牌: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

# 获取当前用户 (替代FastAPI Users的current_active_user)
async def get_current_user(
    token: str = Depends(get_token_from_authorization),
    db: AsyncSession = Depends(get_db)
) -> User:
    """获取当前用户"""
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 解码令牌获取用户ID
    payload = decode_token(token)
    user_id = payload.get("sub")
    
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的令牌内容",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 从数据库获取用户
    query = select(User).where(User.id == int(user_id))
    result = await db.execute(query)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="找不到用户",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user

# 获取当前超级用户
async def get_current_superuser(
    current_user: User = Depends(get_current_user),
) -> User:
    """获取当前超级用户"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要超级管理员权限",
        )
    return current_user

# 获取可选的当前用户
async def get_optional_current_user(
    token: str = Depends(get_token_from_authorization),
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """获取可选的当前用户，未登录返回None"""
    if not token:
        return None
    
    try:
        # 解码令牌获取用户ID
        payload = decode_token(token)
        user_id = payload.get("sub")
        
        if not user_id:
            return None
        
        # 从数据库获取用户
        query = select(User).where(User.id == int(user_id))
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        if not user or not user.is_active:
            return None
        
        return user
    except:
        return None

# 用于替代current_active_user的依赖项
current_active_user_custom = get_current_user
current_superuser_custom = get_current_superuser
optional_current_user_custom = get_optional_current_user

# 权限验证依赖
async def check_permission(
    permission: str,
    current_user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_db),
) -> bool:
    """
    检查用户是否拥有特定权限
    """
    from crud.crud_user import user as user_crud
    
    # 超级管理员拥有所有权限
    if current_user.is_superuser:
        return True
    
    # 检查用户是否拥有该权限
    has_perm = await user_crud.has_permission(db, user_id=current_user.id, permission=permission)
    if not has_perm:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"权限不足，需要 {permission} 权限",
        )
    
    return True

# 依赖项：检查用户是否拥有特定权限
async def check_permissions(
    required_permissions: List[str],
    user: User = Depends(current_active_user),
    db: AsyncSession = Depends(get_db),
) -> None:
    """
    检查用户是否拥有所需权限
    
    Args:
        required_permissions: 所需权限列表
        user: 当前用户
        db: 数据库会话
        
    Raises:
        HTTPException: 用户没有所需权限
    """
    # 超级管理员拥有所有权限
    if user.is_superuser:
        return
    
    # 获取用户权限
    user_permissions = await get_user_permissions(user, db)
    
    # 检查是否拥有所有所需权限
    if not all(perm in user_permissions for perm in required_permissions):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足",
        )

# 创建权限检查依赖项的工厂函数
def has_permissions(*required_permissions: str):
    """
    创建一个权限检查依赖项，要求用户拥有特定权限才能访问
    
    Args:
        required_permissions: 所需权限列表
        
    Returns:
        依赖项函数
    """
    async def _has_permissions(
        user: User = Depends(current_active_user),
        db: AsyncSession = Depends(get_db),
    ) -> User:
        await check_permissions(list(required_permissions), user, db)
        return user
    return _has_permissions

# 获取文件API用户，允许通过令牌或API密钥认证
async def get_file_api_user(
    db: AsyncSession = Depends(get_db),
    token: Optional[str] = Depends(oauth2_scheme),
    api_key: Optional[str] = Depends(api_key_header)
) -> User:
    """
    获取用于文件操作的认证用户
    支持多种认证方式：OAuth2令牌、API密钥
    """
    if token:
        # 使用JWT令牌认证
        token_data = await decode_token(token)
        user = await AuthCache.get_user_by_id(token_data.get("sub"), db)
        if not user or not user.is_active:
            # 在令牌有效但用户不存在或未激活时返回None，这将触发403错误
            return None
        return user
    elif api_key:
        # 使用API密钥认证
        from crud.crud_api_client import api_client
        client = await api_client.get_by_api_key(db=db, api_key=api_key)
        if client and client.is_active:
            # 在API密钥有效时，返回关联的用户
            user = await AuthCache.get_user_by_id(client.user_id, db)
            if user and user.is_active:
                return user
    # 未提供有效凭据时返回None，这将触发403错误
    return None

# 获取匿名文件API用户，允许匿名访问，用于测试目的
async def get_anonymous_file_user(
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    获取匿名用户，用于测试文件上传功能
    总是返回一个默认的管理员用户
    """
    # 返回ID为1的用户(通常是管理员)
    query = select(User).where(User.id == 1)
    result = await db.execute(query)
    admin_user = result.scalar_one_or_none()
    
    if not admin_user:
        # 如果找不到管理员用户，创建一个假用户
        admin_user = User(
            id=1,
            username="admin_test",
            email="<EMAIL>",
            is_active=True,
            is_superuser=True
        )
    
    return admin_user 
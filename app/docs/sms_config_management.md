# 短信配置管理指南

本文档提供管理数据库中短信配置的详细说明，适用于需要配置或修改短信验证功能的管理员。

## 配置管理方式

与以往使用环境变量进行配置不同，我们现在将短信配置存储在数据库中，这带来以下优势：

1. 更灵活的配置管理
2. 可以通过管理界面或API动态修改配置
3. 支持多个配置同时存在，可随时切换
4. 更好的安全性，避免敏感配置出现在环境文件中

## 管理工具

我们提供了两个命令行工具来管理短信配置：

### 1. 配置初始化工具

`tools/setup_sms_config.py` 用于首次将短信配置导入到数据库中。

```bash
# 基本用法
python tools/setup_sms_config.py

# 如果配置参数不完整，会提示手动输入
```

### 2. 配置管理工具

`tools/update_sms_config.py` 是一个功能全面的配置管理工具，支持以下功能：

#### 列出所有配置

```bash
python tools/update_sms_config.py list
```

#### 查看配置详情

```bash
python tools/update_sms_config.py list
```

#### 激活指定配置

```bash
python tools/update_sms_config.py activate <配置ID>
```

#### 更新配置

```bash
python tools/update_sms_config.py update <配置ID> --provider aliyun --sign-name "新签名" --template-code "SMS_123456789"
```

支持的参数：
- `--provider`: 提供商
- `--access-key`: 访问密钥ID
- `--secret-key`: 访问密钥Secret
- `--sign-name`: 短信签名
- `--template-code`: 短信模板代码
- `--auto-create-user`: 是否自动创建用户(true/false)
- `--code-expire-minutes`: 验证码有效期(分钟)
- `--code-length`: 验证码长度
- `--cooldown-seconds`: 冷却时间(秒)
- `--description`: 配置描述

#### 创建新配置

```bash
python tools/update_sms_config.py create --name "new_config" --provider aliyun --access-key "YOUR_KEY" --secret-key "YOUR_SECRET" --sign-name "签名" --template-code "SMS_123456789" --active
```

必选参数：
- `--name`: 配置名称
- `--provider`: 提供商
- `--access-key`: 访问密钥ID
- `--secret-key`: 访问密钥Secret
- `--sign-name`: 短信签名
- `--template-code`: 短信模板代码

可选参数：
- `--auto-create-user`: 是否自动创建用户(默认true)
- `--code-expire-minutes`: 验证码有效期(默认10分钟)
- `--code-length`: 验证码长度(默认6)
- `--cooldown-seconds`: 冷却时间(默认60秒)
- `--active`: 是否激活该配置(添加此参数表示激活)
- `--description`: 配置描述

#### 删除配置

```bash
python tools/update_sms_config.py delete <配置ID>
```

注意：不能删除当前处于激活状态的配置，需要先激活其他配置。

## 配置项说明

- **provider**: 短信服务提供商，如 "aliyun"
- **access_key**: 访问密钥ID
- **secret_key**: 访问密钥密码
- **sign_name**: 短信签名
- **template_code**: 短信模板代码
- **auto_create_user**: 是否自动为未注册用户创建账号
- **code_expire_minutes**: 验证码有效期(分钟)
- **code_length**: 验证码长度
- **cooldown_seconds**: 验证码发送冷却时间(秒)

## 阿里云短信配置流程

1. 登录[阿里云控制台](https://account.aliyun.com/login/login.htm)
2. 访问"短信服务"控制台
3. 获取或创建AccessKey (RAM访问控制)
4. 创建短信签名
5. 创建短信模板
6. 使用上述信息创建短信配置

例如：
```bash
python tools/update_sms_config.py create --name "aliyun_sms" --provider aliyun --access-key "YOUR_KEY" --secret-key "YOUR_SECRET" --sign-name "签名名称" --template-code "SMS_123456789" --active
```

## 处理中文编码问题

### 问题描述

在使用阿里云短信服务时，如果短信签名或模板参数中包含中文字符，可能会遇到以下错误：

```
UnicodeEncodeError: 'latin-1' codec can't encode characters in position XX-XX: ordinal not in range(256)
```

这是因为阿里云SDK在处理HTTP请求时尝试使用Latin-1编码处理中文字符导致的。

### 解决方案

我们已经对代码进行了全面修复，解决方案包括：

1. **模板参数处理**：使用`ensure_ascii=True`参数序列化JSON，确保中文字符被转换为Unicode转义序列：
   ```python
   template_param_str = json.dumps(template_param, ensure_ascii=True)
   ```

2. **签名编码处理**：当发现编码错误时，代码会自动尝试以下策略：
   - 首先尝试直接使用原始签名
   - 如果失败，尝试对签名进行URL编码
   - 如果仍然失败，尝试使用临时的ASCII签名

3. **多重错误处理**：代码增加了多层错误捕获和处理，确保在各种情况下都能提供有用的错误信息

### 可能的问题和解决方法

如果您在更新代码后仍然遇到编码问题，可以尝试以下解决方法：

1. **使用ASCII签名**：将中文签名改为英文字母或数字组成的签名
   ```bash
   python tools/update_sms_config.py update <配置ID> --sign-name "CompanyName"
   ```

2. **编码预处理**：如果必须使用中文签名，可以尝试对签名进行Base64编码后再存储
   ```bash
   python -c "import base64; print(base64.b64encode('中文签名'.encode('utf-8')).decode('ascii'))"
   # 使用输出的编码结果替换签名
   ```

3. **服务端URL编码**：您也可以修改数据库中的签名，将其替换为URL编码后的版本
   ```bash
   python -c "import urllib.parse; print(urllib.parse.quote('上海爱堪珀智能科技'))"
   # 使用输出的编码结果作为新签名
   ```

## 故障排查

如果短信验证码发送失败，可以通过以下步骤排查：

1. 确认至少有一个配置处于激活状态
   ```bash
   python tools/update_sms_config.py list
   ```

2. 检查配置参数是否正确
   - 阿里云AccessKey是否有效
   - 短信签名是否已审核通过
   - 短信模板是否已审核通过

3. 查看服务器日志，了解详细错误信息
   ```bash
   tail -100 server.log | grep "短信"
   ```

4. 检查短信签名是否包含中文
   如果签名包含中文，并且遇到编码问题，可以尝试使用"解决中文编码问题"部分的方法

5. 测试短信发送功能
   ```bash
   # 使用curl测试发送验证码
   curl -X POST "http://localhost:<端口>/api/v1/auth/sms/send-code" \
     -H "Content-Type: application/json" \
     -d '{"phone":"手机号"}'
   ```

6. 检查阿里云控制台
   登录阿里云短信服务控制台，检查是否有失败记录和详细的错误信息 
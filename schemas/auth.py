from typing import Optional, Any, Dict, List
from datetime import datetime
from pydantic import BaseModel, Field, validator

# 令牌相关
class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

class TokenPayload(BaseModel):
    sub: str
    exp: datetime
    type: Optional[str] = None

# 刷新令牌
class TokenRefresh(BaseModel):
    refresh_token: str

# 验证码相关
class VerificationCodeRequest(BaseModel):
    target: str = Field(..., description="手机号或邮箱")
    purpose: str = Field(..., description="用途：login, register, reset_password")
    
    @validator('purpose')
    def valid_purpose(cls, v):
        valid_purposes = ['login', 'register', 'reset_password']
        if v not in valid_purposes:
            raise ValueError(f'无效的用途，必须是 {", ".join(valid_purposes)} 之一')
        return v

# 短信登录
class SMSLogin(BaseModel):
    phone: str
    code: str = Field(..., min_length=4, max_length=6)

# OAuth2相关
class OAuth2Login(BaseModel):
    provider: str = Field(..., description="提供商：google, github等")
    access_token: str = Field(..., description="OAuth访问令牌")
    
    @validator('provider')
    def valid_provider(cls, v):
        valid_providers = ['google', 'github']
        if v not in valid_providers:
            raise ValueError(f'不支持的OAuth提供商，必须是 {", ".join(valid_providers)} 之一')
        return v

# CAS登录
class CASLogin(BaseModel):
    ticket: str = Field(..., description="CAS服务器返回的票据")
    service: Optional[str] = None 
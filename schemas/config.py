from typing import Optional
from datetime import datetime
from pydantic import BaseModel

class ConfigBase(BaseModel):
    key: str
    value: Optional[str] = None
    description: Optional[str] = None

class ConfigCreate(ConfigBase):
    is_system: Optional[bool] = False

class ConfigUpdate(BaseModel):
    value: Optional[str] = None
    description: Optional[str] = None

class Config(ConfigBase):
    id: int
    is_system: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True 
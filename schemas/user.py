"""
用户相关的数据模式定义
包含用户创建、更新、登录等数据模型
适配FastAPI-Users库
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field, field_validator
from fastapi_users import schemas

from schemas.role import Role


# FastAPI-Users 用户架构基类
class UserBase(schemas.BaseUser[int]):
    """用户基本信息模式，继承自FastAPI-Users"""
    username: str
    full_name: Optional[str] = None
    phone: Optional[str] = None
    avatar: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True  # Pydantic v2兼容设置，替代orm_mode


# 创建用户模式
class UserCreate(schemas.BaseUserCreate):
    """用户创建模式，用于注册新用户"""
    username: str
    full_name: Optional[str] = None
    phone: Optional[str] = None
    avatar: Optional[str] = None
    
    class Config:
        from_attributes = True


# 用户更新模式
class UserUpdate(schemas.BaseUserUpdate):
    """用户更新模式，用于更新用户信息"""
    username: Optional[str] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    avatar: Optional[str] = None
    
    class Config:
        from_attributes = True


# 用户模式（响应用）
class User(UserBase):
    """完整用户信息模式，用于API响应"""
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True


# 以下用于自定义操作，如角色分配

# 角色分配架构
class UserRoleAssign(BaseModel):
    """用户角色分配请求模式"""
    role_names: List[str]
    
    class Config:
        from_attributes = True


# 用户角色信息
class UserInfo(BaseModel):
    """用户信息及其角色权限，用于前端展示"""
    id: int
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    is_active: bool = True
    is_superuser: bool = False
    avatar: Optional[str] = None
    roles: List[str] = []
    permissions: List[str] = []
    
    class Config:
        from_attributes = True


# 登录请求
class UserLogin(BaseModel):
    """用户登录请求模式"""
    username: str
    password: str


# 带有令牌的用户登录响应
class UserWithToken(BaseModel):
    """登录成功响应，包含用户信息和访问令牌"""
    user: User
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    
    class Config:
        json_schema_extra = {
            "example": {
                "user": {
                    "id": 1,
                    "username": "admin",
                    "email": "<EMAIL>",
                    "is_active": True,
                    "is_superuser": True,
                },
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer"
            }
        }


# API响应格式
class APIResponse(BaseModel):
    """标准API响应格式"""
    code: str
    msg: str
    data: Any = None


# 密码重置
class PasswordReset(BaseModel):
    """密码重置请求模式"""
    old_password: str
    new_password: str = Field(..., min_length=6)
    
    @field_validator('new_password')
    @classmethod
    def password_different(cls, v, info):
        """验证新密码与旧密码不同"""
        if info.data and 'old_password' in info.data and v == info.data['old_password']:
            raise ValueError('新密码不能与旧密码相同')
        return v
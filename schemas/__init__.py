"""
Schema模块，包含API接口使用的各种模型

当创建新的Schema模型时，请在这里导入并导出
"""

# 导出用户相关模型
from schemas.user import User, UserCreate, UserUpdate, UserLogin, UserWithToken, UserInfo, PasswordReset

# 导出角色相关模型
from schemas.role import Role, RoleCreate, RoleUpdate, Permission, PermissionCreate, PermissionUpdate, RoleWithPermissions

# 导出菜单相关模型
from schemas.menu import Menu, MenuCreate, MenuUpdate, MenuTree, RouterModel

# 导出配置相关模型
from schemas.config import Config, ConfigCreate, ConfigUpdate

# 导出认证相关模型
from schemas.auth import Token, TokenPayload, TokenRefresh, VerificationCodeRequest, SMSLogin, OAuth2Login, CASLogin

# 导出统一响应模型
from schemas.response import (
    ResponseModel, 
    PageResponseModel, 
    PageInfo,
    ErrorResponseModel,
    ResponseCode,
    success_response,
    error_response,
    page_response
)
 
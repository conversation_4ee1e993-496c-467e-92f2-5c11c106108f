from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field

class AuditLogBase(BaseModel):
    """审计日志基础模型"""
    user_id: Optional[int] = None
    username: Optional[str] = None
    action: str
    resource: str
    resource_id: Optional[str] = None
    method: str
    path: str
    query_params: Optional[Dict[str, Any]] = None
    response_code: Optional[int] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    execution_time: Optional[float] = None
    details: Optional[Dict[str, Any]] = None
    message: Optional[str] = None
    success: bool = True

class AuditLogCreate(AuditLogBase):
    """创建审计日志模型"""
    request_body: Optional[Dict[str, Any]] = Field(default=None, description="请求体内容（可能会被清洗敏感信息）")

class AuditLogResponse(AuditLogBase):
    """审计日志响应模型"""
    id: int
    created_at: datetime
    request_body: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True

class AuditLogFilter(BaseModel):
    """审计日志过滤条件模型"""
    user_id: Optional[int] = None
    username: Optional[str] = None
    action: Optional[str] = None
    resource: Optional[str] = None
    resource_id: Optional[str] = None
    success: Optional[bool] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    ip_address: Optional[str] = None
    skip: int = 0
    limit: int = 100

class AuditLogStats(BaseModel):
    """审计日志统计模型"""
    total: int
    by_action: Dict[str, int]
    by_resource: Dict[str, int]
    by_success: Dict[str, bool]
    daily: Dict[str, int]

class AuditLogListResponse(BaseModel):
    """审计日志列表响应模型"""
    total: int
    items: List[AuditLogResponse] 
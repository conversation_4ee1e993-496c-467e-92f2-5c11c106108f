from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class Token(BaseModel):
    """令牌模型"""
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")
    session_id: Optional[str] = Field(None, description="会话ID")


class TokenPayload(BaseModel):
    """令牌载荷模型"""
    sub: Optional[str] = Field(None, description="主题(用户ID)")
    exp: Optional[int] = Field(None, description="过期时间")
    iat: Optional[int] = Field(None, description="签发时间")
    type: Optional[str] = Field(None, description="令牌类型")
    username: Optional[str] = Field(None, description="用户名")


class TokenRefresh(BaseModel):
    """令牌刷新请求模型"""
    refresh_token: str = Field(..., description="刷新令牌")


class SessionInfo(BaseModel):
    """会话信息模型"""
    session_id: str = Field(..., description="会话ID")
    user_id: int = Field(..., description="用户ID")
    created_at: int = Field(..., description="创建时间")
    last_active: int = Field(..., description="最后活动时间")
    username: Optional[str] = Field(None, description="用户名")
    
    # 其他会话数据
    data: Dict[str, Any] = Field(default_factory=dict, description="会话数据")


class SessionList(BaseModel):
    """会话列表模型"""
    sessions: List[SessionInfo] = Field(default_factory=list, description="会话列表")
    total: int = Field(..., description="总会话数") 
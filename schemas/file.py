from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, validator

class FileBase(BaseModel):
    """文件基础模式"""
    filename: str = Field(..., description="文件名")
    content_type: Optional[str] = Field(None, description="内容类型")
    category: Optional[str] = Field(None, description="文件分类")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    is_public: bool = Field(False, description="是否公开访问")
    description: Optional[str] = Field(None, description="文件描述")
    file_metadata: Optional[Dict[str, Any]] = Field(None, description="扩展元数据")


class FileCreate(FileBase):
    """创建文件的请求模式"""
    original_filename: str = Field(..., description="原始文件名")
    storage_path: str = Field(..., description="存储路径")
    file_url: str = Field(..., description="访问URL")
    size: int = Field(..., description="文件大小(字节)")
    file_hash: Optional[str] = Field(None, description="文件哈希值")
    owner_id: Optional[int] = Field(None, description="所有者ID")
    storage_type: str = Field("local", description="存储类型: local, minio")


class FileUpdate(BaseModel):
    """更新文件的请求模式"""
    filename: Optional[str] = Field(None, description="文件名")
    category: Optional[str] = Field(None, description="文件分类")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    is_public: Optional[bool] = Field(None, description="是否公开访问")
    description: Optional[str] = Field(None, description="文件描述")
    file_metadata: Optional[Dict[str, Any]] = Field(None, description="扩展元数据")
    status: Optional[str] = Field(None, description="文件状态")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    

class FileInDB(FileBase):
    """数据库中的文件模式"""
    id: UUID = Field(..., description="文件ID")
    original_filename: str = Field(..., description="原始文件名")
    storage_path: str = Field(..., description="存储路径")
    file_url: str = Field(..., description="访问URL")
    size: int = Field(..., description="文件大小(字节)")
    file_hash: Optional[str] = Field(None, description="文件哈希值")
    status: str = Field(..., description="文件状态")
    owner_id: Optional[int] = Field(None, description="所有者ID")
    storage_type: str = Field("local", description="存储类型: local, minio")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    access_key: Optional[str] = Field(None, description="访问密钥")
    
    class Config:
        from_attributes = True


class File(FileInDB):
    """API响应中的文件模式"""
    pass


class FileListResponse(BaseModel):
    """文件列表响应"""
    total: int = Field(..., description="总数")
    items: List[File] = Field(..., description="文件列表")
    
    class Config:
        from_attributes = True


class FileUploadResponse(BaseModel):
    """文件上传响应"""
    id: UUID = Field(..., description="文件ID")
    filename: str = Field(..., description="文件名")
    file_url: str = Field(..., description="访问URL")
    size: int = Field(..., description="文件大小(字节)")
    content_type: str = Field(..., description="内容类型")
    
    class Config:
        from_attributes = True 
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CAS服务配置Pydantic模式
"""
from typing import Optional
from pydantic import BaseModel, HttpUrl, Field

class CASConfigBase(BaseModel):
    server_url: HttpUrl
    service_url: HttpUrl
    version: int = Field(default=3, ge=2, le=3, description="CAS协议版本 (2或3)")
    auto_create_user: bool = True
    default_role: str = "user"
    validate_cert: bool = True
    is_active: bool = False
    description: Optional[str] = None

class CASConfigCreate(CASConfigBase):
    config_name: str = Field(default="default_cas", description="配置名称，建议唯一")

class CASConfigUpdate(BaseModel):
    server_url: Optional[HttpUrl] = None
    service_url: Optional[HttpUrl] = None
    version: Optional[int] = Field(default=None, ge=2, le=3, description="CAS协议版本 (2或3)")
    auto_create_user: Optional[bool] = None
    default_role: Optional[str] = None
    validate_cert: Optional[bool] = None
    is_active: Optional[bool] = None
    description: Optional[str] = None

class CASConfigInDBBase(CASConfigBase):
    id: int
    config_name: str
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True

class CASConfig(CASConfigInDBBase):
    pass 
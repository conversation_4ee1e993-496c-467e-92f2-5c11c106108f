from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

class MenuBase(BaseModel):
    name: str
    path: str
    component: Optional[str] = None
    redirect: Optional[str] = None
    title: str
    icon: Optional[str] = None
    parent_id: Optional[int] = None
    sort_order: Optional[int] = 0
    is_hidden: Optional[bool] = False
    is_cache: Optional[bool] = True
    is_disabled: Optional[bool] = False
    permission: Optional[str] = None

class MenuCreate(MenuBase):
    pass

class MenuUpdate(BaseModel):
    name: Optional[str] = None
    path: Optional[str] = None
    component: Optional[str] = None
    redirect: Optional[str] = None
    title: Optional[str] = None
    icon: Optional[str] = None
    parent_id: Optional[int] = None
    sort_order: Optional[int] = None
    is_hidden: Optional[bool] = None
    is_cache: Optional[bool] = None
    is_disabled: Optional[bool] = None
    permission: Optional[str] = None

class Menu(MenuBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class MenuTree(Menu):
    children: List["MenuTree"] = []
    
    class Config:
        from_attributes = True

# 解决循环引用问题
MenuTree.model_rebuild()

# 前端路由响应模型
class RouterMeta(BaseModel):
    title: str
    icon: Optional[str] = None
    hidden: Optional[bool] = False
    keepAlive: Optional[bool] = True
    permission: Optional[str] = None

class RouterModel(BaseModel):
    name: str
    path: str
    component: Optional[str] = None
    redirect: Optional[str] = None
    meta: RouterMeta
    children: Optional[List["RouterModel"]] = None
    
    class Config:
        from_attributes = True
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S")
        }

# 解决循环引用问题
RouterModel.model_rebuild() 
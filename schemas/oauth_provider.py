#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OAuth提供商配置Pydantic模式
"""
from typing import Optional, List
from pydantic import BaseModel, HttpUrl

# 基础模式
class OAuthProviderConfigBase(BaseModel):
    provider_name: str
    client_id: str
    client_secret: str # 注意：在实际返回给前端时，可能需要隐藏或部分屏蔽此字段
    authorize_url: Optional[HttpUrl] = None
    token_url: Optional[HttpUrl] = None
    user_info_url: Optional[HttpUrl] = None
    jwks_uri: Optional[HttpUrl] = None
    scopes: Optional[str] = None # 可以是逗号分隔的字符串，或者在处理时解析为列表
    icon_url: Optional[HttpUrl] = None
    is_active: bool = True
    description: Optional[str] = None

# 创建模式
class OAuthProviderConfigCreate(OAuthProviderConfigBase):
    pass

# 更新模式
class OAuthProviderConfigUpdate(BaseModel):
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    authorize_url: Optional[HttpUrl] = None
    token_url: Optional[HttpUrl] = None
    user_info_url: Optional[HttpUrl] = None
    jwks_uri: Optional[HttpUrl] = None
    scopes: Optional[str] = None
    icon_url: Optional[HttpUrl] = None
    is_active: Optional[bool] = None
    description: Optional[str] = None

# 数据库中存储的模式 (继承基础模式，添加id和时间戳)
class OAuthProviderConfigInDBBase(OAuthProviderConfigBase):
    id: int
    created_at: str
    updated_at: str

    class Config:
        from_attributes = True # Pydantic V2 (旧版 orm_mode)

# API响应模式 (可能需要调整client_secret的暴露)
class OAuthProviderConfig(OAuthProviderConfigInDBBase):
    pass

# 用于列表响应的模式
class OAuthProviderConfigList(BaseModel):
    provider_name: str
    icon_url: Optional[HttpUrl] = None
    is_active: bool
    description: Optional[str] = None
    authorize_url: Optional[HttpUrl] = None # 前端可能需要这个来构建跳转链接
    
    class Config:
        from_attributes = True 
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from core.config import settings
from db.connection_pool import db_manager

# 异步引擎
engine = db_manager.engine

# 创建异步会话
AsyncSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
)

# 创建基础模型
Base = declarative_base()

# 获取数据库会话的依赖函数
async def get_db():
    """
    依赖函数，提供数据库会话
    
    Yields:
        AsyncSession: 异步数据库会话
    """
    db = await db_manager.get_session()
    try:
        yield db
    finally:
        try:
            # 只有在会话未关闭时才尝试关闭
            if db.is_active:
                await db.close()
        except Exception as e:
            # 记录错误但不再抛出异常，避免影响正常业务流程
            from logging import getLogger
            logger = getLogger(__name__)
            logger.warning(f"关闭数据库会话时出现错误: {str(e)}")

# 使用优化过的数据库查询执行
async def execute_optimized_query(query_func, *args, query_name=None, **kwargs):
    """
    执行优化的数据库查询
    
    Args:
        query_func: 查询函数
        *args: 位置参数
        query_name: 查询名称
        **kwargs: 关键字参数
        
    Returns:
        查询结果
    """
    return await db_manager.execute_query(query_func, *args, query_name=query_name, **kwargs) 
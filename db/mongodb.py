from typing import Optional, Any, Dict, List, AsyncGenerator
import motor.motor_asyncio
from bson import ObjectId
from fastapi import Depends, HTTPException, status

from core.config import settings

# MongoDB客户端
mongo_client: Optional[motor.motor_asyncio.AsyncIOMotorClient] = None

def get_mongo_client() -> motor.motor_asyncio.AsyncIOMotorClient:
    """获取MongoDB客户端连接"""
    global mongo_client
    if mongo_client is None:
        if settings.MONGODB_URI:
            mongo_client = motor.motor_asyncio.AsyncIOMotorClient(str(settings.MONGODB_URI))
        else:
            # MongoDB未配置
            mongo_client = None
    return mongo_client

def get_mongo_db() -> Optional[motor.motor_asyncio.AsyncIOMotorDatabase]:
    """获取MongoDB数据库"""
    client = get_mongo_client()
    if client is None:
        return None
    return client[settings.MONGODB_DB]

async def get_mongodb():
    """
    获取MongoDB数据库连接的依赖函数
    
    如果MongoDB未启用，则返回None
    """
    if not settings.MONGODB_ENABLED:
        yield None
        return 
    
    db = get_mongo_db()
    if db is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="MongoDB服务未配置或不可用"
        )
    
    yield db

class MongoFile:
    """文件对象模型，用于MongoDB文件管理"""
    
    def __init__(
        self,
        id: Optional[str] = None,
        filename: str = "",
        original_filename: str = "",
        content_type: str = "",
        size: int = 0,
        path: str = "",
        user_id: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
        is_public: bool = False,
        created_at: Optional[str] = None,
        updated_at: Optional[str] = None,
    ):
        """
        初始化文件对象
        
        Args:
            id: 文件ID
            filename: 存储的文件名
            original_filename: 原始文件名
            content_type: 内容类型
            size: 文件大小(字节)
            path: 文件路径
            user_id: 上传用户ID
            metadata: 元数据
            is_public: 是否公开可访问
            created_at: 创建时间
            updated_at: 更新时间
        """
        self.id = id
        self.filename = filename
        self.original_filename = original_filename
        self.content_type = content_type
        self.size = size
        self.path = path
        self.user_id = user_id
        self.metadata = metadata or {}
        self.is_public = is_public
        self.created_at = created_at
        self.updated_at = updated_at
    
    @classmethod
    def from_mongo(cls, mongo_doc: Dict[str, Any]) -> 'MongoFile':
        """从MongoDB文档创建文件对象"""
        if mongo_doc is None:
            return None
        
        # 将MongoDB的_id转换为字符串ID
        id_str = str(mongo_doc.pop('_id', None))
        
        return cls(id=id_str, **mongo_doc)
    
    def to_mongo(self) -> Dict[str, Any]:
        """转换为MongoDB文档格式"""
        doc = self.__dict__.copy()
        
        # 如果有ID，将其转换为ObjectId
        if 'id' in doc and doc['id'] is not None:
            doc['_id'] = ObjectId(doc.pop('id'))
        else:
            # 如果没有ID，移除id字段
            doc.pop('id', None)
        
        return doc
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典表示"""
        return self.__dict__.copy()

class FileService:
    """文件服务，提供文件的CRUD操作"""
    
    def __init__(self, mongodb = None):
        """
        初始化文件服务
        
        Args:
            mongodb: MongoDB数据库连接
        """
        self.mongodb = mongodb
        self.collection = None if mongodb is None else mongodb[settings.MONGODB_FILE_COLLECTION]
    
    async def find_by_id(self, file_id: str) -> Optional[MongoFile]:
        """
        根据ID查找文件
        
        Args:
            file_id: 文件ID
            
        Returns:
            文件对象，如果未找到则返回None
        """
        if not self.collection:
            return None
        
        try:
            doc = await self.collection.find_one({"_id": ObjectId(file_id)})
            return MongoFile.from_mongo(doc)
        except Exception:
            return None
    
    async def find_by_user(
        self, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[MongoFile]:
        """
        查找用户的文件
        
        Args:
            user_id: 用户ID
            skip: 跳过的数量
            limit: 返回的最大数量
            
        Returns:
            文件对象列表
        """
        if not self.collection:
            return []
        
        cursor = self.collection.find({"user_id": user_id})
        cursor = cursor.skip(skip).limit(limit)
        
        files = []
        async for doc in cursor:
            files.append(MongoFile.from_mongo(doc))
        
        return files
    
    async def save(self, file: MongoFile) -> str:
        """
        保存文件对象
        
        Args:
            file: 文件对象
            
        Returns:
            文件ID
        """
        if not self.collection:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="MongoDB服务未配置或不可用"
            )
        
        doc = file.to_mongo()
        
        if '_id' in doc:
            # 更新文件
            result = await self.collection.replace_one(
                {"_id": doc["_id"]},
                doc
            )
            return str(doc["_id"])
        else:
            # 新建文件
            result = await self.collection.insert_one(doc)
            return str(result.inserted_id)
    
    async def delete(self, file_id: str) -> bool:
        """
        删除文件
        
        Args:
            file_id: 文件ID
            
        Returns:
            是否成功删除
        """
        if not self.collection:
            return False
        
        try:
            result = await self.collection.delete_one({"_id": ObjectId(file_id)})
            return result.deleted_count > 0
        except Exception:
            return False
    
    async def count_by_user(self, user_id: int) -> int:
        """
        统计用户文件数量
        
        Args:
            user_id: 用户ID
            
        Returns:
            文件数量
        """
        if not self.collection:
            return 0
        
        return await self.collection.count_documents({"user_id": user_id})

async def get_file_service(mongodb = Depends(get_mongodb)) -> FileService:
    """
    获取文件服务的依赖函数
    
    如果MongoDB未启用，文件服务的部分功能将不可用
    
    使用方法:
    ```
    @router.get("/files/{file_id}")
    async def get_file(
        file_id: str,
        file_service: FileService = Depends(get_file_service)
    ):
        file = await file_service.find_by_id(file_id)
        if not file:
            raise HTTPException(status_code=404, detail="文件不存在")
        return file.to_dict()
    ```
    """
    return FileService(mongodb) 
import logging
from sqlalchemy.ext.asyncio import AsyncSession

from db.session import AsyncSessionLocal, engine, Base
from crud.crud_user import user as crud_user
from crud.crud_role import role as crud_role
from crud.crud_permission import permission as crud_permission
from crud.crud_config import config as crud_config, create_config_setting
from crud.crud_menu import menu as crud_menu
from schemas.user import UserCreate
from schemas.role import RoleCreate, PermissionCreate
from schemas.menu import MenuCreate
from schemas.config import ConfigCreate

logger = logging.getLogger(__name__)

# 初始角色
initial_roles = [
    {"name": "admin", "description": "超级管理员，拥有全部权限"},
    {"name": "manager", "description": "管理员，拥有大部分管理权限"},
    {"name": "user", "description": "普通用户，仅拥有基本操作权限"},
    {"name": "guest", "description": "访客，仅拥有查看权限"},
]

# 初始权限
initial_permissions = [
    # 用户管理权限
    {"name": "user:read", "description": "查看用户信息"},
    {"name": "user:create", "description": "创建用户"},
    {"name": "user:update", "description": "更新用户"},
    {"name": "user:delete", "description": "删除用户"},
    # 角色管理权限
    {"name": "role:read", "description": "查看角色信息"},
    {"name": "role:create", "description": "创建角色"},
    {"name": "role:update", "description": "更新角色"},
    {"name": "role:delete", "description": "删除角色"},
    # 权限管理权限
    {"name": "permission:read", "description": "查看权限信息"},
    {"name": "permission:create", "description": "创建权限"},
    {"name": "permission:update", "description": "更新权限"},
    {"name": "permission:delete", "description": "删除权限"},
    # 菜单管理权限
    {"name": "menu:read", "description": "查看菜单信息"},
    {"name": "menu:create", "description": "创建菜单"},
    {"name": "menu:update", "description": "更新菜单"},
    {"name": "menu:delete", "description": "删除菜单"},
    # 配置管理权限
    {"name": "config:read", "description": "查看配置信息"},
    {"name": "config:update", "description": "更新配置信息"},
    # 文件管理权限
    {"name": "file:read", "description": "查看文件信息"},
    {"name": "file:upload", "description": "上传文件"},
    {"name": "file:delete", "description": "删除文件"},
]

# 角色权限映射
role_permissions_map = {
    "admin": [perm["name"] for perm in initial_permissions],  # 超级管理员拥有所有权限
    "manager": [  # 管理员拥有除删除外的大部分权限
        "user:read", "user:create", "user:update",
        "role:read", 
        "permission:read", 
        "menu:read", "menu:create", "menu:update",
        "config:read", "config:update",
        "file:read", "file:upload", "file:delete",
    ],
    "user": [  # 普通用户拥有基本操作权限
        "user:read",
        "role:read",
        "permission:read",
        "menu:read",
        "config:read",
        "file:read", "file:upload",
    ],
    "guest": [  # 访客仅拥有查看权限
        "user:read",
        "role:read",
        "permission:read",
        "menu:read",
        "config:read",
        "file:read",
    ],
}

# 初始菜单
initial_menus = [
    # 首页
    {
        "name": "dashboard",
        "path": "/dashboard",
        "component": "LAYOUT",
        "redirect": "/dashboard/index",
        "title": "首页",
        "icon": "DashboardOutlined",
        "sort_order": 1,
        "is_hidden": False,
        "is_cache": True,
        "is_disabled": False,
        "parent_id": None,
        "permission": None,
        "children": [
            {
                "name": "workbench",
                "path": "index",
                "component": "/dashboard/index/index",
                "title": "工作台",
                "icon": "DesktopOutlined",
                "sort_order": 1,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": None,
            },
            {
                "name": "analysis",
                "path": "analysis",
                "component": "/dashboard/analysis/index",
                "title": "数据分析",
                "icon": "BarChartOutlined",
                "sort_order": 2,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": None,
            }
        ]
    },
    # 系统管理
    {
        "name": "system",
        "path": "/system",
        "component": "LAYOUT",
        "redirect": "/system/user",
        "title": "系统管理",
        "icon": "SettingOutlined",
        "sort_order": 2,
        "is_hidden": False,
        "is_cache": True,
        "is_disabled": False,
        "parent_id": None,
        "permission": None,
        "children": [
            {
                "name": "user",
                "path": "user",
                "component": "/system/user/index",
                "title": "用户管理",
                "icon": "UserOutlined",
                "sort_order": 1,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": "user:read",
            },
            {
                "name": "role",
                "path": "role",
                "component": "/system/role/index",
                "title": "角色管理",
                "icon": "TeamOutlined",
                "sort_order": 2,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": "role:read",
            },
            {
                "name": "menu",
                "path": "menu",
                "component": "/system/menu/index",
                "title": "菜单管理",
                "icon": "MenuOutlined",
                "sort_order": 3,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": "menu:read",
            },
            {
                "name": "permission",
                "path": "permission",
                "component": "/system/permission/index",
                "title": "权限管理",
                "icon": "SafetyOutlined",
                "sort_order": 4,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": "permission:read",
            },
            {
                "name": "config",
                "path": "config",
                "component": "/system/config/index",
                "title": "系统配置",
                "icon": "ToolOutlined",
                "sort_order": 5,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": "config:read",
            }
        ]
    },
    # 内容管理
    {
        "name": "content",
        "path": "/content",
        "component": "LAYOUT",
        "redirect": "/content/article",
        "title": "内容管理",
        "icon": "FileTextOutlined",
        "sort_order": 3,
        "is_hidden": False,
        "is_cache": True,
        "is_disabled": False,
        "parent_id": None,
        "permission": None,
        "children": [
            {
                "name": "article",
                "path": "article",
                "component": "/content/article/index",
                "title": "文章管理",
                "icon": "FileOutlined",
                "sort_order": 1,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": None,
            },
            {
                "name": "category",
                "path": "category",
                "component": "/content/category/index",
                "title": "分类管理",
                "icon": "FolderOutlined",
                "sort_order": 2,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": None,
            },
            {
                "name": "tag",
                "path": "tag",
                "component": "/content/tag/index",
                "title": "标签管理",
                "icon": "TagsOutlined",
                "sort_order": 3,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": None,
            }
        ]
    },
    # 文件管理
    {
        "name": "file",
        "path": "/file",
        "component": "LAYOUT",
        "redirect": "/file/list",
        "title": "文件管理",
        "icon": "FileOutlined",
        "sort_order": 4,
        "is_hidden": False,
        "is_cache": True,
        "is_disabled": False,
        "parent_id": None,
        "permission": "file:read",
        "children": [
            {
                "name": "filelist",
                "path": "list",
                "component": "/file/list/index",
                "title": "文件列表",
                "icon": "FileSearchOutlined",
                "sort_order": 1,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": "file:read",
            },
            {
                "name": "fileupload",
                "path": "upload",
                "component": "/file/upload/index",
                "title": "文件上传",
                "icon": "UploadOutlined",
                "sort_order": 2,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": "file:upload",
            }
        ]
    },
    # 个人中心
    {
        "name": "personal",
        "path": "/personal",
        "component": "LAYOUT",
        "redirect": "/personal/center",
        "title": "个人中心",
        "icon": "UserOutlined",
        "sort_order": 99,
        "is_hidden": False,
        "is_cache": True,
        "is_disabled": False,
        "parent_id": None,
        "permission": None,
        "children": [
            {
                "name": "center",
                "path": "center",
                "component": "/personal/center/index",
                "title": "个人信息",
                "icon": "IdcardOutlined",
                "sort_order": 1,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": None,
            },
            {
                "name": "setting",
                "path": "setting",
                "component": "/personal/setting/index",
                "title": "个人设置",
                "icon": "SettingOutlined",
                "sort_order": 2,
                "is_hidden": False,
                "is_cache": True,
                "is_disabled": False,
                "permission": None,
            }
        ]
    },
]

# 初始配置
initial_configs = [
    {"key": "SITE_NAME", "value": "后台管理系统", "description": "网站名称"},
    {"key": "SITE_LOGO", "value": "/static/logo.png", "description": "网站Logo"},
    {"key": "SITE_FAVICON", "value": "/static/favicon.ico", "description": "网站图标"},
    {"key": "LOGIN_METHODS", "value": "password,oauth2,cas,sms", "description": "启用的登录方式，用逗号分隔"},
    {"key": "DEFAULT_ROLE", "value": "user", "description": "默认角色"},
    {"key": "USER_REGISTRATION_ENABLED", "value": "true", "description": "是否允许用户注册"},
    {"key": "USER_DEFAULT_AVATAR", "value": "/static/avatar.png", "description": "用户默认头像"},
    {"key": "SYSTEM_ANNOUNCEMENT", "value": "欢迎使用后台管理系统", "description": "系统公告"},
    {"key": "THEME_COLOR", "value": "#1890ff", "description": "主题色"},
    {"key": "LAYOUT_MODE", "value": "light", "description": "布局模式：light或dark"},
]

async def init_db():
    """初始化数据库"""
    try:
        # 创建所有表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        # 创建一个数据库会话
        async with AsyncSessionLocal() as db:
            # 初始化基础数据
            await init_roles(db)
            await init_permissions(db)
            await init_role_permissions(db)
            await init_menus(db)
            await init_configs(db)
            await init_users(db)
            
            logger.info("数据库初始化完成!")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise

async def init_roles(db: AsyncSession):
    """初始化角色数据"""
    for role_data in initial_roles:
        # 检查是否已存在
        existing = await crud_role.get_by_name(db, name=role_data["name"])
        if not existing:
            role_in = RoleCreate(**role_data)
            await crud_role.create(db, obj_in=role_in)
    logger.info("角色数据初始化完成")

async def init_permissions(db: AsyncSession):
    """初始化权限数据"""
    for perm_data in initial_permissions:
        # 检查是否已存在
        existing = await crud_permission.get_by_name(db, name=perm_data["name"])
        if not existing:
            perm_in = PermissionCreate(**perm_data)
            await crud_permission.create(db, obj_in=perm_in)
    logger.info("权限数据初始化完成")

async def init_role_permissions(db: AsyncSession):
    """初始化角色权限关联"""
    # 直接导入关联表和所需类型
    from sqlalchemy import select, delete, insert
    from sqlalchemy.sql import text
    from sqlalchemy.exc import IntegrityError
    from models.role import Role, Permission, role_permission
    
    # 查询所有角色和权限，建立名称到ID的映射
    query = select(Role)
    result = await db.execute(query)
    all_roles = result.scalars().all()
    role_map = {role.name: role.id for role in all_roles}
    
    query = select(Permission)
    result = await db.execute(query)
    all_permissions = result.scalars().all()
    perm_map = {perm.name: perm.id for perm in all_permissions}
    
    # 获取现有的角色-权限关联
    query = select(role_permission)
    result = await db.execute(query)
    existing_relations = [(row.role_id, row.permission_id) for row in result.fetchall()]
    
    # 对每个角色处理其权限
    for role_name, perm_names in role_permissions_map.items():
        if role_name not in role_map:
            logger.warning(f"角色 {role_name} 不存在，跳过权限分配")
            continue
            
        role_id = role_map[role_name]
        
        # 对于删除权限关联，考虑以下选项（选择一种）:
        # 选项1: 保留现有权限，只添加新的权限（不删除现有权限）
        # 选项2: 删除所有现有权限，重新添加所有定义的权限
        # 选项3: 根据差异有选择地添加/删除权限
        
        # 本例使用选项1：保留现有权限，只添加新的
        
        # 添加新的权限关联
        for perm_name in perm_names:
            if perm_name not in perm_map:
                logger.warning(f"权限 {perm_name} 不存在，跳过")
                continue
                
            permission_id = perm_map[perm_name]
            
            # 检查是否已存在关联
            if (role_id, permission_id) in existing_relations:
                logger.debug(f"角色 {role_name} 已有权限 {perm_name}，跳过")
                continue
            
            try:
                # 使用SQL参数化查询插入记录
                insert_stmt = role_permission.insert().values(
                    role_id=role_id, 
                    permission_id=permission_id
                )
                await db.execute(insert_stmt)
            except IntegrityError as e:
                # 捕获唯一约束冲突异常
                logger.warning(f"插入角色权限关系失败 (role_id={role_id}, permission_id={permission_id}): {e}")
                # 回滚当前事务以继续处理其他记录
                await db.rollback()
    
    # 提交所有更改
    try:
        await db.commit()
        logger.info("角色权限关联初始化完成")
    except Exception as e:
        logger.error(f"提交角色权限关联失败: {e}")
        await db.rollback()
        raise

async def init_menus(db: AsyncSession):
    """初始化菜单数据"""
    # 递归创建菜单及其子菜单
    async def create_menu_with_children(menu_data, parent_id=None):
        # 准备菜单数据
        children = menu_data.pop("children", []) if "children" in menu_data else []
        menu_data["parent_id"] = parent_id
        
        # 创建菜单
        menu_in = MenuCreate(**menu_data)
        created_menu = await crud_menu.create(db, obj_in=menu_in)
        
        # 创建子菜单
        for child_data in children:
            await create_menu_with_children(child_data, created_menu.id)
        
        return created_menu
    
    # 检查菜单是否已存在，如果不存在则创建
    from sqlalchemy import select
    from models.menu import Menu
    
    query = select(Menu)
    result = await db.execute(query)
    existing_menus = result.scalars().all()
    
    if not existing_menus:
        for menu_data in initial_menus:
            await create_menu_with_children(menu_data.copy())
        logger.info("菜单数据初始化完成")
    else:
        logger.info("菜单数据已存在，跳过初始化")

async def init_configs(db: AsyncSession):
    """初始化配置数据"""
    for config_data in initial_configs:
        # 尝试创建或更新配置
        await create_config_setting(db, ConfigCreate(**config_data))
    logger.info("配置数据初始化完成")

async def init_users(db: AsyncSession):
    """初始化用户数据"""
    # 初始化管理员账号
    admin_data = {
        "username": "admin",
        "email": "<EMAIL>",
        "password": "admin123",
        "is_active": True,
        "is_superuser": True,
        "full_name": "超级管理员",
        "avatar": "/static/avatar/admin.png",
    }
    
    # 初始化测试用户
    test_users = [
        {
            "username": "manager",
            "email": "<EMAIL>",
            "password": "manager123",
            "is_active": True,
            "is_superuser": False,
            "full_name": "管理员",
            "avatar": "/static/avatar/manager.png",
        },
        {
            "username": "user",
            "email": "<EMAIL>",
            "password": "user123",
            "is_active": True,
            "is_superuser": False,
            "full_name": "普通用户",
            "avatar": "/static/avatar/user.png",
        },
        {
            "username": "guest",
            "email": "<EMAIL>",
            "password": "guest123",
            "is_active": True,
            "is_superuser": False,
            "full_name": "访客",
            "avatar": "/static/avatar/guest.png",
        },
    ]
    
    # 创建管理员账号
    existing = await crud_user.get_by_username(db, username=admin_data["username"])
    if not existing:
        user_in = UserCreate(**admin_data)
        await crud_user.create(db, obj_in=user_in, roles=["admin"])
        logger.info("管理员账号初始化完成")
    else:
        logger.info("管理员账号已存在，跳过初始化")
    
    # 创建测试用户
    for user_data in test_users:
        username = user_data["username"]
        existing = await crud_user.get_by_username(db, username=username)
        if not existing:
            user_in = UserCreate(**user_data)
            await crud_user.create(db, obj_in=user_in, roles=[username])  # 用户名与角色名相同
            logger.info(f"用户 {username} 初始化完成")
        else:
            logger.info(f"用户 {username} 已存在，跳过初始化")
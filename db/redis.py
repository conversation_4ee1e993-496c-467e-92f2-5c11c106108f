from typing import Optional, Any, Dict, List, AsyncGenerator
import json
from redis.asyncio import Redis, ConnectionPool
from fastapi import Depends

from core.config import settings

# 创建Redis连接池
redis_pool: Optional[ConnectionPool] = None

def get_redis_pool() -> Optional[ConnectionPool]:
    """获取Redis连接池"""
    global redis_pool
    if redis_pool is None:
        try:
            redis_pool = ConnectionPool.from_url(
                str(settings.REDIS_URL),
                decode_responses=True,  # 自动解码响应
                socket_timeout=settings.REDIS_TIMEOUT,
                socket_connect_timeout=settings.REDIS_TIMEOUT,
            )
        except Exception as e:
            import logging
            logging.getLogger(__name__).error(f"Redis连接池初始化失败: {str(e)}")
            return None
    return redis_pool

async def get_redis() -> Redis:
    """
    获取Redis客户端
    
    使用方法:
    ```
    redis_client = await get_redis()
    await redis_client.set("key", "value")
    ```
    """
    pool = get_redis_pool()
    if pool is None:
        # Redis连接池不可用，抛出异常或返回None
        # 这里我们使用异常，因为这是一个依赖函数
        import logging
        logging.getLogger(__name__).warning("Redis连接池不可用")
        yield None
        return
        
    try:
        client = Redis(connection_pool=pool)
        # 测试连接
        await client.ping()
        yield client
    except Exception as e:
        import logging
        logging.getLogger(__name__).error(f"Redis客户端创建失败: {str(e)}")
        yield None
    finally:
        # 安全关闭客户端，避免资源泄漏
        if 'client' in locals() and client is not None:
            try:
                await client.close()
            except Exception as close_err:
                logging.getLogger(__name__).error(f"关闭Redis客户端失败: {str(close_err)}")

class RedisCache:
    """Redis缓存工具类"""
    
    def __init__(self, redis: Redis):
        """
        初始化Redis缓存
        
        Args:
            redis: Redis客户端
        """
        self.redis = redis
    
    async def get(self, key: str, prefix: str = "") -> Any:
        """
        获取缓存数据
        
        Args:
            key: 缓存键
            prefix: 前缀
            
        Returns:
            缓存的数据，如果没有找到则返回None
        """
        full_key = f"{prefix}{key}" if prefix else key
        data = await self.redis.get(full_key)
        if data is None:
            return None
        
        try:
            return json.loads(data)
        except json.JSONDecodeError:
            return data
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire: int = None,
        prefix: str = "",
    ) -> bool:
        """
        设置缓存数据
        
        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间(秒)，如果为None则使用默认过期时间
            prefix: 前缀
            
        Returns:
            是否成功设置
        """
        full_key = f"{prefix}{key}" if prefix else key
        
        # 如果值不是字符串，则转换为JSON
        if not isinstance(value, str):
            value = json.dumps(value)
        
        # 设置过期时间
        expire_seconds = expire if expire is not None else settings.REDIS_CACHE_EXPIRE_SECONDS
        
        return await self.redis.set(full_key, value, ex=expire_seconds)
    
    async def delete(self, key: str, prefix: str = "") -> int:
        """
        删除缓存数据
        
        Args:
            key: 缓存键
            prefix: 前缀
            
        Returns:
            删除的键数量
        """
        full_key = f"{prefix}{key}" if prefix else key
        return await self.redis.delete(full_key)
    
    async def exists(self, key: str, prefix: str = "") -> bool:
        """
        检查键是否存在
        
        Args:
            key: 缓存键
            prefix: 前缀
            
        Returns:
            键是否存在
        """
        full_key = f"{prefix}{key}" if prefix else key
        return await self.redis.exists(full_key) > 0
    
    async def ttl(self, key: str, prefix: str = "") -> int:
        """
        获取键的剩余生存时间(秒)
        
        Args:
            key: 缓存键
            prefix: 前缀
            
        Returns:
            剩余生存时间(秒)，-1表示永不过期，-2表示键不存在
        """
        full_key = f"{prefix}{key}" if prefix else key
        return await self.redis.ttl(full_key)
    
    async def incr(self, key: str, amount: int = 1, prefix: str = "") -> int:
        """
        递增键的值
        
        Args:
            key: 缓存键
            amount: 增加的数量
            prefix: 前缀
            
        Returns:
            递增后的值
        """
        full_key = f"{prefix}{key}" if prefix else key
        return await self.redis.incr(full_key, amount)
    
    async def keys(self, pattern: str) -> List[str]:
        """
        获取匹配模式的所有键
        
        Args:
            pattern: 匹配模式
            
        Returns:
            匹配的键列表
        """
        return await self.redis.keys(pattern)
        
    async def expire(self, key: str, seconds: int, prefix: str = "") -> bool:
        """
        设置键的过期时间
        
        Args:
            key: 缓存键
            seconds: 过期时间(秒)
            prefix: 前缀
            
        Returns:
            是否成功设置
        """
        full_key = f"{prefix}{key}" if prefix else key
        return await self.redis.expire(full_key, seconds)
        
    async def hmset_dict(self, key: str, mapping: Dict[str, Any], prefix: str = "") -> bool:
        """
        设置哈希表中的多个字段
        
        Args:
            key: 缓存键
            mapping: 字段和值的映射
            prefix: 前缀
            
        Returns:
            是否成功设置
        """
        full_key = f"{prefix}{key}" if prefix else key
        
        # 转换值为字符串
        string_mapping = {}
        for field, value in mapping.items():
            if not isinstance(value, str):
                string_mapping[field] = json.dumps(value)
            else:
                string_mapping[field] = value
                
        return await self.redis.hset(full_key, mapping=string_mapping)

async def get_redis_cache(redis: Redis) -> RedisCache:
    """
    获取Redis缓存工具
    
    Args:
        redis: Redis客户端
        
    Returns:
        RedisCache实例
    """
    return RedisCache(redis) 
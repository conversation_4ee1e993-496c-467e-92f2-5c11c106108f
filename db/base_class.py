"""
SQLAlchemy ORM 基类定义
"""
import re
from datetime import datetime
from sqlalchemy import Column, Integer, DateTime, func
from sqlalchemy.ext.declarative import declared_attr

# 从session导入Base
from db.session import Base

# 表名转换函数
def camel_to_snake(name):
    """
    将驼峰命名转换为蛇形命名
    例如: MyModel -> my_model
    """
    # 在大写字母前添加下划线，并转为小写
    name = re.sub(r'(?<!^)(?=[A-Z])', '_', name).lower()
    return name

class BaseModel:
    """
    所有模型的基类，提供通用字段和方法
    """
    # 使用声明性属性自动生成表名
    @declared_attr
    def __tablename__(cls):
        return camel_to_snake(cls.__name__)
    
    # 主键ID
    id = Column(Integer, primary_key=True, index=True)
    
    # 时间戳，使用func.now()确保使用数据库时间而非Python时间
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # 公共方法
    def to_dict(self):
        """将模型实例转换为字典"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            # 处理datetime类型
            if isinstance(value, datetime):
                value = value.isoformat()
            result[column.name] = value
        return result
    
    # 字符串表示
    def __repr__(self):
        return f"<{self.__class__.__name__} {self.id}>" 
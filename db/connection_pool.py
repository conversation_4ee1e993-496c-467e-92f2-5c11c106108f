import logging
import time
import asyncio
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import create_async_engine, AsyncEngine, AsyncSession
from sqlalchemy.pool import QueuePool
from sqlalchemy import text

logger = logging.getLogger(__name__)

class DBConnectionManager:
    """数据库连接管理器，优化连接池使用和监控查询性能"""
    
    _instance = None
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(DBConnectionManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(
        self, 
        database_url: Optional[str] = None,
        pool_size: int = 5,
        max_overflow: int = 10,
        pool_recycle: int = 3600,
        pool_timeout: int = 30
    ):
        """初始化连接管理器
        
        Args:
            database_url: 数据库连接URL
            pool_size: 连接池大小
            max_overflow: 最大连接溢出数
            pool_recycle: 连接回收时间(秒)
            pool_timeout: 获取连接超时时间(秒)
        """
        if self._initialized:
            return
            
        from core.config import settings
        
        # 确保 database_url 是字符串类型
        if database_url:
            self.database_url = str(database_url)
        else:
            self.database_url = str(settings.DATABASE_URL)
            
        self.engine: Optional[AsyncEngine] = None
        self.pool_size = pool_size
        self.max_overflow = max_overflow
        self.pool_recycle = pool_recycle
        self.pool_timeout = pool_timeout
        
        # 查询统计
        self.query_stats: Dict[str, Any] = {
            "total_queries": 0,
            "slow_queries": 0,
            "error_queries": 0,
            "avg_duration": 0,
            "last_reset": datetime.now().isoformat()
        }
        
        # 慢查询记录
        self.slow_queries: List[Dict[str, Any]] = []
        self.max_slow_queries = 100
        self.slow_query_threshold = 1.0  # 秒
        
        # 正在执行的查询跟踪
        self.active_queries: Set[str] = set()
        
        # 初始化引擎
        self._initialize_engine()
        
        # 启动健康检查
        self._start_health_check()
        
        self._initialized = True
    
    def _initialize_engine(self):
        """初始化数据库引擎"""
        try:
            self.engine = create_async_engine(
                self.database_url,
                pool_size=self.pool_size,
                max_overflow=self.max_overflow,
                pool_recycle=self.pool_recycle,
                pool_timeout=self.pool_timeout,
                pool_pre_ping=True,  # 连接前执行ping操作，确保连接有效
                echo=False,
                future=True
            )
            logger.info(f"数据库引擎已初始化 (池大小: {self.pool_size}, 最大溢出: {self.max_overflow})")
        except Exception as e:
            logger.error(f"初始化数据库引擎失败: {str(e)}")
            raise
    
    def _start_health_check(self):
        """启动数据库连接健康检查"""
        async def health_check_task():
            while True:
                try:
                    await self.check_db_health()
                    # 每5分钟执行一次
                    await asyncio.sleep(300)
                except Exception as e:
                    logger.error(f"数据库健康检查失败: {str(e)}")
                    # 出错后等待1分钟再试
                    await asyncio.sleep(60)
        
        try:
            # 检查是否有运行中的事件循环
            loop = asyncio.get_running_loop()
            asyncio.create_task(health_check_task())
            logger.info("数据库健康检查任务已启动")
        except RuntimeError:
            # 没有运行中的事件循环，可能是在命令行环境中
            logger.info("没有运行中的事件循环，跳过数据库健康检查启动")
    
    async def get_session(self) -> AsyncSession:
        """获取数据库会话
        
        Returns:
            数据库会话
        """
        if self.engine is None:
            self._initialize_engine()
            
        # 创建会话
        session = AsyncSession(bind=self.engine)
        return session
    
    async def check_db_health(self) -> Dict[str, Any]:
        """检查数据库健康状态
        
        Returns:
            健康状态报告
        """
        start_time = time.time()
        is_healthy = False
        error_message = None
        
        try:
            if self.engine is None:
                raise ValueError("数据库引擎未初始化")
                
            # 获取连接池统计
            pool_stats = {
                "size": self.pool_size,
                "max_overflow": self.max_overflow,
                "timeout": self.pool_timeout,
                "recycle": self.pool_recycle
            }
            
            # 测试连接
            async with AsyncSession(bind=self.engine) as session:
                # 执行简单查询
                result = await session.execute(text("SELECT 1"))
                is_healthy = result.scalar() == 1
            
            response_time = time.time() - start_time
            
            health_report = {
                "status": "healthy" if is_healthy else "unhealthy",
                "response_time": response_time,
                "timestamp": datetime.now().isoformat(),
                "pool_stats": pool_stats,
                "query_stats": {k: v for k, v in self.query_stats.items()},
                "active_queries": len(self.active_queries)
            }
            
            return health_report
            
        except Exception as e:
            error_message = str(e)
            logger.error(f"数据库健康检查失败: {error_message}")
            
            return {
                "status": "unhealthy",
                "error": error_message,
                "timestamp": datetime.now().isoformat()
            }
    
    async def execute_query(
        self, 
        query_func,
        *args,
        query_name: Optional[str] = None,
        **kwargs
    ):
        """执行查询并记录统计信息
        
        Args:
            query_func: 查询函数
            *args: 位置参数
            query_name: 查询名称(用于记录)
            **kwargs: 关键字参数
            
        Returns:
            查询结果
        """
        if query_name is None:
            query_name = query_func.__qualname__
            
        # 生成唯一标识
        query_id = f"{query_name}_{time.time()}"
        
        # 记录开始执行
        start_time = time.time()
        self.active_queries.add(query_id)
        
        try:
            # 执行查询
            result = await query_func(*args, **kwargs)
            
            # 更新统计
            duration = time.time() - start_time
            self._update_query_stats(query_name, duration)
            
            # 检查是否是慢查询
            if duration >= self.slow_query_threshold:
                self._record_slow_query(query_name, duration, args, kwargs)
                
            return result
            
        except Exception as e:
            # 记录错误
            duration = time.time() - start_time
            self._update_query_stats(query_name, duration, error=True)
            logger.error(f"查询 {query_name} 失败: {str(e)}")
            raise
            
        finally:
            # 移除活动查询记录
            self.active_queries.discard(query_id)
    
    def _update_query_stats(self, query_name: str, duration: float, error: bool = False):
        """更新查询统计信息
        
        Args:
            query_name: 查询名称
            duration: 执行时间(秒)
            error: 是否出错
        """
        # 更新总查询数
        self.query_stats["total_queries"] += 1
        
        # 更新平均持续时间
        old_avg = self.query_stats["avg_duration"]
        old_count = self.query_stats["total_queries"] - 1
        if old_count > 0:
            self.query_stats["avg_duration"] = (old_avg * old_count + duration) / self.query_stats["total_queries"]
        else:
            self.query_stats["avg_duration"] = duration
        
        # 更新慢查询和错误查询计数
        if duration >= self.slow_query_threshold:
            self.query_stats["slow_queries"] += 1
            
        if error:
            self.query_stats["error_queries"] += 1
    
    def _record_slow_query(self, query_name: str, duration: float, args, kwargs):
        """记录慢查询
        
        Args:
            query_name: 查询名称
            duration: 执行时间(秒)
            args: 查询参数
            kwargs: 查询关键字参数
        """
        # 创建慢查询记录
        slow_query = {
            "query": query_name,
            "duration": duration,
            "timestamp": datetime.now().isoformat(),
            "args_summary": str(args)[:100] if args else None,
            "kwargs_summary": str(kwargs)[:100] if kwargs else None
        }
        
        # 添加到慢查询列表
        self.slow_queries.append(slow_query)
        
        # 保持慢查询列表在最大大小以内
        if len(self.slow_queries) > self.max_slow_queries:
            self.slow_queries.pop(0)
            
        # 记录日志
        logger.warning(f"慢查询: {query_name} (耗时: {duration:.4f}秒)")
    
    def reset_stats(self):
        """重置查询统计信息"""
        self.query_stats = {
            "total_queries": 0,
            "slow_queries": 0, 
            "error_queries": 0,
            "avg_duration": 0,
            "last_reset": datetime.now().isoformat()
        }
        self.slow_queries = []

# 创建单例实例
db_manager = DBConnectionManager() 
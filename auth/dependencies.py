import logging
from typing import Callable, List, Optional, Set, Union

from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.session import get_db
from auth.users import User
# 从api.deps中导入自定义认证依赖项
from api.deps import get_current_user

# 添加日志记录器
logger = logging.getLogger(__name__)

# 恢复原始的get_user_permissions函数
async def get_user_permissions(
    user: User,
    db: AsyncSession
) -> Set[str]:
    """
    获取当前用户的所有权限
    
    Args:
        user: 当前用户
        db: 数据库会话
        
    Returns:
        用户权限集合
    """
    from crud.crud_permission import permission
    
    # 如果是超级管理员，返回所有权限
    if user.is_superuser:
        all_permissions = await permission.get_multi(db)
        return {perm.name for perm in all_permissions}
    
    # 否则获取用户角色所拥有的权限
    user_permissions = await permission.get_user_permissions(db, user.id)
    return {perm.name for perm in user_permissions}

# 获取用户角色 - 带缓存版本
async def get_user_roles_cached(
    user: User,
    db: AsyncSession
) -> Set[str]:
    """
    获取用户的所有角色名称（带缓存）
    
    Args:
        user: 当前用户
        db: 数据库会话
        
    Returns:
        角色名称集合
    """
    # 如果用户角色已加载，直接使用
    if hasattr(user, 'roles') and user.roles:
        return {role.name for role in user.roles}
    
    # 尝试从缓存获取角色
    try:
        from utils.permission_cache import get_permission_cache
        
        cache = await get_permission_cache()
        
        # 尝试从缓存获取角色
        cache_key = f"user_roles:{user.id}"
        cached_roles = await cache.redis.get(cache_key)
        
        if cached_roles:
            import json
            role_names = json.loads(cached_roles)
            logger.debug(f"已从缓存获取用户({user.id})角色: {role_names}")
            return set(role_names)
    except Exception as e:
        logger.warning(f"从缓存获取角色失败: {str(e)}")
    
    # 缓存未命中，从数据库获取角色
    from crud.crud_role import role
    
    roles = await role.get_user_roles(db, user_id=user.id)
    role_names = {r.name for r in roles}
    
    # 将角色存入缓存
    try:
        from utils.permission_cache import get_permission_cache
        import json
        
        cache = await get_permission_cache()
        cache_key = f"user_roles:{user.id}"
        
        await cache.redis.set(
            cache_key,
            json.dumps(list(role_names)),
            expire=cache.expire_time
        )
        logger.debug(f"已将用户({user.id})角色缓存: {role_names}")
    except Exception as e:
        logger.warning(f"缓存用户角色失败: {str(e)}")
    
    return role_names

# 获取用户权限 - 优化版本带缓存
async def get_user_permissions_cached(
    user: User,
    db: AsyncSession
) -> Set[str]:
    """
    获取当前用户的所有权限 (带缓存)
    
    Args:
        user: 当前用户
        db: 数据库会话
        
    Returns:
        用户权限集合
    """
    # 超级管理员直接返回所有权限
    if user.is_superuser:
        from crud.crud_permission import permission
        all_permissions = await permission.get_multi(db)
        return {perm.name for perm in all_permissions}
    
    # 尝试从缓存中获取用户权限
    try:
        from utils.permission_cache import get_permission_cache
        
        # 获取缓存实例
        cache = await get_permission_cache()
        
        # 尝试从缓存获取权限
        cached_permissions = await cache.get_user_permissions(user.id)
        if cached_permissions:
            logger.debug(f"已从缓存获取用户({user.id})权限: {len(cached_permissions)}个")
            return cached_permissions
    except Exception as e:
        logger.warning(f"从缓存获取权限失败: {str(e)}")
    
    # 缓存未命中，从数据库获取权限
    from crud.crud_permission import permission
    
    # 获取用户角色所拥有的权限
    user_permissions = await permission.get_user_permissions(db, user.id)
    permission_set = {perm.name for perm in user_permissions}
    
    # 将权限存入缓存
    try:
        from utils.permission_cache import get_permission_cache
        
        cache = await get_permission_cache()
        await cache.set_user_permissions(user.id, permission_set)
        logger.debug(f"已将用户({user.id})权限缓存: {len(permission_set)}个")
    except Exception as e:
        logger.warning(f"缓存用户权限失败: {str(e)}")
    
    return permission_set

# 权限检查依赖函数
def has_permission(required_permissions: Union[str, List[str]], require_all: bool = False):
    """
    检查用户是否拥有指定权限的依赖函数
    
    Args:
        required_permissions: 需要的权限，可以是单个权限字符串或权限列表
        require_all: 是否需要拥有所有指定的权限，为False时只需要拥有其中一个即可
        
    Returns:
        依赖函数
    """
    if isinstance(required_permissions, str):
        required_permissions = [required_permissions]
    
    required_permissions_set = set(required_permissions)
    
    async def check_permissions(
        user: User = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
    ) -> User:
        # 超级管理员拥有所有权限
        if user.is_superuser:
            return user
        
        # 获取用户权限
        user_permissions = await get_user_permissions_cached(user, db)
        
        # 检查权限
        if require_all:
            # 必须拥有所有权限
            if not required_permissions_set.issubset(user_permissions):
                missing = required_permissions_set - user_permissions
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足。缺少以下权限: {', '.join(missing)}",
                )
        else:
            # 至少拥有一个权限
            if not required_permissions_set.intersection(user_permissions):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足。需要以下权限之一: {', '.join(required_permissions_set)}",
                )
        
        return user
    
    return check_permissions

# 检查角色依赖函数
def has_role(required_roles: Union[str, List[str]], require_all: bool = False):
    """
    检查用户是否拥有指定角色的依赖函数
    
    Args:
        required_roles: 需要的角色，可以是单个角色名或角色列表
        require_all: 是否需要拥有所有指定的角色，为False时只需要拥有其中一个即可
        
    Returns:
        依赖函数
    """
    if isinstance(required_roles, str):
        required_roles = [required_roles]
    
    required_roles_set = set(required_roles)
    
    async def check_roles(
        user: User = Depends(get_current_user),
        db: AsyncSession = Depends(get_db)
    ) -> User:
        # 超级管理员拥有所有角色权限
        if user.is_superuser:
            return user
        
        # 获取用户角色（带缓存）
        user_roles = await get_user_roles_cached(user, db)
        
        # 检查角色
        if require_all:
            # 必须拥有所有角色
            if not required_roles_set.issubset(user_roles):
                missing = required_roles_set - user_roles
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"角色不足。缺少以下角色: {', '.join(missing)}",
                )
        else:
            # 至少拥有一个角色
            if not required_roles_set.intersection(user_roles):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"角色不足。需要以下角色之一: {', '.join(required_roles_set)}",
                )
        
        return user
    
    return check_roles 
from typing import Optional, List, Dict, Any, AsyncGenerator, Union, Set
import uuid
from datetime import datetime
import random
import string
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from fastapi import Depends, Request, Response
from fastapi_users import BaseUserManager, FastAPIUsers, UUIDIDMixin
from fastapi_users.authentication import (
    AuthenticationBackend,
    BearerTransport,
    JWTStrategy,
)
from fastapi_users.db import SQLAlchemyUserDatabase

from db.session import get_db
from core.config import settings

# 导入模型（注意导入顺序以避免循环导入）
from models.user import User
from models.auth import OAuthAccount, LoginHistory

# 导入核心用户组件
from core.users import (
    fastapi_users,
    jwt_backend,
    current_active_user,
    current_superuser
)

# 用户数据库适配器
async def get_user_db(session: AsyncSession = Depends(get_db)) -> AsyncGenerator[SQLAlchemyUserDatabase, None]:
    """获取用户数据库适配器"""
    yield SQLAlchemyUserDatabase(session, User, OAuthAccount)

# 用户管理器
class UserManager(UUIDIDMixin, BaseUserManager[User, int]):
    """用户管理器"""
    reset_password_token_secret = settings.SECRET_KEY
    verification_token_secret = settings.SECRET_KEY

    async def on_after_register(self, user: User, request: Optional[Request] = None):
        """用户注册后的回调"""
        print(f"用户 {user.id} 注册成功")
        
        # 给用户分配默认角色
        from crud.crud_user import user as crud_user
        from sqlalchemy import text
        from db.session import AsyncSessionLocal
        from core.config import settings
        
        # 使用AsyncSessionLocal替代self.session
        async with AsyncSessionLocal() as session:
            try:
                # 使用原生SQL直接插入关系，避免ORM关系映射问题
                # 先查询默认角色ID
                result = await session.execute(
                    text("SELECT id FROM roles WHERE name = :role_name"),
                    {"role_name": "user"}  # 默认角色名
                )
                role_id = result.scalar_one_or_none()
                
                if role_id:
                    # 直接插入用户角色关系
                    await session.execute(
                        text("INSERT INTO user_role (user_id, role_id) VALUES (:user_id, :role_id)"),
                        {"user_id": user.id, "role_id": role_id}
                    )
                    await session.commit()
                    print(f"已为用户 {user.id} 分配默认角色")
                else:
                    print(f"未找到默认角色，无法分配")
            except Exception as e:
                await session.rollback()
                print(f"分配默认角色失败: {e}")

    async def on_after_login(
        self, user: User, request: Optional[Request] = None, response: Optional[Any] = None
    ):
        """用户登录后的回调"""
        # 更新最后登录时间
        user.last_login = datetime.now()
        self.user_db.session.add(user)
        await self.user_db.session.commit()
        
        # 创建登录历史记录
        if request:
            login_history = LoginHistory(
                user_id=user.id,
                login_method="password",  # 暂时硬编码，后续根据实际登录方式更新
                login_ip=request.client.host if request.client else None,
                user_agent=request.headers.get("User-Agent"),
                status=True
            )
            self.user_db.session.add(login_history)
            await self.user_db.session.commit()

    async def on_after_forgot_password(
        self, user: User, token: str, request: Optional[Request] = None
    ):
        """用户请求忘记密码后的回调"""
        print(f"用户 {user.id} 请求了密码重置，令牌: {token}")

    async def on_after_reset_password(self, user: User, request: Optional[Request] = None):
        """用户重置密码后的回调"""
        print(f"用户 {user.id} 重置了密码")

    async def on_after_request_verify(
        self, user: User, token: str, request: Optional[Request] = None
    ):
        """用户请求验证邮箱后的回调"""
        print(f"用户 {user.id} 请求了邮箱验证，令牌: {token}")

    async def on_after_verify(self, user: User, request: Optional[Request] = None):
        """用户验证邮箱后的回调"""
        print(f"用户 {user.id} 验证邮箱成功")
        
    async def get_by_username(self, username: str) -> Optional[User]:
        """通过用户名获取用户"""
        statement = select(User).where(User.username == username)
        result = await self.user_db.session.execute(statement)
        return result.scalar_one_or_none()
        
    async def get_by_phone(self, phone: str) -> Optional[User]:
        """通过手机号获取用户"""
        statement = select(User).where(User.phone == phone)
        result = await self.user_db.session.execute(statement)
        return result.scalar_one_or_none()
    
    async def create_oauth_user(self, 
                               provider: str, 
                               account_id: str, 
                               account_email: str, 
                               username: str = None) -> User:
        """
        创建 OAuth 用户
        
        Args:
            provider: OAuth 提供商
            account_id: 账号ID
            account_email: 账号邮箱
            username: 用户名，如果为空则随机生成
            
        Returns:
            创建的用户
        """
        # 生成随机用户名和密码(如果未提供)
        if not username:
            username = f"{provider}_{account_id}_{random.randint(1000, 9999)}"
        password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))
        
        # 创建用户
        user = await self.create(
            {
                "email": account_email,
                "username": username,
                "password": password,
                "is_active": True, 
                "is_verified": True,  # OAuth用户默认已验证
            }
        )
        
        # 添加OAuth账户
        oauth_account = OAuthAccount(
            user_id=user.id,
            oauth_name=provider,
            access_token="",  # 这里暂时设为空，在后续步骤中更新
            account_id=account_id,
            account_email=account_email,
        )
        self.user_db.session.add(oauth_account)
        await self.user_db.session.commit()
        
        return user

# 获取用户管理器
async def get_user_manager(user_db: SQLAlchemyUserDatabase = Depends(get_user_db)) -> AsyncGenerator[UserManager, None]:
    """获取用户管理器"""
    yield UserManager(user_db)

# 用于获取当前用户权限的依赖
async def get_user_permissions(user: User = Depends(current_active_user), db: AsyncSession = Depends(get_db)):
    """获取当前用户的所有权限"""
    from crud.crud_permission import permission
    
    # 如果是超级管理员，返回所有权限
    if user.is_superuser:
        all_permissions = await permission.get_multi(db)
        return {perm.name for perm in all_permissions}
    
    # 否则获取用户角色所拥有的权限
    user_permissions = await permission.get_user_permissions(db, user.id)
    return {perm.name for perm in user_permissions}

"""
用户认证相关的依赖项
"""

# 获取用户权限
async def get_user_permissions(user: User) -> Set[str]:
    """
    获取用户的所有权限
    """
    if user.is_superuser:
        # 超级管理员有所有权限
        from crud.crud_permission import permission
        from db.session import AsyncSessionLocal
        
        # 获取所有权限
        async with AsyncSessionLocal() as db:
            permissions = await permission.get_multi(db)
            return {p.name for p in permissions}
    
    # 普通用户从角色中获取权限
    permissions = set()
    for role in user.roles:
        for perm in role.permissions:
            permissions.add(perm.name)
    
    return permissions 
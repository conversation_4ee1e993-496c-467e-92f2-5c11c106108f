"""
认证相关异常
"""
from fastapi import status

class AuthError(Exception):
    """认证相关异常基类"""
    def __init__(self, detail: str, status_code: int = status.HTTP_400_BAD_REQUEST):
        self.detail = detail
        self.status_code = status_code
        super().__init__(detail)

class InvalidCredentialsError(AuthError):
    """无效的认证凭据"""
    def __init__(self, detail: str = "LOGIN_BAD_CREDENTIALS"):
        super().__init__(detail, status_code=status.HTTP_400_BAD_REQUEST)

class UserNotFoundError(AuthError):
    """用户不存在"""
    def __init__(self, detail: str = "用户不存在"):
        super().__init__(detail, status_code=status.HTTP_404_NOT_FOUND)

class TokenError(AuthError):
    """令牌错误"""
    def __init__(self, detail: str = "无效的令牌"):
        super().__init__(detail, status_code=status.HTTP_401_UNAUTHORIZED)

class ServiceNotEnabledError(AuthError):
    """服务未启用异常"""
    def __init__(self, service_name: str = ""):
        detail = f"服务未启用: {service_name}" if service_name else "服务未启用"
        super().__init__(detail, status_code=status.HTTP_403_FORBIDDEN) 
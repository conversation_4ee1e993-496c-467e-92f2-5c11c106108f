from typing import Dict, Optional, List, Any
import os
import logging
import json
from httpx_oauth.clients.github import GitHubOAuth2
from httpx_oauth.clients.google import GoogleOAuth2
from httpx_oauth.oauth2 import BaseOAuth2, OAuth2

from fastapi import Depends, Request
from fastapi_users.authentication import AuthenticationBackend, BearerTransport, JWTStrategy
from fastapi_users.authentication.strategy.db import (
    AccessTokenDatabase,
    DatabaseStrategy,
)

from sqlalchemy.ext.asyncio import AsyncSession
from db.session import get_db
from core.config import settings
from crud.crud_oauth_provider import get_active_oauth_providers
from sqlalchemy import select
from sqlalchemy.orm import joinedload
from datetime import datetime

# 日志记录器
logger = logging.getLogger(__name__)

# OAuth客户端字典，将在应用启动时加载
oauth_clients: Dict[str, BaseOAuth2] = {}

# OAuth认证传输
bearer_transport = BearerTransport(tokenUrl="api/auth/oauth/login")

# OAuth认证策略 - 使用JWT
def get_oauth_strategy() -> JWTStrategy:
    """获取OAuth认证策略"""
    return JWTStrategy(
        secret=settings.JWT_SECRET,
        lifetime_seconds=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    )

# OAuth认证后端
oauth_backend = AuthenticationBackend(
    name="oauth2",
    transport=bearer_transport,
    get_strategy=get_oauth_strategy,
)

async def load_oauth_clients(db: AsyncSession = None):
    """
    从数据库加载OAuth提供商配置并初始化OAuth客户端
    
    Args:
        db: 可选的数据库会话，如果未提供将创建新会话
    """
    global oauth_clients
    
    # 清空当前客户端字典
    oauth_clients = {}
    
    try:
        # 如果未提供数据库会话，则创建一个临时会话
        if db is None:
            async for session in get_db():
                db = session
                break
        
        # 从数据库获取所有激活的OAuth配置
        providers = await get_active_oauth_providers(db)
        
        for provider in providers:
            try:
                # 根据提供商名称创建不同的OAuth客户端
                if provider.provider_name.lower() == "github":
                    oauth_clients["github"] = GitHubOAuth2(
                        client_id=provider.client_id,
                        client_secret=provider.client_secret,
                    )
                elif provider.provider_name.lower() == "google":
                    oauth_clients["google"] = GoogleOAuth2(
                        client_id=provider.client_id,
                        client_secret=provider.client_secret,
                    )
                else:
                    # 对于其他提供商，尝试使用通用OAuth2客户端
                    # 这需要明确提供 authorize_url, token_url 和 user_info_url
                    if provider.authorize_url and provider.token_url:
                        scopes = []
                        if provider.scopes:
                            try:
                                # 尝试解析JSON格式的scopes
                                scopes = json.loads(provider.scopes)
                            except json.JSONDecodeError:
                                # 否则按逗号分隔处理
                                scopes = [s.strip() for s in provider.scopes.split(",")]
                        
                        oauth_clients[provider.provider_name] = OAuth2(
                            client_id=provider.client_id,
                            client_secret=provider.client_secret,
                            authorize_url=provider.authorize_url,
                            token_url=provider.token_url,
                            name=provider.provider_name,
                            base_scopes=scopes,
                        )
                
                logger.info(f"成功加载OAuth提供商: {provider.provider_name}")
            except Exception as e:
                logger.error(f"加载OAuth提供商 {provider.provider_name} 失败: {str(e)}")
        
        # 如果环境配置中启用了OAuth2，使用环境变量配置（向后兼容）
        if settings.OAUTH2_ENABLED:
            # 添加环境变量中配置的Google OAuth
            if settings.GOOGLE_CLIENT_ID and settings.GOOGLE_CLIENT_SECRET and "google" not in oauth_clients:
                oauth_clients["google"] = GoogleOAuth2(
                    settings.GOOGLE_CLIENT_ID, 
                    settings.GOOGLE_CLIENT_SECRET
                )
                logger.info("已从环境变量加载Google OAuth配置")
            
            # 添加环境变量中配置的GitHub OAuth
            if settings.GITHUB_CLIENT_ID and settings.GITHUB_CLIENT_SECRET and "github" not in oauth_clients:
                oauth_clients["github"] = GitHubOAuth2(
                    settings.GITHUB_CLIENT_ID, 
                    settings.GITHUB_CLIENT_SECRET
                )
                logger.info("已从环境变量加载GitHub OAuth配置")
        
        logger.info(f"OAuth客户端加载完成，共 {len(oauth_clients)} 个提供商")
        
    except Exception as e:
        logger.error(f"加载OAuth客户端失败: {str(e)}")

# 应用启动时监听器
def on_startup():
    """应用启动时调用以加载OAuth客户端"""
    import asyncio
    asyncio.create_task(load_oauth_clients())
    
# 获取所有OAuth提供商的信息
async def get_oauth_providers_info() -> List[Dict[str, Any]]:
    """
    获取所有已加载的OAuth提供商信息
    
    Returns:
        提供商信息列表
    """
    providers = []
    for name, client in oauth_clients.items():
        providers.append({
            "name": name,
            "authorize_url": client.authorize_url,
            "is_active": True,
        })
    return providers 

# 获取OAuth客户端，如果未初始化则尝试加载
async def get_oauth_client(provider_name: str, db: AsyncSession = None) -> Optional[BaseOAuth2]:
    """
    获取指定的OAuth客户端，如果客户端未初始化则尝试加载
    
    Args:
        provider_name: OAuth提供商名称
        db: 可选的数据库会话
        
    Returns:
        OAuth客户端实例，如果未找到返回None
    """
    global oauth_clients
    
    # 如果客户端已存在，直接返回
    if provider_name in oauth_clients:
        return oauth_clients[provider_name]
    
    # 尝试重新加载OAuth客户端
    try:
        # 如果未提供数据库会话，则创建一个临时会话
        close_db = False
        if db is None:
            close_db = True
            async for session in get_db():
                db = session
                break
        
        # 重新加载所有OAuth客户端
        await load_oauth_clients(db)
        
        # 检查是否成功加载了指定的客户端
        if provider_name in oauth_clients:
            return oauth_clients[provider_name]
        
        logger.warning(f"未找到OAuth提供商: {provider_name}")
        return None
        
    except Exception as e:
        logger.error(f"获取OAuth客户端失败: {str(e)}")
        return None
        
    finally:
        # 如果是我们创建的临时会话，关闭它
        if close_db and db is not None:
            await db.close()

# 验证OAuth访问令牌
async def validate_oauth_token(provider: str, access_token: str, db: AsyncSession = None) -> Optional[Dict]:
    """
    验证OAuth访问令牌并获取用户信息
    
    Args:
        provider: OAuth提供商名称
        access_token: 访问令牌
        db: 可选的数据库会话
        
    Returns:
        用户信息字典，如果验证失败返回None
    """
    # 获取OAuth客户端
    client = await get_oauth_client(provider, db)
    if not client:
        logger.error(f"未找到OAuth提供商: {provider}")
        return None
    
    try:
        # 获取用户信息
        if provider.lower() == "github":
            # GitHub特定处理
            user_info = await client.get_user_info(access_token)
            return {
                "id": str(user_info.get("id")),
                "email": user_info.get("email"),
                "name": user_info.get("name") or user_info.get("login"),
                "raw": user_info
            }
            
        elif provider.lower() == "google":
            # Google特定处理
            user_info = await client.get_user_info(access_token)
            return {
                "id": user_info.get("sub"),
                "email": user_info.get("email"),
                "name": user_info.get("name"),
                "raw": user_info
            }
            
        else:
            # 通用处理
            try:
                # 尝试使用标准方法
                user_data = await client.get_id_email_name(access_token)
                return {
                    "id": user_data.id,
                    "email": user_data.email,
                    "name": getattr(user_data, "name", None),
                    "raw": user_data
                }
            except Exception:
                # 尝试使用基本方法
                user_data = await client.get_id_email(access_token)
                return {
                    "id": user_data.id,
                    "email": user_data.email,
                    "raw": user_data
                }
    
    except Exception as e:
        logger.error(f"验证OAuth令牌失败: {str(e)}")
        return None

# 处理OAuth用户
async def handle_oauth_user(
    provider: str, 
    user_info: Dict, 
    db: AsyncSession = None,
    auto_create: bool = False
) -> Optional[Dict]:
    """
    处理OAuth用户信息，查找或创建用户
    
    Args:
        provider: OAuth提供商名称
        user_info: 用户信息字典
        db: 可选的数据库会话
        auto_create: 是否自动创建用户
        
    Returns:
        用户信息字典，如果处理失败返回None
    """
    # 如果未提供数据库会话，则创建一个临时会话
    close_db = False
    if db is None:
        close_db = True
        async for session in get_db():
            db = session
            break
    
    try:
        # 导入所需模型
        from models.user import User
        from models.auth import OAuthAccount
        
        # 获取用户标识
        account_id = user_info.get("id")
        account_email = user_info.get("email")
        
        if not account_id:
            logger.error("OAuth用户信息中缺少ID")
            return None
        
        # 查找OAuth账号关联的用户
        stmt = (
            select(User)
            .join(User.oauth_accounts)
            .options(joinedload(User.oauth_accounts))
            .where(
                OAuthAccount.oauth_name == provider,
                OAuthAccount.account_id == account_id
            )
        )
        
        result = await db.execute(stmt)
        user = result.unique().scalar_one_or_none()
        
        # 找到关联用户
        if user:
            # 更新OAuth账号信息
            for oauth_account in user.oauth_accounts:
                if oauth_account.oauth_name == provider and oauth_account.account_id == account_id:
                    oauth_account.updated_at = datetime.now()
                    db.add(oauth_account)
                    await db.commit()
                    break
            
            return {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "is_new": False
            }
        
        # 如果有邮箱，尝试查找使用相同邮箱的用户
        if account_email:
            stmt = (
                select(User)
                .options(joinedload(User.oauth_accounts))
                .where(User.email == account_email)
            )
            
            result = await db.execute(stmt)
            user = result.unique().scalar_one_or_none()
            
            # 找到相同邮箱的用户，添加OAuth关联
            if user:
                # 创建OAuth账号
                oauth_account = OAuthAccount(
                    user_id=user.id,
                    oauth_name=provider,
                    account_id=account_id,
                    account_email=account_email
                )
                
                # 添加关联
                user.oauth_accounts.append(oauth_account)
                db.add(user)
                await db.commit()
                
                return {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "is_new": False
                }
        
        # 如果允许自动创建用户
        if auto_create:
            # 生成用户名
            import random
            import string
            username = user_info.get("name", "")
            if not username:
                # 生成随机用户名
                random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
                username = f"user_{random_suffix}"
            else:
                # 处理用户名中的特殊字符
                username = ''.join(c for c in username if c.isalnum() or c == '_')
                if not username:
                    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
                    username = f"user_{random_suffix}"
            
            # 添加后缀确保唯一
            random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=4))
            username = f"{username}_{random_suffix}"
            
            # 创建用户
            from auth.users import UserManager, get_user_manager
            
            # 获取用户管理器
            user_manager = get_user_manager()
            
            # 准备用户数据
            user_data = {
                "username": username,
                "email": account_email or f"{username}@{provider.lower()}.user",
                "password": None,  # 不设置密码
                "is_active": True,
                "is_verified": True if account_email else False,
            }
            
            # 创建用户
            try:
                user = await user_manager.create_user(user_data)
                
                # 创建OAuth账号
                oauth_account = OAuthAccount(
                    user_id=user.id,
                    oauth_name=provider,
                    account_id=account_id,
                    account_email=account_email
                )
                
                # 添加关联
                user.oauth_accounts.append(oauth_account)
                db.add(user)
                await db.commit()
                
                return {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "is_new": True
                }
                
            except Exception as e:
                logger.error(f"创建OAuth用户失败: {str(e)}")
                return None
        
        # 不允许自动创建用户且未找到匹配用户
        logger.warning(f"未找到与OAuth账号匹配的用户且不允许自动创建")
        return None
        
    except Exception as e:
        logger.error(f"处理OAuth用户失败: {str(e)}")
        return None
        
    finally:
        # 如果是我们创建的临时会话，关闭它
        if close_db and db is not None:
            await db.close() 
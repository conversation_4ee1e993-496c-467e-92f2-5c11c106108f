from fastapi_users.authentication import AuthenticationBackend, BearerTransport, JWTStrategy

from core.config import settings

# Bearer传输
bearer_transport = BearerTransport(tokenUrl="api/auth/jwt/login")

# JWT策略
def get_jwt_strategy() -> JWTStrategy:
    """获取JWT认证策略"""
    return JWTStrategy(
        secret=settings.JWT_SECRET,
        lifetime_seconds=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    )

# JWT认证后端
jwt_backend = AuthenticationBackend(
    name="jwt",
    transport=bearer_transport,
    get_strategy=get_jwt_strategy,
) 
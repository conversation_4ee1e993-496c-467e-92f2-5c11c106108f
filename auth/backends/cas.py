from typing import Any, Dict, Optional, Tuple, Type
import httpx
import logging
from xml.etree import ElementTree
from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from fastapi_users.authentication import Strategy
from fastapi_users.authentication.backend import AuthenticationBackend
from fastapi_users.jwt import generate_jwt

from sqlalchemy.ext.asyncio import AsyncSession
from db.session import get_db
from auth.users import User, UserManager, get_user_manager
from core.config import settings
from crud.crud_cas_config import get_default_cas_config, get_cas_config_by_name

# 日志记录器
logger = logging.getLogger(__name__)

# CAS配置
cas_config = {
    "server_url": None,
    "service_url": None,
    "auto_create_user": False,
    "default_role": "user",
    "version": 3,
    "validate_cert": True,
}

# CAS认证传输
class CASBearerTransport(HTTPBearer):
    def __init__(self, tokenUrl: str):
        self.tokenUrl = tokenUrl
        super().__init__(auto_error=False)

# CAS认证策略
class CASStrategy(Strategy[User, int]):
    def __init__(
        self, 
        secret: str, 
        lifetime_seconds: int = 3600
    ):
        self.secret = secret
        self.lifetime_seconds = lifetime_seconds

    # 读取令牌
    async def read_token(
        self, token: Optional[str], user_manager: UserManager
    ) -> Optional[User]:
        """从CAS票据中读取用户"""
        if token is None:
            return None

        try:
            # 拆分令牌 (格式: "ticket:service")
            parts = token.split(":", 1)
            if len(parts) != 2:
                logger.warning("CAS令牌格式错误，应为 ticket:service")
                return None
                
            cas_ticket, service = parts
            
            # 验证CAS票据
            username = await validate_cas_ticket(cas_ticket, service)
            
            # 根据用户名查找用户
            user = await user_manager.get_by_username(username)
            
            # 如果用户不存在且配置允许自动创建，则创建新用户
            if user is None and cas_config.get("auto_create_user", False):
                logger.info(f"为CAS用户创建新账户: {username}")
                
                # 准备用户数据
                default_role = cas_config.get("default_role", "user")
                user_data = {
                    "username": username,
                    "email": f"{username}@cas.user",  # 使用临时邮箱
                    "password": None,  # 不设置密码
                    "is_active": True,
                    "is_verified": True,
                    "role": default_role,
                }
                
                # 创建用户
                # 这里使用create_user方法，如果有专门的create_cas_user方法，应该使用那个
                try:
                    user = await user_manager.create_user(user_data)
                    logger.info(f"已为CAS用户 {username} 创建新账户")
                except Exception as e:
                    logger.error(f"创建CAS用户失败: {str(e)}")
                    return None
            
            return user
                
        except Exception as e:
            logger.error(f"CAS认证过程出错: {str(e)}")
            return None

    # 写入令牌
    async def write_token(self, user: User) -> str:
        """生成JWT令牌"""
        data = {"sub": str(user.id), "aud": self.transport_name}
        return generate_jwt(
            data, self.secret, self.lifetime_seconds
        )

    # 获取策略名称
    @property
    def transport_name(self) -> str:
        """获取传输名称"""
        return "cas"

# 获取CAS认证策略
def get_cas_strategy() -> CASStrategy:
    """获取CAS认证策略"""
    return CASStrategy(
        secret=settings.JWT_SECRET,
        lifetime_seconds=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    )

# CAS认证传输
cas_transport = CASBearerTransport(tokenUrl="api/auth/cas/login")

# CAS认证后端
cas_backend = AuthenticationBackend(
    name="cas",
    transport=cas_transport,
    get_strategy=get_cas_strategy,
)

async def load_cas_config(db: AsyncSession = None, config_name: str = "default_cas"):
    """
    从数据库加载CAS配置
    
    Args:
        db: 可选的数据库会话，如果未提供将创建新会话
        config_name: 要加载的配置名称，默认为"default_cas"
    """
    global cas_config
    
    try:
        # 如果未提供数据库会话，则创建一个临时会话
        if db is None:
            async for session in get_db():
                db = session
                break
        
        # 尝试按名称获取配置
        config = await get_cas_config_by_name(db, config_name)
        
        # 如果未找到指定配置，尝试获取默认配置
        if not config:
            config = await get_default_cas_config(db)
        
        # 如果成功找到配置且配置已激活
        if config and config.is_active:
            cas_config = {
                "server_url": config.server_url,
                "service_url": config.service_url,
                "auto_create_user": config.auto_create_user,
                "default_role": config.default_role or "user",
                "version": config.version or 3,
                "validate_cert": config.validate_cert if config.validate_cert is not None else True,
            }
            logger.info(f"已加载CAS配置: {config.config_name}")
        # 如果未找到激活的配置但环境变量中有配置，使用环境变量配置（向后兼容）
        elif hasattr(settings, "CAS_ENABLED") and getattr(settings, "CAS_ENABLED", False) and hasattr(settings, "CAS_SERVER_URL"):
            cas_config = {
                "server_url": getattr(settings, "CAS_SERVER_URL", None),
                "service_url": getattr(settings, "CAS_SERVICE_URL", None),
                "auto_create_user": getattr(settings, "CAS_AUTO_CREATE_USER", False),
                "default_role": getattr(settings, "CAS_DEFAULT_ROLE", "user"),
                "version": getattr(settings, "CAS_VERSION", 3),
                "validate_cert": getattr(settings, "CAS_VALIDATE_CERT", True),
            }
            logger.info("已从环境变量加载CAS配置")
        else:
            logger.warning("未找到有效的CAS配置")
    except Exception as e:
        logger.error(f"加载CAS配置失败: {str(e)}")

# 应用启动时监听器
def on_startup():
    """应用启动时调用以加载CAS配置"""
    import asyncio
    asyncio.create_task(load_cas_config())

# 辅助函数：验证CAS票据
async def validate_cas_ticket(ticket: str, service: str = None, db: AsyncSession = None) -> str:
    """
    验证CAS票据
    
    Args:
        ticket: CAS票据
        service: 服务URL，默认使用配置的URL
        db: 数据库会话，可选
        
    Returns:
        CAS认证成功返回的用户名
        
    Raises:
        Exception: 票据验证失败
    """
    # 首先确保CAS配置已加载
    global cas_config
    
    # 如果未提供数据库会话，创建一个临时会话
    close_db = False
    if db is None:
        close_db = True
        async for session in get_db():
            db = session
            break
    
    try:
        # 检查配置是否完整
        cas_server_url = cas_config.get("server_url")
        cas_service_url = service or cas_config.get("service_url")
        
        if not cas_server_url or not cas_service_url:
            # 尝试重新加载配置
            await load_cas_config(db)
            
            # 再次检查
            cas_server_url = cas_config.get("server_url")
            cas_service_url = service or cas_config.get("service_url")
            
            if not cas_server_url or not cas_service_url:
                raise Exception("CAS配置不完整，无法验证票据")
        
        # 构建验证URL
        validate_url = f"{cas_server_url}/serviceValidate?ticket={ticket}&service={cas_service_url}"
        
        # 是否验证SSL证书
        verify_ssl = cas_config.get("validate_cert", True)
        
        logger.debug(f"CAS验证URL: {validate_url}")
        
        # 发送请求验证票据
        async with httpx.AsyncClient(verify=verify_ssl) as client:
            response = await client.get(validate_url)
            
            if response.status_code != 200:
                raise Exception(f"CAS验证请求失败: {response.status_code}")
            
            # 解析XML响应
            root = ElementTree.fromstring(response.text)
            namespace = "{http://www.yale.edu/tp/cas}"
            
            # 检查是否认证成功
            if root.find(f"{namespace}authenticationSuccess") is None:
                failure = root.find(f"{namespace}authenticationFailure")
                error_msg = "CAS认证失败"
                if failure is not None:
                    error_msg += f": {failure.text}"
                raise Exception(error_msg)
            
            # 获取用户名
            user_element = root.find(f"{namespace}authenticationSuccess/{namespace}user")
            if user_element is None or not user_element.text:
                raise Exception("CAS响应中未找到用户名")
            
            username = user_element.text
            logger.info(f"CAS认证成功: {username}")
            
            return username
    
    except Exception as e:
        logger.error(f"CAS票据验证失败: {str(e)}")
        raise
    
    finally:
        # 如果是我们创建的临时会话，关闭它
        if close_db and db:
            await db.close() 
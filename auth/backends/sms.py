from typing import Any, Dict, Op<PERSON>, Tu<PERSON>, Type
import time
import logging
from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from fastapi_users.authentication import Strategy
from fastapi_users.authentication.backend import AuthenticationBackend
from fastapi_users.jwt import generate_jwt

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from db.session import get_db
from auth.users import User, User<PERSON>anager, get_user_manager
from core.config import settings
from models.auth import VerificationCode
from crud.crud_sms_config import get_default_sms_config, get_sms_config_by_name
from datetime import datetime, timezone
import asyncio

# 日志记录器
logger = logging.getLogger(__name__)

# SMS配置
sms_config = {
    "provider": None,
    "access_key": None,
    "secret_key": None,
    "sign_name": None,
    "template_code": None,
    "auto_create_user": False,
    "code_expire_minutes": 10,
    "code_length": 6,
    "cooldown_seconds": 60,
}

# 短信验证码认证传输
class SMSBearerTransport(HTTPBearer):
    def __init__(self, tokenUrl: str):
        self.tokenUrl = tokenUrl
        super().__init__(auto_error=False)

# 短信验证码认证策略
class SMSStrategy(Strategy[User, int]):
    def __init__(self, secret: str, lifetime_seconds: int = 3600):
        self.secret = secret
        self.lifetime_seconds = lifetime_seconds

    # 读取用户
    async def read_token(
        self, token: Optional[str], user_manager: UserManager
    ) -> Optional[User]:
        """从令牌中读取用户"""
        if token is None:
            return None

        try:
            # 这里直接处理phone:code格式的令牌
            if ":" not in token:
                logger.warning("SMS令牌格式错误，应为 phone:code")
                return None
            
            phone, code = token.split(":", 1)
            logger.debug(f"验证短信验证码: 手机={phone}, 验证码={code}")
            
            # 验证验证码
            is_valid = await verify_sms_code(phone, code)
            if not is_valid:
                logger.warning(f"验证码不匹配或已过期: 手机={phone}")
                return None
                
            # 验证成功，根据手机号查找用户
            user = await user_manager.get_by_phone(phone)
            
            # 如果用户不存在且配置允许自动创建，则创建新用户
            global sms_config
            auto_create = sms_config.get("auto_create_user", False)
            
            if user is None and auto_create:
                logger.info(f"为SMS用户创建新账户: {phone}")
                
                # 生成随机用户名
                import random
                import string
                random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
                username = f"user_{random_suffix}"
                
                # 准备用户数据
                user_data = {
                    "username": username,
                    "email": f"{phone}@sms.user",  # 临时邮箱
                    "phone": phone,
                    "password": None,  # 不设置密码
                    "is_active": True,
                    "is_verified": True,
                }
                
                # 创建用户
                try:
                    user = await user_manager.create_user(user_data)
                    logger.info(f"已为SMS用户 {phone} 创建新账户")
                except Exception as e:
                    logger.error(f"创建SMS用户失败: {str(e)}")
                    return None
            
            return user
                
        except Exception as e:
            logger.error(f"SMS认证过程出错: {str(e)}")
            return None

    # 写入令牌
    async def write_token(self, user: User) -> str:
        """生成JWT令牌"""
        data = {"sub": str(user.id), "aud": self.transport_name}
        return generate_jwt(
            data, self.secret, self.lifetime_seconds
        )

    # 获取策略名称
    @property
    def transport_name(self) -> str:
        """获取传输名称"""
        return "sms"

# 获取短信认证策略
def get_sms_strategy() -> SMSStrategy:
    """获取短信验证码认证策略"""
    return SMSStrategy(
        secret=settings.JWT_SECRET,
        lifetime_seconds=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    )

# 短信认证传输
sms_transport = SMSBearerTransport(tokenUrl="api/auth/sms/login")

# 短信认证后端
sms_backend = AuthenticationBackend(
    name="sms",
    transport=sms_transport,
    get_strategy=get_sms_strategy,
)

async def load_sms_config(db: AsyncSession = None, config_name: str = "default_sms", max_retries: int = 3):
    """
    从数据库加载SMS配置
    
    Args:
        db: 可选的数据库会话，如果未提供将创建新会话
        config_name: 要加载的配置名称，默认为"default_sms"
        max_retries: 最大重试次数
    """
    global sms_config
    log_context = f"[SMS配置加载][{config_name}]"
    logger.info(f"{log_context} 开始加载短信配置")
    
    # 如果已经有配置，直接返回
    if sms_config.get("provider"):
        logger.debug(f"{log_context} 配置已存在，跳过加载")
        return
    
    # 创建新的数据库会话
    close_db = False
    session = None
    
    try:
        if db is None or not db.is_active:
            close_db = True
            from db.session import AsyncSessionLocal
            session = AsyncSessionLocal()
        else:
            session = db
            
        # 尝试加载配置
        from models.sms_config import SMSConfig
        from sqlalchemy import select
        
        # 首先尝试获取指定名称的配置
        stmt = select(SMSConfig).where(SMSConfig.config_name == config_name)
        result = await session.execute(stmt)
        config = result.scalar_one_or_none()
        
        # 如果未找到指定配置，尝试获取默认配置
        if not config:
            stmt = select(SMSConfig).where(SMSConfig.is_active == True)
            result = await session.execute(stmt)
            config = result.scalar_one_or_none()
        
        # 如果找到配置，更新全局配置
        if config and config.is_active:
            sms_config.update({
                "provider": config.provider,
                "access_key": config.access_key,
                "secret_key": config.secret_key,
                "sign_name": config.sign_name,
                "template_code": config.template_code,
                "app_id": config.app_id,
                "auto_create_user": config.auto_create_user,
                "code_expire_minutes": config.code_expire_minutes or 10,
                "code_length": config.code_length or 6,
                "cooldown_seconds": config.cooldown_seconds or 60,
            })
            logger.info(f"{log_context} 成功加载配置: {config.provider}")
        else:
            logger.warning(f"{log_context} 未找到有效的短信配置")
            
    except Exception as e:
        logger.error(f"{log_context} 加载配置失败: {str(e)}")
        
    finally:
        # 关闭自己创建的会话
        if close_db and session and session is not db:
            try:
                await session.close()
            except Exception as e:
                logger.error(f"{log_context} 关闭数据库会话失败: {str(e)}")

async def _load_from_database(db: AsyncSession, config_name: str) -> bool:
    """从数据库加载SMS配置"""
    global sms_config
    log_context = f"[SMS配置加载][{config_name}]"
    
    try:
        # 确保数据库会话有效
        if db.is_active:
            # 导入需要的模块和类
            from models.sms_config import SMSConfig
            from sqlalchemy import select
            
            # 尝试按名称获取配置
            logger.debug(f"{log_context} 尝试获取名为 '{config_name}' 的短信配置")
            stmt = select(SMSConfig).where(SMSConfig.config_name == config_name)
            
            try:
                # 使用事务独立的执行器，防止并发问题
                result = await db.execute(stmt)
                config = result.scalar_one_or_none()
            except Exception as e:
                logger.error(f"{log_context} 查询数据库时出错: {str(e)}", exc_info=True)
                return False
            
            # 如果未找到指定配置，尝试获取默认配置
            if not config:
                logger.debug(f"{log_context} 未找到名为 '{config_name}' 的配置，尝试获取默认(is_active=True)配置")
                stmt = select(SMSConfig).where(SMSConfig.is_active == True)
                
                try:
                    result = await db.execute(stmt) 
                    config = result.scalar_one_or_none()
                except Exception as e:
                    logger.error(f"{log_context} 查询默认配置时出错: {str(e)}", exc_info=True)
                    return False
            
            # 如果找到有效配置
            if config:
                if config.is_active:
                    # 使用字典保存配置，防止并发问题
                    sms_config_data = {
                        "provider": config.provider,
                        "access_key": config.access_key,
                        "secret_key": config.secret_key,
                        "sign_name": config.sign_name,
                        "template_code": config.template_code,
                        "app_id": config.app_id,
                        "auto_create_user": config.auto_create_user,
                        "code_expire_minutes": config.code_expire_minutes or 10,
                        "code_length": config.code_length or 6,
                        "cooldown_seconds": config.cooldown_seconds or 60,
                    }
                    
                    # 一次性更新全局配置
                    sms_config.update(sms_config_data)
                    
                    logger.info(f"{log_context} 已从数据库加载短信配置: {config.config_name} (提供商: {config.provider})")
                    return True
                else:
                    logger.warning(f"{log_context} 找到短信配置 '{config.config_name}'，但该配置未激活")
            else:
                logger.warning(f"{log_context} 数据库中未找到有效的短信配置")
        else:
            logger.error(f"{log_context} 提供的数据库会话无效")
            
        return False
    except Exception as e:
        logger.error(f"{log_context} 从数据库加载短信配置时出错: {str(e)}", exc_info=True)
        return False

async def _load_from_environment() -> bool:
    """从环境变量加载SMS配置"""
    global sms_config
    log_context = "[SMS配置加载][环境变量]"
    
    try:
        # 检查标准SMS配置
        if hasattr(settings, "SMS_ENABLED") and getattr(settings, "SMS_ENABLED", False):
            provider = getattr(settings, "SMS_PROVIDER", "aliyun")
            logger.debug(f"{log_context} 检测到环境变量中的通用短信配置，提供商: {provider}")
            
            sms_config = {
                "provider": provider,
                "access_key": getattr(settings, "SMS_ACCESS_KEY", None),
                "secret_key": getattr(settings, "SMS_SECRET_KEY", None),
                "sign_name": getattr(settings, "SMS_SIGN_NAME", None),
                "template_code": getattr(settings, "SMS_TEMPLATE_CODE", None),
                "auto_create_user": getattr(settings, "SMS_AUTO_CREATE_USER", False),
                "code_expire_minutes": getattr(settings, "SMS_CODE_EXPIRE_MINUTES", 10),
                "code_length": getattr(settings, "SMS_CODE_LENGTH", 6),
                "cooldown_seconds": getattr(settings, "SMS_COOLDOWN_SECONDS", 60),
            }
            logger.info(f"{log_context} 已从环境变量加载通用短信配置，提供商: {provider}")
            return True
        
        # 检查阿里云SMS配置
        if hasattr(settings, "ALIYUN_SMS_ENABLED") and getattr(settings, "ALIYUN_SMS_ENABLED", False):
            logger.debug(f"{log_context} 检测到环境变量中的阿里云短信配置")
            
            sms_config = {
                "provider": "aliyun",
                "access_key": getattr(settings, "ALIYUN_ACCESS_KEY_ID", None),
                "secret_key": getattr(settings, "ALIYUN_ACCESS_KEY_SECRET", None),
                "sign_name": getattr(settings, "ALIYUN_SMS_SIGN_NAME", None),
                "template_code": getattr(settings, "ALIYUN_SMS_TEMPLATE_CODE", None),
                "auto_create_user": getattr(settings, "SMS_AUTO_CREATE_USER", False),
                "code_expire_minutes": getattr(settings, "SMS_CODE_EXPIRE_MINUTES", 10),
                "code_length": 6,
                "cooldown_seconds": 60,
            }
            
            # 检查关键参数是否存在
            missing_params = []
            if not sms_config["access_key"]: missing_params.append("ALIYUN_ACCESS_KEY_ID")
            if not sms_config["secret_key"]: missing_params.append("ALIYUN_ACCESS_KEY_SECRET")
            if not sms_config["sign_name"]: missing_params.append("ALIYUN_SMS_SIGN_NAME")
            if not sms_config["template_code"]: missing_params.append("ALIYUN_SMS_TEMPLATE_CODE")
            
            if missing_params:
                logger.warning(f"{log_context} 阿里云短信配置不完整，缺少以下环境变量: {', '.join(missing_params)}")
                return False
                
            logger.info(f"{log_context} 已从环境变量加载阿里云短信配置")
            return True
        
        logger.debug(f"{log_context} 环境变量中未找到短信配置")
        return False
    except Exception as e:
        logger.error(f"{log_context} 从环境变量加载短信配置时出错: {str(e)}", exc_info=True)
        return False

# 应用启动时监听器
def on_startup():
    """应用启动时调用以加载SMS配置"""
    logger.info("[SMS配置加载] 应用启动时加载短信配置")
    # 使用create_task创建一个独立的异步任务
    asyncio.create_task(load_sms_config(max_retries=5))

# 发送SMS验证码
async def send_sms_code(phone: str, code: str, db: AsyncSession = None) -> bool:
    """
    发送短信验证码
    
    Args:
        phone: 手机号码
        code: 验证码
        db: 可选的数据库会话
        
    Returns:
        是否发送成功
    """
    # 日志上下文
    log_context = f"[短信发送][{phone}][{code}]"
    logger.info(f"{log_context} 开始发送短信验证码")
    
    # 确保SMS配置已加载
    global sms_config
    
    # 如果未提供数据库会话，创建一个临时会话
    close_db = False
    if db is None:
        try:
            close_db = True
            async for session in get_db():
                db = session
                break
        except Exception as e:
            logger.error(f"{log_context} 创建数据库会话失败: {str(e)}")
            return False
    
    try:
        # 确保配置已加载，如果未加载则从数据库加载
        if not sms_config.get("provider"):
            logger.info(f"{log_context} SMS配置未加载，开始从数据库加载配置...")
            await load_sms_config(db)
        
        provider = sms_config.get("provider")
        if not provider:
            # 如果仍然没有配置，记录错误并返回
            logger.error(f"{log_context} SMS配置不完整，无法发送验证码")
            return False
        
        logger.info(f"{log_context} 使用 {provider} 短信服务发送验证码")
        
        # 检查手机号格式
        import re
        if not re.match(r'^1[3-9]\d{9}$', phone):
            logger.error(f"{log_context} 手机号格式不正确")
            return False
        
        # 检查必要的凭据是否存在
        template_code = sms_config.get("template_code")
        sign_name = sms_config.get("sign_name")
        access_key = sms_config.get("access_key")
        secret_key = sms_config.get("secret_key")
        
        if not all([template_code, sign_name, access_key, secret_key]):
            missing_fields = []
            if not template_code: missing_fields.append("template_code")
            if not sign_name: missing_fields.append("sign_name")
            if not access_key: missing_fields.append("access_key")
            if not secret_key: missing_fields.append("secret_key")
            
            logger.error(f"{log_context} 短信配置不完整，缺少必要字段: {', '.join(missing_fields)}")
            return False
        
        # 根据不同的提供商发送短信
        if provider.lower() == "aliyun":
            # 导入阿里云SMS服务
            try:
                # 使用新的直接AK/SK方式发送，不再使用安全凭据方式
                from utils.aliyun_sms import send_sms_code as aliyun_send_sms
                
                # 准备模板参数
                template_param = {"code": code}
                
                # 记录配置信息用于调试
                logger.debug(f"{log_context} 阿里云短信配置: TemplateCode={template_code}, SignName={sign_name}")
                
                # 直接调用原始的阿里云发送方法
                result = await aliyun_send_sms(
                    phone_number=phone,
                    code=code,
                    template_param=template_param,
                    template_code=template_code,
                    sign_name=sign_name,
                    access_key_id=access_key,
                    access_key_secret=secret_key
                )
                
                if result.get("Code") == "OK":
                    logger.info(f"{log_context} 阿里云短信发送成功: RequestId={result.get('RequestId', '')}")
                    return True
                else:
                    error_code = result.get("Code", "Unknown")
                    error_message = result.get("Message", "无错误信息")
                    logger.error(f"{log_context} 阿里云短信发送失败: Code={error_code}, Message={error_message}")
                    
                    # 处理特定错误码
                    if error_code == "isv.BUSINESS_LIMIT_CONTROL":
                        logger.error(f"{log_context} 业务限流错误，可能是发送过于频繁或超过当日限制")
                    elif error_code == "isv.MOBILE_NUMBER_ILLEGAL":
                        logger.error(f"{log_context} 手机号不合法")
                    elif error_code == "SignatureDoesNotMatch":
                        logger.error(f"{log_context} 签名不匹配，请检查AccessKey和SecretKey")
                    elif error_code == "isv.SMS_SIGNATURE_ILLEGAL":
                        logger.error(f"{log_context} 签名不合法，请检查签名是否已通过审核或URL编码是否正确")
                        
                    return False
            except Exception as e:
                logger.error(f"{log_context} 阿里云短信发送异常: {str(e)}", exc_info=True)
                return False
                
        elif provider.lower() == "tencent":
            # TODO: 实现腾讯云SMS发送
            logger.warning(f"{log_context} 暂不支持腾讯云短信提供商")
            return False
            
        else:
            # 其他提供商
            logger.warning(f"{log_context} 暂不支持 {provider} 短信提供商")
            return False
    
    except Exception as e:
        logger.error(f"{log_context} 发送短信验证码失败: {str(e)}", exc_info=True)
        return False
        
    finally:
        # 如果是我们创建的临时会话，关闭它
        if close_db and db:
            try:
                await db.close()
            except Exception as e:
                logger.error(f"{log_context} 关闭数据库会话失败: {str(e)}")

# 生成随机验证码
async def generate_random_code(length: int = 6) -> str:
    """
    生成指定长度的随机数字验证码
    
    Args:
        length: 验证码长度
        
    Returns:
        随机数字验证码
    """
    import random
    return ''.join(random.choices('**********', k=length))

# 验证短信验证码
async def verify_sms_code(phone: str, code: str, db: AsyncSession = None) -> bool:
    """
    验证短信验证码
    
    Args:
        phone: 手机号码
        code: 验证码
        db: 可选的数据库会话
        
    Returns:
        验证码是否有效
    """
    log_context = f"[验证短信验证码][{phone}]"
    logger.info(f"{log_context} 开始验证验证码")
    
    # 如果未提供数据库会话，创建一个临时会话
    close_db = False
    session = None
    
    try:
        if db is None or not db.is_active:
            close_db = True
            logger.debug(f"{log_context} 创建新数据库会话进行验证")
            try:
                from db.session import AsyncSessionLocal
                session = AsyncSessionLocal()
                await session.connection()  # 提前检查连接是否有效
            except Exception as e:
                logger.error(f"{log_context} 创建数据库会话失败: {str(e)}", exc_info=True)
                return False
        else:
            session = db  # 使用提供的会话，但不关闭它
            logger.debug(f"{log_context} 使用提供的数据库会话")
    
        # 查询验证码记录
        from models.auth import VerificationCode
        from sqlalchemy import select, and_, or_, update
        from datetime import datetime, timezone
        
        # 构建查询条件
        conditions = [
            VerificationCode.phone == phone,
            VerificationCode.purpose == "login",
            VerificationCode.is_used == False,
            VerificationCode.expires_at > datetime.now(timezone.utc),
        ]
        
        # 查找未使用且未过期的验证码
        query = (
            select(VerificationCode)
            .where(and_(*conditions))
            .order_by(VerificationCode.created_at.desc())
            .limit(1)
        )
        
        try:
            result = await session.execute(query)
            verification = result.scalar_one_or_none()
            
            # 验证码无效
            if not verification:
                logger.warning(f"{log_context} 未找到有效的验证码记录")
                return False
                
            # 验证码不匹配
            if verification.code != code:
                logger.warning(f"{log_context} 验证码不匹配")
                return False
            
            # 标记验证码为已使用
            verification.is_used = True
            verification.used_at = datetime.now(timezone.utc)
            session.add(verification)
            
            # 使同一手机号下其他未过期的验证码失效
            invalidate_query = (
                update(VerificationCode)
                .where(
                    and_(
                        VerificationCode.phone == phone,
                        VerificationCode.purpose == "login",
                        VerificationCode.is_used == False,
                        VerificationCode.expires_at > datetime.now(timezone.utc),
                        VerificationCode.id != verification.id  # 排除当前验证码
                    )
                )
                .values(is_used=True, used_at=datetime.now(timezone.utc))
            )
            
            await session.execute(invalidate_query)
            await session.commit()
            
            logger.info(f"{log_context} 验证成功，已标记验证码为已使用")
            return True
            
        except Exception as db_error:
            logger.error(f"{log_context} 数据库操作失败: {str(db_error)}", exc_info=True)
            try:
                await session.rollback()
            except Exception as rollback_error:
                logger.error(f"{log_context} 回滚事务失败: {str(rollback_error)}")
            return False
        
    except Exception as e:
        logger.error(f"{log_context} 验证过程异常: {str(e)}", exc_info=True)
        return False
        
    finally:
        # 如果是我们创建的临时会话，关闭它
        if close_db and session is not None and session is not db:
            try:
                await session.close()
                logger.debug(f"{log_context} 已关闭临时数据库会话")
            except Exception as e:
                logger.error(f"{log_context} 关闭数据库会话失败: {str(e)}")

async def _handle_retry(attempt: int, max_retries: int, exception: Exception) -> bool:
    """处理重试逻辑，返回是否应继续重试"""
    log_context = "[SMS配置加载]"
    error_msg = f"加载短信配置失败 (尝试 {attempt+1}/{max_retries}): {str(exception)}"
    
    if attempt < max_retries - 1:
        logger.warning(f"{log_context} {error_msg}，将重试")
        # 短暂等待后重试
        await asyncio.sleep(1)
        return True
    
    logger.error(f"{log_context} {error_msg}，已达到最大重试次数")
    return False 
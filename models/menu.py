from sqlalchemy import Column, String, Inte<PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, DateTime, Text
from sqlalchemy.orm import relationship, backref
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID

from db.session import Base

class Menu(Base):
    """菜单模型"""
    __tablename__ = "menus"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, index=True)
    path = Column(String(255), nullable=False)
    component = Column(String(255), nullable=True)
    redirect = Column(String(255), nullable=True)
    title = Column(String(100), nullable=False)
    icon = Column(String(36), nullable=True, comment="图标文件UUID")
    parent_id = Column(Integer, ForeignKey("menus.id", ondelete="CASCADE"), nullable=True)
    sort_order = Column(Integer, default=0)
    is_hidden = Column(Boolean, default=False)
    is_cache = Column(<PERSON><PERSON><PERSON>, default=True)
    is_disabled = Column(Boolean, default=False)
    permission = Column(String(100), nullable=True, comment="菜单访问所需权限")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关联关系
    children = relationship("Menu", 
                           cascade="all, delete-orphan",
                           backref=backref("parent", remote_side=[id]),
                           uselist=True)
    
    # 图标关系 - 外键引用files表的id字段
    icon_file = relationship(
        "File", 
        foreign_keys=[icon],
        primaryjoin="Menu.icon == cast(File.id, String)",
        uselist=False,
        viewonly=True
    )
    
    def __repr__(self):
        return f"<Menu {self.title}>" 
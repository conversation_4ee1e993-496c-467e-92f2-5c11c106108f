from datetime import datetime
from typing import Optional
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, DateTime, Integer, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship

from db.base_class import Base

class APIClient(Base):
    """API客户端模型，用于第三方API访问认证"""
    
    __tablename__ = "api_clients"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 客户端标识信息
    name = Column(String(100), index=True, nullable=False, comment="客户端名称")
    description = Column(Text, nullable=True, comment="客户端描述")
    
    # 认证凭据
    client_id = Column(String(64), unique=True, index=True, nullable=False, comment="客户端ID")
    client_secret = Column(String(128), nullable=False, comment="客户端密钥")
    
    # 配置信息
    allowed_ips = Column(JSON, nullable=True, comment="允许的IP地址列表，为空表示不限制")
    scopes = Column(JSON, nullable=True, comment="允许的权限作用域")
    rate_limit = Column(Integer, default=60, comment="每分钟最大请求次数")
    
    # 状态信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    expires_at = Column(DateTime, nullable=True, comment="过期时间，为空表示永不过期")
    
    # 额外信息
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="创建者ID")
    created_by = relationship("User", back_populates="api_clients")
    
    # 使用统计
    request_count = Column(Integer, default=0, comment="总请求次数")
    last_used_at = Column(DateTime, nullable=True, comment="最后使用时间")
    
    def __repr__(self):
        return f"<APIClient {self.name} ({self.client_id})>" 
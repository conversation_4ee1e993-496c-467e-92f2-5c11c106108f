from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, Integer, ForeignKey, Table, DateTime, func, TIMESTAMP, UniqueConstraint
from sqlalchemy.orm import relationship
from fastapi_users.db import SQLAlchemyBaseUserTable
from sqlalchemy.dialects.postgresql import UUID

from db.session import Base
from models.oauth import OAuthAccount  # 导入统一的OAuthAccount模型

# 使用多重继承方式集成FastAPI-Users的用户模型
class User(SQLAlchemyBaseUserTable[int], Base):
    """用户模型，集成FastAPI-Users"""
    __tablename__ = "users"
    
    # 覆盖id字段，保持和原来相同的配置
    id = Column(Integer, primary_key=True, index=True)
    
    # FastAPI-Users需要的字段在SQLAlchemyBaseUserTable中已有:
    # email, hashed_password, is_active, is_superuser, is_verified
    
    # 添加自定义字段
    username = Column(String(50), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=True)
    phone = Column(String(20), unique=True, index=True, nullable=True)
    avatar = Column(String(36), nullable=True, comment="头像文件UUID") # 修改为存储文件ID (UUID格式)
    last_login = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(TIMESTAMP(timezone=True), server_default=func.now())
    updated_at = Column(TIMESTAMP(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 保留角色关系
    roles = relationship("Role", secondary="user_role", lazy="selectin")
    
    # OAuth账户关系，适配FastAPI-Users
    oauth_accounts = relationship(
        OAuthAccount, back_populates="user", cascade="all, delete-orphan", lazy="selectin"
    )
    
    # 登录历史关系
    login_history = relationship(
        "LoginHistory", back_populates="user", cascade="all, delete-orphan", lazy="selectin"
    )
    
    # 审计日志关系 - 使用back_populates建立双向关系
    audit_logs = relationship(
        "AuditLog", back_populates="user", cascade="all, delete-orphan", lazy="selectin"
    )
    
    # API客户端关系
    api_clients = relationship(
        "APIClient", back_populates="created_by", cascade="all, delete-orphan", lazy="selectin"
    )
    
    # 文件关系 - 删除用户时不自动删除文件
    files = relationship(
        "File", back_populates="owner", cascade="save-update, merge, refresh-expire", lazy="selectin"
    )
    
    # 头像关系 - 外键引用files表的id字段
    avatar_file = relationship(
        "File", 
        foreign_keys=[avatar],
        primaryjoin="User.avatar == cast(File.id, String)",
        uselist=False,
        viewonly=True
    )
    
    def __repr__(self):
        return f"<User {self.username}>"

# 移除重复定义的OAuthAccount类，使用从models.oauth导入的类 
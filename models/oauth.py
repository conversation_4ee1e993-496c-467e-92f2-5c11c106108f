"""
OAuth账号模型定义
将OAuth账号模型单独提取到一个文件中，避免重复定义和循环导入
"""
from sqlalchemy import Column, String, Integer, ForeignKey, DateTime, func
from sqlalchemy.orm import relationship, Mapped, mapped_column
from typing import Optional
from datetime import datetime
from fastapi_users.db import SQLAlchemyBaseOAuthAccountTable

from db.session import Base

# OAuth账号模型
class OAuthAccount(SQLAlchemyBaseOAuthAccountTable[int], Base):
    """
    OAuth账号表
    
    用于存储第三方OAuth授权账号信息，如GitHub、Google等登录方式关联的账号
    """
    __tablename__ = "oauth_accounts"
    __table_args__ = {'extend_existing': True}
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    user_id: Mapped[int] = mapped_column(Integer, ForeignKey("users.id", ondelete="CASCADE"), index=True)
    oauth_name: Mapped[str] = mapped_column(String(length=100), index=True)
    access_token: Mapped[str] = mapped_column(String(length=1024))
    expires_at: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    refresh_token: Mapped[Optional[str]] = mapped_column(String(length=1024), nullable=True)
    account_id: Mapped[str] = mapped_column(String(length=320), index=True)
    account_email: Mapped[str] = mapped_column(String(length=320))
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关联用户（在user.py中设置反向关系）
    user = relationship("User", back_populates="oauth_accounts")
    
    def __repr__(self):
        return f"<OAuthAccount {self.oauth_name}:{self.account_id}>" 
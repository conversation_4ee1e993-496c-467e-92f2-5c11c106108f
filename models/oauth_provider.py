#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OAuth提供商配置数据库模型
"""
from sqlalchemy import Column, String, Boolean, Text, Integer
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from db.session import Base

class OAuthProviderConfig(Base):
    """OAuth提供商配置模型"""
    __tablename__ = "oauth_provider_configs"

    id = Column(Integer, primary_key=True, index=True)
    provider_name = Column(String(50), unique=True, index=True, nullable=False, comment="提供商名称 (e.g., github, google)")
    client_id = Column(String(255), nullable=False, comment="Client ID")
    client_secret = Column(String(255), nullable=False, comment="Client Secret")
    authorize_url = Column(String(512), nullable=True, comment="授权URL")
    token_url = Column(String(512), nullable=True, comment="获取令牌URL")
    user_info_url = Column(String(512), nullable=True, comment="获取用户信息URL")
    jwks_uri = Column(String(512), nullable=True, comment="JWKS URI (用于OIDC)")
    scopes = Column(Text, nullable=True, comment="授权范围 (逗号分隔或JSON列表)")
    icon = Column(String(36), nullable=True, comment="提供商图标文件UUID")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    description = Column(Text, nullable=True, comment="描述")
    
    created_at = Column(String(255), server_default=func.now(), comment="创建时间")
    updated_at = Column(String(255), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 图标关系 - 外键引用files表的id字段
    icon_file = relationship(
        "File", 
        foreign_keys=[icon],
        primaryjoin="OAuthProviderConfig.icon == cast(File.id, String)",
        uselist=False,
        viewonly=True
    )

    def __repr__(self):
        return f"<OAuthProviderConfig {self.provider_name}>" 
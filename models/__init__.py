"""Models模块初始化

按照正确顺序导入模型，避免循环依赖
"""
# 避免循环导入，不在这里导入任何模型

# 导入所有数据库模型以确保SQLAlchemy能正确解析它们之间的关系
from models.user import User
from models.role import Role
from models.menu import Menu
from models.oauth import OAuthAccount
from models.config import Config
from models.auth import VerificationCode, LoginHistory
from models.cas_config import CASConfig
from models.sms_config import SMSConfig 
from models.oauth_provider import OAuthProviderConfig 
from models.audit_log import AuditLog
from models.file import File
from models.api_client import APIClient

# 避免循环导入，不在这里导入任何模型
# 请在需要使用模型的地方直接导入具体模型
# 例如: from models.user import User
# 例如: from models.role import Role 
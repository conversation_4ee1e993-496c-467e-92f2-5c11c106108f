"""
认证相关模型
包含验证码、登录历史等模型
"""
from sqlalchemy import Column, String, Integer, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from typing import Optional, List
from datetime import datetime

from db.session import Base
from models.oauth import OAuthAccount  # 从oauth.py导入OAuthAccount，避免重复定义

# 验证码模型
class VerificationCode(Base):
    """验证码模型"""
    __tablename__ = "verification_codes"
    
    id = Column(Integer, primary_key=True, index=True)
    phone = Column(String(20), nullable=True, index=True)
    email = Column(String(100), nullable=True, index=True)
    code = Column(String(10), nullable=False)
    purpose = Column(String(20), nullable=False, comment="用途，如login, register, reset_password等")
    is_used = Column(Boolean, default=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<VerificationCode {self.id}>"

# 登录历史模型
class LoginHistory(Base):
    """登录历史模型"""
    __tablename__ = "login_history"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    login_method = Column(String(20), nullable=False, comment="登录方式，如password, oauth2, cas, sms等")
    login_ip = Column(String(50), nullable=True)
    user_agent = Column(Text, nullable=True)
    status = Column(Boolean, default=True, comment="登录是否成功")
    message = Column(String(255), nullable=True, comment="登录失败原因")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 简化引用方式
    user = relationship("User", back_populates="login_history")
    
    def __repr__(self):
        return f"<LoginHistory {self.user_id}>" 
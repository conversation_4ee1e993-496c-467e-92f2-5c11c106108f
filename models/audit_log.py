from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Boolean, JSON, func, Float
from sqlalchemy.orm import relationship
from datetime import datetime

from db.session import Base
# 避免直接导入User模型造成的循环导入问题

class AuditLog(Base):
    """操作审计日志模型，记录所有用户操作"""
    __tablename__ = "audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    username = Column(String(50), nullable=True, index=True)
    
    # 操作信息
    action = Column(String(50), nullable=False, index=True)  # 操作类型：create, update, delete, read
    resource = Column(String(50), nullable=False, index=True)  # 资源类型：user, role, etc.
    resource_id = Column(String(50), nullable=True, index=True)  # 资源ID
    
    # 请求信息
    method = Column(String(10), nullable=False)  # HTTP方法
    path = Column(String(255), nullable=False)  # 请求路径
    query_params = Column(JSON, nullable=True)  # 查询参数
    request_body = Column(JSON, nullable=True)  # 请求体内容
    response_code = Column(Integer, nullable=True)  # 响应状态码
    
    # IP和用户代理
    ip_address = Column(String(50), nullable=True, index=True)
    user_agent = Column(String(255), nullable=True)
    
    # 请求性能
    execution_time = Column(Float, nullable=True)  # 执行时间(秒)
    
    # 详细信息
    details = Column(JSON, nullable=True)  # 其他详细信息，JSON格式
    message = Column(Text, nullable=True)  # 操作描述
    
    # 成功标志
    success = Column(Boolean, default=True, nullable=False, index=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    
    # 关系 - 使用back_populates并添加overlaps参数
    user = relationship("User", back_populates="audit_logs", overlaps="audit_logs")
    
    @classmethod
    def create_log(cls, **kwargs):
        """创建日志记录工厂方法"""
        return cls(**kwargs)
        
    def __repr__(self):
        return f"<AuditLog id={self.id} user_id={self.user_id} action={self.action} resource={self.resource}>" 
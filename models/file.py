import uuid
from datetime import datetime

from sqlalchemy import Column, String, Integer, DateTime, Boolean, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from db.base_class import Base


class File(Base):
    """文件数据模型，存储上传文件的元数据信息"""
    
    __tablename__ = "files"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    filename = Column(String(255), nullable=False, comment="文件名")
    original_filename = Column(String(255), nullable=False, comment="原始文件名")
    storage_path = Column(String(512), nullable=False, comment="存储路径")
    file_url = Column(String(512), nullable=False, comment="访问URL")
    content_type = Column(String(128), nullable=True, comment="内容类型")
    size = Column(Integer, nullable=False, default=0, comment="文件大小(字节)")
    file_hash = Column(String(64), nullable=True, index=True, comment="文件哈希值")
    
    # 文件分类和组织
    category = Column(String(64), nullable=True, index=True, comment="文件分类")
    tags = Column(JSONB, nullable=True, comment="标签列表")
    
    # 访问控制
    is_public = Column(Boolean, default=False, comment="是否公开访问")
    access_key = Column(String(64), nullable=True, comment="访问密钥(用于非公开文件)")
    
    # 用户关联
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="文件所有者")
    owner = relationship("User", back_populates="files")
    
    # 存储信息
    storage_type = Column(String(20), default="local", comment="存储类型: local, minio")
    
    # 状态和扩展数据
    status = Column(String(20), default="active", comment="文件状态: active, deleted, archived")
    file_metadata = Column(JSONB, nullable=True, comment="额外元数据")
    description = Column(Text, nullable=True, comment="文件描述")
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    expires_at = Column(DateTime, nullable=True, comment="过期时间")
    
    def __repr__(self):
        return f"<File {self.filename}>" 
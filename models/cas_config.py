#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CAS服务配置数据库模型
"""
from sqlalchemy import Column, String, Boolean, Integer
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from db.session import Base

class CASConfig(Base):
    """CAS服务配置模型"""
    __tablename__ = "cas_configs"

    id = Column(Integer, primary_key=True, index=True)
    config_name = Column(String(50), unique=True, index=True, nullable=False, default="default_cas", comment="配置名称，默认为default_cas")
    server_url = Column(String(512), nullable=False, comment="CAS服务器URL")
    service_url = Column(String(512), nullable=False, comment="应用回调服务URL")
    version = Column(Integer, default=3, nullable=False, comment="CAS协议版本 (2或3)")
    auto_create_user = Column(Boolean, default=True, comment="是否自动创建不存在的用户")
    default_role = Column(String(50), default="user", comment="自动创建用户时分配的角色")
    validate_cert = Column(Boolean, default=True, comment="是否验证CAS服务器的SSL证书")
    is_active = Column(Boolean, default=False, nullable=False, comment="是否激活此CAS配置")
    description = Column(String(255), nullable=True, comment="描述")
    icon = Column(String(36), nullable=True, comment="服务图标文件UUID")
    
    created_at = Column(String(255), server_default=func.now(), comment="创建时间")
    updated_at = Column(String(255), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 图标关系 - 外键引用files表的id字段
    icon_file = relationship(
        "File", 
        foreign_keys=[icon],
        primaryjoin="CASConfig.icon == cast(File.id, String)",
        uselist=False,
        viewonly=True
    )

    def __repr__(self):
        return f"<CASConfig {self.config_name}>" 
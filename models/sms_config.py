#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
短信服务配置数据库模型
"""
from sqlalchemy import Column, String, Boolean, Integer
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from db.session import Base

class SMSConfig(Base):
    """短信服务配置模型"""
    __tablename__ = "sms_configs"

    id = Column(Integer, primary_key=True, index=True)
    config_name = Column(String(50), unique=True, index=True, nullable=False, default="default_sms", comment="配置名称，默认为default_sms")
    provider = Column(String(50), nullable=False, comment="短信服务提供商 (e.g., aliyun, tencent)")
    access_key = Column(String(255), nullable=False, comment="Access Key ID")
    secret_key = Column(String(255), nullable=False, comment="Secret Key")
    sign_name = Column(String(100), nullable=True, comment="短信签名")
    template_code = Column(String(100), nullable=True, comment="短信模板代码")
    app_id = Column(String(100), nullable=True, comment="应用ID (例如腾讯云需要)") # 腾讯云等可能需要
    auto_create_user = Column(Boolean, default=True, comment="是否自动创建不存在的用户")
    code_expire_minutes = Column(Integer, default=10, comment="验证码有效期（分钟）")
    code_length = Column(Integer, default=6, comment="验证码长度")
    cooldown_seconds = Column(Integer, default=60, comment="验证码发送冷却时间（秒）")
    is_active = Column(Boolean, default=False, nullable=False, comment="是否激活此短信配置")
    description = Column(String(255), nullable=True, comment="描述")
    icon = Column(String(36), nullable=True, comment="服务图标文件UUID")
    
    created_at = Column(String(255), server_default=func.now(), comment="创建时间")
    updated_at = Column(String(255), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 图标关系 - 外键引用files表的id字段
    icon_file = relationship(
        "File", 
        foreign_keys=[icon],
        primaryjoin="SMSConfig.icon == cast(File.id, String)",
        uselist=False,
        viewonly=True
    )

    def __repr__(self):
        return f"<SMSConfig {self.config_name} ({self.provider})>" 
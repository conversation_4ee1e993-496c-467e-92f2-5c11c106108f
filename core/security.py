from datetime import datetime, timedelta,timezone
import random
import string
from typing import Any, Dict, Optional, Tuple, Union
import secrets
import base64

from jose import JWTError, jwt
from passlib.context import CryptContext

from core.config import settings
from core.exceptions import AuthenticationException

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt", "argon2"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码，支持多种哈希算法"""
    try:
        if hashed_password.startswith("$argon2id$"):
            # 处理argon2id格式哈希
            try:
                from argon2 import PasswordHasher
                from argon2.exceptions import VerifyMismatchError, InvalidHash
                
                ph = PasswordHasher()
                try:
                    # 注意：argon2的verify方法参数顺序是(hash, password)
                    ph.verify(hashed_password, plain_password)
                    return True
                except (VerifyMismatchError, InvalidHash) as e:
                    print(f"argon2验证失败: {e}")
                    return False
            except ImportError as e:
                print(f"argon2库导入失败: {e}")
                return False
        else:
            # 其他格式哈希，使用passlib验证
            return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        print(f"密码验证错误: {e}")
        return False

def get_password_hash(password: str) -> str:
    """获取密码哈希值，统一使用bcrypt"""
    return pwd_context.hash(password)

def generate_tokens(subject: Union[str, Any], additional_data: Dict[str, Any] = None) -> Tuple[str, str]:
    """
    生成访问令牌和刷新令牌
    
    Args:
        subject: 令牌主题（通常是用户ID）
        additional_data: 要加入令牌的额外数据
        
    Returns:
        (access_token, refresh_token): 访问令牌和刷新令牌
    """
    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data=subject,
        expires_delta=access_token_expires
    )

    # 创建刷新令牌
    refresh_token = create_refresh_token(
        data=subject
    )
    
    return access_token, refresh_token

def create_access_token(data: str, expires_delta: Optional[timedelta] = None) -> str:
    """
    创建访问令牌
    
    Args:
        data: 用户ID
        expires_delta: 过期时间增量
        
    Returns:
        JWT令牌
    """
    to_encode = {
        "sub": str(data),  # 确保是字符串
        "type": "access",
        "iat": datetime.now(timezone.utc)
    }
    
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    
    try:
        encoded_jwt = jwt.encode(
            to_encode, 
            settings.JWT_SECRET, 
            algorithm=settings.JWT_ALGORITHM
        )
        return encoded_jwt
    except Exception as e:
        raise AuthenticationException(detail=f"令牌生成失败: {str(e)}")

def create_refresh_token(data: str, expires_delta: Optional[timedelta] = None) -> str:
    """
    创建刷新令牌
    
    Args:
        data: 用户ID
        expires_delta: 过期时间增量
        
    Returns:
        JWT令牌
    """
    to_encode = {
        "sub": str(data),  # 确保是字符串
        "type": "refresh",
        "iat": datetime.now(timezone.utc)
    }
    
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    
    to_encode.update({"exp": expire})
    
    try:
        encoded_jwt = jwt.encode(
            to_encode, 
            settings.JWT_SECRET, 
            algorithm=settings.JWT_ALGORITHM
        )
        return encoded_jwt
    except Exception as e:
        raise AuthenticationException(detail=f"令牌生成失败: {str(e)}")

def decode_token(token: str) -> Dict[str, Any]:
    """
    解码JWT令牌
    
    Args:
        token: JWT令牌
        
    Returns:
        解码后的JWT数据
        
    Raises:
        AuthenticationException: 令牌无效或已过期
    """
    try:
        # 使用相同的密钥 JWT_SECRET_KEY 进行解码
        payload = jwt.decode(
            token, 
            settings.JWT_SECRET,  # 使用与加密相同的密钥
            algorithms=[settings.JWT_ALGORITHM]
        )
        
        # 确保返回的是字典格式
        if isinstance(payload, dict):
            return payload
        elif isinstance(payload, str):
            # 如果是字符串，尝试解析为字典
            try:
                import json
                return json.loads(payload)
            except:
                return {"sub": payload}
        else:
            # 其他类型，构造标准格式
            return {"sub": str(payload)}
            
    except JWTError as e:
        raise AuthenticationException(detail=f"无效的令牌: {str(e)}")
    except Exception as e:
        raise AuthenticationException(detail=f"令牌解析错误: {str(e)}")

def generate_random_code(length: int = 6) -> str:
    """
    生成随机数字验证码
    
    Args:
        length: 验证码长度
        
    Returns:
        随机生成的验证码
    """
    return ''.join(random.choices(string.digits, k=length))

def generate_random_password(length: int = 12) -> str:
    """
    生成随机安全密码
    
    Args:
        length: 密码长度
        
    Returns:
        随机生成的密码
    """
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*()-_=+"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def encrypt_data(data: str) -> str:
    """
    简单加密数据
    
    Args:
        data: 要加密的数据
        
    Returns:
        加密后的字符串
    """
    # 简单的base64编码和混淆
    key = settings.SECRET_KEY[:8]
    encoded = base64.b64encode((data + key).encode()).decode()
    return encoded

def decrypt_data(encrypted: str) -> str:
    """
    解密数据
    
    Args:
        encrypted: 加密的数据
        
    Returns:
        解密后的字符串
    """
    try:
        key = settings.SECRET_KEY[:8]
        decoded = base64.b64decode(encrypted.encode()).decode()
        if decoded.endswith(key):
            return decoded[:-len(key)]
        return ""
    except Exception:
        return "" 

from typing import Any, Dict, Optional, Union
from fastapi import FastAPI, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from fastapi.exception_handlers import request_validation_exception_handler
from starlette.exceptions import HTTPException as StarletteHTTPException
import traceback
import logging

from schemas.response import ResponseCode, RESPONSE_MESSAGES, error_response

# 配置日志
logger = logging.getLogger(__name__)

# 业务异常基类
class BusinessException(Exception):
    """业务异常基类"""
    def __init__(
        self, 
        code: str, 
        msg: Optional[str] = None,
        detail: Optional[str] = None,
        status_code: int = status.HTTP_400_BAD_REQUEST
    ):
        """
        初始化业务异常
        
        Args:
            code: 错误码
            msg: 错误信息，默认使用错误码对应的标准信息
            detail: 详细错误信息
            status_code: HTTP状态码
        """
        self.code = code
        self.msg = msg or RESPONSE_MESSAGES.get(code) or "业务处理异常"
        self.detail = detail
        self.status_code = status_code
        super().__init__(self.msg)

# 参数校验异常
class ValidationException(BusinessException):
    """参数校验异常"""
    def __init__(self, detail: Optional[str] = None):
        super().__init__(
            code=ResponseCode.PARAM_ERROR,
            detail=detail,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY
        )

# 认证异常
class AuthenticationException(BusinessException):
    """认证异常"""
    def __init__(self, detail: Optional[str] = None):
        super().__init__(
            code=ResponseCode.AUTH_ERROR,
            detail=detail,
            status_code=status.HTTP_401_UNAUTHORIZED
        )

# 权限异常
class PermissionException(BusinessException):
    """权限异常"""
    def __init__(self, detail: Optional[str] = None):
        super().__init__(
            code=ResponseCode.PERMISSION_ERROR,
            detail=detail,
            status_code=status.HTTP_403_FORBIDDEN
        )

# 资源不存在异常
class NotFoundException(BusinessException):
    """资源不存在异常"""
    def __init__(self, detail: Optional[str] = None):
        super().__init__(
            code=ResponseCode.NOT_FOUND,
            detail=detail,
            status_code=status.HTTP_404_NOT_FOUND
        )

# 资源已存在异常
class DuplicateException(BusinessException):
    """资源已存在异常"""
    def __init__(self, detail: Optional[str] = None):
        super().__init__(
            code=ResponseCode.DUPLICATE,
            detail=detail,
            status_code=status.HTTP_409_CONFLICT
        )

# 系统异常
class SystemException(BusinessException):
    """系统异常"""
    def __init__(self, detail: Optional[str] = None):
        super().__init__(
            code=ResponseCode.SYSTEM_ERROR,
            detail=detail,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

# 服务调用异常
class ServiceException(BusinessException):
    """服务调用异常"""
    def __init__(self, detail: Optional[str] = None):
        super().__init__(
            code=ResponseCode.SERVICE_ERROR,
            detail=detail,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE
        )

# 异常处理函数
async def business_exception_handler(request: Request, exc: BusinessException) -> JSONResponse:
    """
    处理业务异常，返回统一格式的错误响应
    """
    logger.error(f"业务异常: {exc.msg}, 详情: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=exc.code,
            msg=exc.msg,
            detail=exc.detail,
            path=request.url.path
        )
    )

async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """
    处理请求参数验证异常，返回统一格式的错误响应
    """
    # 格式化验证错误信息
    error_details = []
    for error in exc.errors():
        loc = " -> ".join(str(x) for x in error["loc"])
        error_details.append(f"位置 [{loc}]: {error['msg']}")
    
    detail = "；".join(error_details)
    logger.warning(f"参数验证失败: {detail}")
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response(
            code=ResponseCode.PARAM_ERROR,
            detail=detail,
            path=request.url.path
        )
    )

async def http_exception_handler(request: Request, exc: StarletteHTTPException) -> JSONResponse:
    """
    处理HTTP异常，返回统一格式的错误响应
    """
    # 根据状态码映射到业务错误码
    code_map = {
        status.HTTP_401_UNAUTHORIZED: ResponseCode.AUTH_ERROR,
        status.HTTP_403_FORBIDDEN: ResponseCode.PERMISSION_ERROR,
        status.HTTP_404_NOT_FOUND: ResponseCode.NOT_FOUND,
        status.HTTP_409_CONFLICT: ResponseCode.DUPLICATE,
        status.HTTP_422_UNPROCESSABLE_ENTITY: ResponseCode.PARAM_ERROR,
        status.HTTP_500_INTERNAL_SERVER_ERROR: ResponseCode.SYSTEM_ERROR,
        status.HTTP_503_SERVICE_UNAVAILABLE: ResponseCode.SERVICE_ERROR,
    }
    
    code = code_map.get(exc.status_code, "B9999")  # 默认未分类错误码
    logger.warning(f"HTTP异常 {exc.status_code}: {exc.detail}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response(
            code=code,
            msg=exc.detail,
            path=request.url.path
        )
    )

async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    处理未捕获的异常，返回统一格式的错误响应
    """
    # 记录详细错误信息和堆栈跟踪
    error_msg = f"未捕获的异常: {str(exc)}"
    stack_trace = traceback.format_exc()
    logger.error(f"{error_msg}\n{stack_trace}")
    
    # 开发环境返回详细错误，生产环境返回通用错误
    from core.config import settings
    detail = str(exc) if settings.DEBUG else None
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response(
            code=ResponseCode.SYSTEM_ERROR,
            detail=detail,
            path=request.url.path
        )
    )

# 添加异常处理器
def setup_exception_handlers(app: FastAPI) -> None:
    """
    设置全局异常处理器
    
    Args:
        app: FastAPI应用实例
    """
    # 处理业务异常
    app.add_exception_handler(BusinessException, business_exception_handler)
    
    # 处理FastAPI验证异常
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    
    # 处理HTTPException
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    
    # 处理未捕获的异常
    app.add_exception_handler(Exception, general_exception_handler) 
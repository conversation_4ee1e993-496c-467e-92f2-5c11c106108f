import os
import secrets
import logging
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, PostgresDsn, RedisDsn, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

# 配置日志器
logger = logging.getLogger("app.config")

class Settings(BaseSettings):
    """
    应用配置类 - 管理所有应用配置项
    
    使用Pydantic的BaseSettings自动从环境变量和.env文件加载配置
    支持类型验证和默认值
    """
    
    ###################
    # 基础配置
    ###################
    
    # 项目名称
    PROJECT_NAME: str = "Admin Server"
    
    # API前缀
    API_V1_STR: str = "/api/v1"
    
    # 环境: development, testing, production
    ENV: str = "development"
    
    # 调试模式
    DEBUG: bool = os.getenv("DEBUG", "").lower() in ("1", "true", "yes", "y")
    
    # 前端URL
    FRONTEND_URL: str = "http://localhost:3000"
    
    # 域名 - 用于Cookie设置等
    DOMAIN: Optional[str] = None
    
    ###################
    # 短信服务配置
    ###################
    
    # 短信服务基本配置
    SMS_ENABLED: bool = False                     # 是否启用短信服务
    SMS_PROVIDER: str = "aliyun"                  # 短信服务提供商
    SMS_ACCESS_KEY: Optional[str] = None          # 访问密钥ID
    SMS_SECRET_KEY: Optional[str] = None          # 访问密钥密码
    SMS_SIGN_NAME: Optional[str] = None           # 短信签名
    SMS_TEMPLATE_CODE: Optional[str] = None       # 短信模板代码
    SMS_AUTO_CREATE_USER: bool = False            # 是否自动创建不存在的用户
    SMS_CODE_EXPIRE_MINUTES: int = 10             # 验证码有效期（分钟）
    SMS_CODE_LENGTH: int = 6                      # 验证码长度
    SMS_COOLDOWN_SECONDS: int = 60                # 验证码发送冷却时间（秒）
    
    # 阿里云短信服务配置
    ALIYUN_SMS_ENABLED: bool = False
    ALIYUN_ACCESS_KEY_ID: Optional[str] = None
    ALIYUN_ACCESS_KEY_SECRET: Optional[str] = None
    ALIYUN_SMS_SIGN_NAME: Optional[str] = None
    ALIYUN_SMS_TEMPLATE_CODE: Optional[str] = None
    
    ###################
    # CAS单点登录配置
    ###################
    CAS_ENABLED: bool = False                      # 是否启用CAS单点登录
    CAS_SERVER_URL: Optional[str] = None           # CAS服务器URL
    CAS_SERVICE_URL: Optional[str] = None          # 服务URL
    CAS_AUTO_CREATE_USER: bool = False             # 是否自动创建CAS用户
    
    ###################
    # API 限流配置
    ###################
    
    # API速率限制
    API_RATE_LIMIT_MAX_REQUESTS: int = 60        # 每分钟最大请求数
    API_RATE_LIMIT_BLACKLIST_THRESHOLD: int = 100 # 触发IP黑名单的阈值
    API_RATE_LIMIT_ENABLED: bool = True          # 是否启用API速率限制
    
    ###################
    # 安全与认证配置
    ###################
    
    # 通用密钥 - 用于各种加密操作
    SECRET_KEY: str = secrets.token_urlsafe(32)
    
    # JWT配置
    JWT_SECRET: str = secrets.token_urlsafe(32)  # JWT签名密钥，生产环境应设置强密钥
    JWT_ALGORITHM: str = "HS256"                 # JWT签名算法
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30        # 访问令牌过期时间(分钟)
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 刷新令牌过期时间(7天)
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7           # 刷新令牌过期天数
    
    # OAuth2配置
    OAUTH2_ENABLED: bool = False                # 是否启用OAuth2认证
    OAUTH2_PROVIDERS: Dict[str, Dict[str, str]] = {}  # OAuth2提供商配置
    
    # CORS配置
    CORS_ORIGINS: Optional[str] = None           # 允许的跨域来源，以逗号分隔
    
    ###################
    # 数据库配置
    ###################
    
    # PostgreSQL配置
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "postgres"
    POSTGRES_DB: str = "admin_server"
    POSTGRES_PORT: int = 5432
    DATABASE_URL: Optional[PostgresDsn] = None   # 数据库连接字符串，未指定时自动构建
    
    # 构建数据库URL
    @model_validator(mode='after')
    def assemble_db_url(self) -> 'Settings':
        """自动构建数据库连接URL"""
        if not self.DATABASE_URL:
            try:
                # 直接构建连接字符串，确保格式正确
                db_url = f"postgresql+asyncpg://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
                self.DATABASE_URL = PostgresDsn(db_url)
                logger.debug(f"成功构建数据库URL: {self.DATABASE_URL}")
            except Exception as e:
                logger.error(f"构建数据库URL失败: {str(e)}")
                # 保持默认值或提供备用值
                pass
        return self
    
    ###################
    # 日志配置
    ###################
    
    # 日志级别
    LOG_LEVEL: str = "INFO"
    
    # 日志文件配置
    LOG_DIR: str = "logs"                       # 日志目录
    LOG_MAX_SIZE_MB: int = 2                    # 单个日志文件最大大小(MB)
    LOG_BACKUP_COUNT: int = 7                   # 备份日志文件数量
    LOG_FORMAT_DETAILED: bool = True            # 是否使用详细日志格式
    
    # 日志分类
    LOG_SQL_STATEMENTS: bool = True            # 是否记录SQL语句
    LOG_HTTP_REQUESTS: bool = True             # 是否记录HTTP请求
    
    ###################
    # Redis缓存配置
    ###################
    
    # Redis服务器配置
    REDIS_HOST: str = "localhost"                # Redis服务器地址
    REDIS_PORT: int = 6379                       # Redis端口
    REDIS_DB: int = 0                            # Redis数据库编号
    REDIS_PASSWORD: Optional[str] = None         # Redis密码
    REDIS_USE_SSL: bool = False                  # 是否使用SSL连接
    REDIS_TIMEOUT: int = 5                       # 连接超时时间(秒)
    
    # Redis缓存策略
    REDIS_CACHE_EXPIRE_SECONDS: int = 3600       # 默认缓存过期时间(秒)
    REDIS_SESSION_PREFIX: str = "session:"       # 会话前缀
    REDIS_VERIFY_CODE_PREFIX: str = "verify:"    # 验证码前缀
    
    # Redis URL (会在初始化时自动构建)
    REDIS_URL: Optional[str] = None

    ###################
    # MongoDB配置
    ###################
    
    MONGODB_ENABLED: bool = False                # 是否启用MongoDB
    MONGODB_URI: Optional[str] = None            # MongoDB连接URI
    MONGODB_DB: str = "admin_server"             # MongoDB数据库名
    MONGODB_FILE_COLLECTION: str = "files"       # 文件集合名
    
    ###################
    # MinIO对象存储配置
    ###################
    
    MINIO_ENDPOINT: str = "localhost"           # MinIO服务器地址
    MINIO_API_PORT: int = 9000                  # MinIO API端口
    MINIO_WEB_PORT: int = 9001                  # MinIO Web控制台端口
    MINIO_ACCESS_KEY: str = "minioadmin"        # MinIO访问密钥
    MINIO_SECRET_KEY: str = "minioadmin"        # MinIO秘密密钥
    MINIO_BUCKET: str = "file-storage"          # MinIO默认存储桶
    MINIO_REGION: str = "us-east-1"             # MinIO区域
    MINIO_USE_SSL: bool = False                 # 是否使用SSL
    STORAGE_TYPE: str = "local"                 # 存储类型: local, minio
    
    ###################
    # 用户配置
    ###################
    
    DEFAULT_ROLE: str = "user"                   # 默认用户角色
    
    # 用户注册配置
    ALLOW_USER_REGISTRATION: bool = True         # 是否允许用户注册
    VERIFY_EMAIL_ON_REGISTER: bool = False       # 是否要求注册时验证邮箱
    
    ###################
    # Pydantic配置
    ###################
    
    # 设置配置文件位置和处理选项
    model_config = SettingsConfigDict(
        env_file=".env",                         # 环境变量文件路径
        env_file_encoding="utf-8",               # 环境变量文件编码
        case_sensitive=True,                     # 区分大小写
        extra="ignore",                          # 忽略额外的环境变量
    )
    
    ###################
    # 初始化方法
    ###################
    
    def __init__(self, **data: Any):
        """初始化配置实例并执行必要的配置转换"""
        super().__init__(**data)
        
        # 处理CORS_ORIGINS
        cors_origins = os.getenv("CORS_ORIGINS")
        if cors_origins:
            # 如果是JSON格式的字符串，尝试解析
            try:
                import json
                self.CORS_ORIGINS = json.loads(cors_origins)
            except json.JSONDecodeError:
                # 如果不是JSON格式，按逗号分隔处理
                self.CORS_ORIGINS = [origin.strip() for origin in cors_origins.replace('"', '').split(",")]
        else:
            # 默认值
            self.CORS_ORIGINS = ["http://localhost:3000", "http://localhost:8080"]
        
        # 构建Redis URL
        self._build_redis_url()
        
        # 检查配置是否完整
        self._validate_essential_configs()
        
        # 输出配置加载信息
        self._log_config_info()
    
    def _build_redis_url(self):
        """构建Redis连接URL"""
        try:
            protocol = "rediss" if self.REDIS_USE_SSL else "redis"
            auth_part = ""
            if self.REDIS_PASSWORD:
                auth_part = f":{self.REDIS_PASSWORD}@"
            
            self.REDIS_URL = f"{protocol}://{auth_part}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
            logger.debug(f"成功构建Redis URL: {self.REDIS_URL}")
        except Exception as e:
            logger.error(f"构建Redis URL失败: {str(e)}")
    
    def _validate_essential_configs(self):
        """验证必要的配置项是否已设置"""
        missing_configs = []
        warnings = []
        
        # 检查必须的配置
        if not self.DATABASE_URL:
            missing_configs.append("DATABASE_URL")
        
        if self.MONGODB_ENABLED and not self.MONGODB_URI:
            missing_configs.append("MONGODB_URI")
        
        # 生产环境下的安全检查
        if self.ENV == "production":
            if self.SECRET_KEY == secrets.token_urlsafe(32):
                warnings.append("生产环境使用了默认的SECRET_KEY，建议设置强密钥")
            if self.JWT_SECRET == secrets.token_urlsafe(32):
                warnings.append("生产环境使用了默认的JWT_SECRET，建议设置强密钥")
            if self.DEBUG:
                warnings.append("生产环境启用了DEBUG模式，建议关闭")
        
        if missing_configs:
            logger.warning(f"以下必要的配置项未设置: {', '.join(missing_configs)}")
        
        if warnings:
            for warning in warnings:
                logger.warning(warning)
    
    def _log_config_info(self):
        """记录配置信息到日志"""
        logger.info(f"加载配置完成: 项目={self.PROJECT_NAME}, 环境={self.ENV}")
        logger.debug(f"数据库连接: {'已配置' if self.DATABASE_URL else '未配置'}")
        logger.debug(f"MongoDB: {'已启用' if self.MONGODB_ENABLED else '未启用'}")
        logger.debug(f"Redis: {'已配置' if self.REDIS_URL else '未配置'}")
    
    ###################
    # 动态配置方法
    ###################
    
    # 存储从数据库加载的动态配置
    system_config: Dict[str, Any] = {}
    
    async def load_dynamic_config(self):
        """
        从数据库加载动态配置
        当数据库连接建立后调用
        """
        try:
            from crud.crud_config import get_all_config_settings
            from db.session import AsyncSessionLocal
            
            async with AsyncSessionLocal() as session:
                self.system_config = await get_all_config_settings(session)
                logger.info(f"成功从数据库加载系统配置，共{len(self.system_config)}项")
        except Exception as e:
            logger.warning(f"无法加载动态配置: {str(e)}")
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        获取配置项，优先从动态配置中获取，如果不存在则从静态配置中获取
        
        Args:
            key: 配置项的键
            default: 如果配置项不存在时的默认值
            
        Returns:
            配置项的值
        """
        # 先从动态配置中获取
        if key in self.system_config:
            return self.system_config[key]
        
        # 再从类属性中获取
        if hasattr(self, key):
            return getattr(self, key)
        
        # 最后返回默认值
        return default
    
    def set_config(self, key: str, value: Any) -> bool:
        """
        设置动态配置项
        
        Args:
            key: 配置项的键
            value: 配置项的值
            
        Returns:
            是否设置成功
        """
        try:
            # 只能设置动态配置
            self.system_config[key] = value
            logger.debug(f"设置动态配置: {key}={value}")
            return True
        except Exception as e:
            logger.error(f"设置动态配置失败: {str(e)}")
            return False
            
    def reload_env(self):
        """
        重新加载环境变量
        """
        try:
            # 创建一个新的Settings实例
            new_settings = Settings()
            
            # 更新当前实例的属性
            for key, value in new_settings.__dict__.items():
                if key != "system_config":  # 保留动态配置
                    setattr(self, key, value)
                    
            logger.info("重新加载环境变量成功")
            
            # 重新构建Redis URL
            self._build_redis_url()
            
            # 重新验证配置
            self._validate_essential_configs()
            
            # 重新记录配置信息
            self._log_config_info()
            
            return True
        except Exception as e:
            logger.error(f"重新加载环境变量失败: {str(e)}")
            return False

# 添加文件存储配置
STORAGE_PATH = os.getenv("STORAGE_PATH", "storage")
TEMP_DIR = os.getenv("TEMP_DIR", "temp")

# 系统监控配置
METRICS_COLLECTION_INTERVAL = int(os.getenv("METRICS_COLLECTION_INTERVAL", "60"))
METRICS_RETENTION_DAYS = int(os.getenv("METRICS_RETENTION_DAYS", "7"))

# 数据库连接池配置
DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "5"))
DB_MAX_OVERFLOW = int(os.getenv("DB_MAX_OVERFLOW", "10"))
DB_POOL_RECYCLE = int(os.getenv("DB_POOL_RECYCLE", "3600"))
DB_POOL_TIMEOUT = int(os.getenv("DB_POOL_TIMEOUT", "30"))

# API缓存配置
API_CACHE_TTL = int(os.getenv("API_CACHE_TTL", "300"))  # 默认5分钟
API_CACHE_MAX_SIZE = int(os.getenv("API_CACHE_MAX_SIZE", "1000"))

# 单例模式
settings = Settings() 
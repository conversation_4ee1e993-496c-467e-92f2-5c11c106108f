from typing import Optional, Union, Dict, Any, Tuple
from fastapi import Depends, Request
from fastapi_users import BaseUserManager, FastAPIUsers, IntegerIDMixin
from fastapi_users.authentication import (
    AuthenticationBackend, 
    BearerTransport,
    JWTStrategy,
    CookieTransport
)
from fastapi_users.db import SQLAlchemyUserDatabase
from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis
from sqlalchemy import select

from db.session import get_db
from models.user import User
from models.oauth import OAuthAccount  # 从oauth模块导入
from core.config import settings
from crud.crud_role import role as role_crud
from db.redis import get_redis
from utils.auth_cache import AuthCache

# 创建用户数据库适配器
async def get_user_db(session: AsyncSession = Depends(get_db)):
    yield SQLAlchemyUserDatabase(session, User, OAuthAccount)

# 自定义用户管理器
class UserManager(IntegerIDMixin, BaseUserManager[User, int]):
    reset_password_token_secret = settings.SECRET_KEY
    verification_token_secret = settings.SECRET_KEY
    model = User

    def __init__(self, user_db):
        """
        初始化用户管理器
        
        Args:
            user_db: 用户数据库适配器
        """
        super().__init__(user_db)

    async def on_after_register(self, user: User, request: Optional[Request] = None):
        """注册后操作：分配默认角色"""
        db = request.state.db if request and hasattr(request.state, "db") else None
        if db:
            # 分配默认角色
            default_role_name = settings.DEFAULT_ROLE
            await role_crud.assign_role_to_user(db, user_id=user.id, role_name=default_role_name)
            print(f"用户 {user.id} 已注册并分配默认角色 {default_role_name}")
        else:
            print(f"用户 {user.id} 已注册，但无法分配默认角色（数据库会话不可用）")

    async def on_after_forgot_password(
        self, user: User, token: str, request: Optional[Request] = None
    ):
        """发送密码重置邮件"""
        print(f"用户 {user.id} 请求密码重置。重置令牌: {token}")

    async def on_after_request_verify(
        self, user: User, token: str, request: Optional[Request] = None
    ):
        """发送邮箱验证邮件"""
        print(f"验证邮件已发送给用户 {user.id}。验证令牌: {token}")
    
    async def on_after_update(
        self, user: User, update_dict: Dict[str, Any], request: Optional[Request] = None
    ):
        """更新用户信息后的操作"""
        print(f"用户 {user.id} 信息已更新: {update_dict}")
        
        # 清除用户缓存
        try:
            from services.redis_service import RedisService
            from utils.auth_cache import AuthCache
            
            redis_cache = await RedisService.get_cache()
            auth_cache = AuthCache(redis_cache)
            await auth_cache.delete_user(user.id)
        except Exception as e:
            import logging
            logging.getLogger(__name__).error(f"清除用户缓存失败: {str(e)}")

    async def on_after_login(
        self, user: User, request: Optional[Request] = None, response: Optional[Any] = None
    ):
        """登录成功后的操作"""
        # 安全访问用户ID，避免懒加载问题
        try:
            # 如果user对象是字典或者已经加载完成的实体对象
            if isinstance(user, dict):
                user_id = user.get('id', 'unknown')
            elif hasattr(user, '__dict__') and 'id' in user.__dict__:
                # 如果属性已经加载到__dict__中，可以安全访问
                user_id = user.__dict__['id']
            else:
                # 否则使用安全标记
                user_id = '<已登录用户>'
                
            print(f"用户 {user_id} 登录成功")
        except Exception as e:
            print(f"用户登录成功，但无法获取用户ID: {str(e)}")
        
        # 更新最后登录时间
        # 此处可以添加最后登录时间更新逻辑
        
    async def get_by_username(self, username: str) -> Optional[User]:
        """
        根据用户名获取用户
        
        Args:
            username: 用户名
            
        Returns:
            用户对象，如果未找到则返回None
        """
        if not username:
            return None
            
        statement = select(self.model).where(self.model.username == username)
        user = await self.user_db.session.execute(statement)
        return user.scalar_one_or_none()
    
    async def get_by_phone(self, phone: str) -> Optional[User]:
        """
        根据手机号获取用户
        
        Args:
            phone: 手机号
            
        Returns:
            用户对象，如果未找到则返回None
        """
        if not phone or not hasattr(self.model, 'phone'):
            return None
            
        statement = select(self.model).where(self.model.phone == phone)
        user = await self.user_db.session.execute(statement)
        return user.scalar_one_or_none()
        
    async def authenticate(self, credentials):
        """
        认证用户，支持用户名和邮箱登录
        
        Args:
            credentials: 登录凭据
            
        Returns:
            认证成功返回用户对象，否则返回None
        """
        try:
            # 先尝试通过用户名查找用户
            user = await self.get_by_username(credentials.username)
            
            # 如果未找到，再尝试通过邮箱查找
            if user is None:
                user = await self.get_by_email(credentials.username)
            
            # 尝试通过手机号查找(如果模型支持)
            if user is None and hasattr(self.model, 'phone'):
                user = await self.get_by_phone(credentials.username)
            
            # 验证用户是否存在且激活
            if user is None or not user.is_active:
                # 添加调试日志
                print(f"认证失败: 用户不存在或未激活 - {credentials.username}")
                return None
            
            # 验证密码
            verified, updated_password_hash = self.password_helper.verify_and_update(
                credentials.password, user.hashed_password
            )
            
            if not verified:
                # 添加调试日志
                print(f"认证失败: 密码验证失败 - {credentials.username}")
                return None
            
            # 如果密码哈希需要更新
            if updated_password_hash is not None:
                await self._update_password(user, updated_password_hash)
            
            return user
        except Exception as e:
            # 记录异常
            import logging
            logging.getLogger(__name__).error(f"认证过程出错: {str(e)}")
            return None

    # 获取最终用户，可在此添加额外逻辑，如加载权限等
    async def get_user(self, user_id: int) -> Optional[User]:
        """获取用户，优先从缓存获取"""
        # 尝试使用缓存
        try:
            from services.redis_service import RedisService
            from utils.auth_cache import AuthCache
            
            # 获取缓存实例
            redis_cache = await RedisService.get_cache()
            auth_cache = AuthCache(redis_cache)
            
            # 尝试从缓存获取
            cached_user = await auth_cache.get_user(user_id)
            if cached_user:
                # 将缓存数据转换为用户对象(部分信息)
                try:
                    # 尝试从缓存信息判断用户是否存在，若存在再查询完整信息
                    # 这里还是需要查询数据库，但可以避免用户不存在时的无效查询
                    return await super().get_user(user_id)
                except Exception as e:
                    import logging
                    logging.getLogger(__name__).error(f"从数据库获取用户失败: {e}")
                    return None
        except Exception as e:
            import logging
            logging.getLogger(__name__).error(f"从缓存获取用户失败: {str(e)}")
        
        # 从数据库获取
        user = await super().get_user(user_id)
        
        # 如果找到了用户，缓存用户信息
        if user:
            try:
                from services.redis_service import RedisService
                from utils.auth_cache import AuthCache
                
                redis_cache = await RedisService.get_cache()
                auth_cache = AuthCache(redis_cache)
                
                # 序列化用户对象基本信息到字典
                user_dict = {
                    "id": user.id,
                    "email": user.email,
                    "username": user.username,
                    "is_active": user.is_active,
                    "is_verified": user.is_verified,
                    "is_superuser": user.is_superuser,
                }
                await auth_cache.set_user(user.id, user_dict)
            except Exception as e:
                import logging
                logging.getLogger(__name__).error(f"缓存用户信息失败: {str(e)}")
        
        return user

# 创建用户管理器实例
async def get_user_manager(
    user_db=Depends(get_user_db)
):
    yield UserManager(user_db)

# 配置认证后端
# JWT令牌认证
bearer_transport = BearerTransport(tokenUrl=f"{settings.API_V1_STR}/auth/jwt/login")

# 自定义JWT策略，增加缓存支持
class CachedJWTStrategy(JWTStrategy):
    """支持缓存的JWT策略"""
    
    async def read_token(
        self, token: str, user_manager: Optional[BaseUserManager] = None
    ) -> Optional[User]:
        """
        读取令牌，优先从缓存获取令牌数据
        
        Args:
            token: JWT令牌
            user_manager: 用户管理器
            
        Returns:
            验证成功返回用户对象，否则返回None
        """
        try:
            # 使用RedisService获取缓存实例
            from services.redis_service import RedisService
            from utils.auth_cache import AuthCache
            
            redis_cache = await RedisService.get_cache()
            auth_cache = AuthCache(redis_cache)
            
            # 尝试从缓存中获取令牌数据
            token_data = await auth_cache.get_token_data(token)
            if token_data:
                # 如果缓存命中，从令牌数据中获取用户ID
                user_id = token_data.get("sub")
                if user_id and user_manager:
                    # 使用用户ID获取用户
                    return await user_manager.get_user(int(user_id))
                    
            # 缓存未命中，使用常规方式验证令牌
            user = await super().read_token(token, user_manager)
            
            # 如果验证成功，缓存令牌数据
            if user:
                # 从令牌中提取数据
                token_data = self.decode_token(token)
                # 缓存令牌数据
                await auth_cache.set_token_data(token, token_data)
            
            return user
            
        except Exception as e:
            # 出现异常时记录日志并回退到标准方法
            import logging
            logging.getLogger(__name__).error(f"缓存令牌验证失败: {str(e)}")
            return await super().read_token(token, user_manager)
    
    async def write_token(self, user: User) -> str:
        """
        生成JWT令牌
        
        Args:
            user: 用户对象
            
        Returns:
            生成的JWT令牌
        """
        # 创建令牌数据
        token_data = {"sub": str(user.id)}
        
        # 调用父类方法生成令牌
        token = await super().write_token(user)
        
        # 尝试缓存令牌数据
        try:
            from services.redis_service import RedisService
            from utils.auth_cache import AuthCache
            
            redis_cache = await RedisService.get_cache()
            auth_cache = AuthCache(redis_cache)
            
            # 缓存令牌数据
            await auth_cache.set_token_data(token, token_data)
        except Exception as e:
            # 异常记录但不中断流程
            import logging
            logging.getLogger(__name__).error(f"缓存令牌数据失败: {str(e)}")
        
        return token

# 使用缓存JWT策略
jwt_strategy = CachedJWTStrategy(
    secret=settings.JWT_SECRET, 
    lifetime_seconds=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
)

jwt_backend = AuthenticationBackend(
    name="jwt",
    transport=bearer_transport,
    get_strategy=lambda: jwt_strategy,
)

# Cookie认证（可选）
cookie_transport = CookieTransport(
    cookie_name="fastapiusers_auth",
    cookie_max_age=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    cookie_secure=not settings.DEBUG,  # 开发环境不需要https
    cookie_httponly=True
)
cookie_backend = AuthenticationBackend(
    name="cookie",
    transport=cookie_transport,
    get_strategy=lambda: jwt_strategy,  # 复用JWT策略
)

# 创建FastAPIUsers实例
fastapi_users = FastAPIUsers[User, int](
    get_user_manager,
    [jwt_backend, cookie_backend],  # 使用两种认证后端
)

# 依赖项：获取当前用户
current_active_user = fastapi_users.current_user(active=True)
current_superuser = fastapi_users.current_user(active=True, superuser=True)
optional_current_user = fastapi_users.current_user(active=True, optional=True) 
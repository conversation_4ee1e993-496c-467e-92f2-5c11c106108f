# 项目工具集

本目录包含项目开发、测试和维护的各种工具脚本和实用程序。

## 目录结构

- `auth_tests/` - 用户认证和授权相关的测试工具，用于诊断和修复JWT认证问题
- *（后续可添加更多工具子目录）*

## 使用说明

每个工具子目录中都包含自己的README.md文件，详细说明了该工具的用途和使用方法。请参考各子目录中的文档了解详情。

## 开发规范

1. 所有工具应该放在适当的子目录中，按功能分类
2. 每个工具脚本应该包含详细的注释和文档字符串
3. 工具脚本应该提供命令行参数帮助
4. 所有工具应该遵循项目的编码风格和最佳实践
5. 工具应该能够从项目的任何目录运行，不应该依赖特定的工作目录

## 贡献指南

添加新工具时，请遵循以下步骤：

1. 在适当的子目录中创建工具脚本
2. 添加完整的文档和使用示例
3. 确保工具能够正确处理错误情况
4. 如果创建新的子目录，添加一个README.md文件
5. 更新此文件，添加新工具的引用 
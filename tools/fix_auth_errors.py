#!/usr/bin/env python3
"""
修复认证token阶段的错误

修复以下错误：
1. object dict can't be used in 'await' expression
2. 'int' object has no attribute 'expires'
"""

import asyncio
import sys
import os
from pathlib import Path
import traceback

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


async def test_api_auth_middleware():
    """测试API认证中间件"""
    print("=== 测试API认证中间件 ===")
    
    try:
        from utils.api_auth import APIClientAuth
        from fastapi import Request
        from unittest.mock import Mock
        
        # 创建模拟请求
        request = Mock(spec=Request)
        request.url.path = "/api/v1/test"
        request.client.host = "127.0.0.1"
        request.headers = {"Authorization": "Bearer test_token"}
        request.query_params = {}
        request.method = "GET"
        
        # 创建API认证中间件
        middleware = APIClientAuth(exclude_paths=["/docs"], scopes_map={})
        
        # 测试初始化
        await middleware._ensure_initialized()
        print("✅ 中间件初始化成功")
        
        # 测试令牌验证
        try:
            result = await middleware._verify_token("test_token")
            print(f"✅ 令牌验证完成: {result}")
        except Exception as e:
            print(f"⚠️  令牌验证错误（预期）: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ API认证中间件测试失败: {str(e)}")
        traceback.print_exc()
        return False


async def test_client_info_caching():
    """测试客户端信息缓存"""
    print("\n=== 测试客户端信息缓存 ===")
    
    try:
        from utils.api_auth import APIClientAuth
        from unittest.mock import Mock, AsyncMock
        
        # 创建模拟数据库会话
        db = AsyncMock()
        
        # 创建API认证中间件
        middleware = APIClientAuth(exclude_paths=["/docs"], scopes_map={})
        await middleware._ensure_initialized()
        
        # 测试获取客户端信息
        try:
            client_info = await middleware._get_client_info("test_client", db)
            print(f"✅ 客户端信息获取完成: {client_info}")
        except Exception as e:
            print(f"⚠️  客户端信息获取错误（预期）: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 客户端信息缓存测试失败: {str(e)}")
        traceback.print_exc()
        return False


async def test_request_cache():
    """测试请求缓存装饰器"""
    print("\n=== 测试请求缓存装饰器 ===")
    
    try:
        from utils.request_cache import cached_request
        
        # 测试异步函数缓存
        @cached_request(ttl=60)
        async def test_async_func(param1, param2="default"):
            return {"result": f"{param1}_{param2}", "type": "async"}
        
        # 测试同步函数缓存
        @cached_request(ttl=60)
        async def test_sync_func(param1, param2="default"):
            return {"result": f"{param1}_{param2}", "type": "sync"}
        
        # 测试异步函数
        result1 = await test_async_func("test", param2="value")
        print(f"✅ 异步函数缓存测试: {result1}")
        
        # 测试同步函数
        result2 = await test_sync_func("test", param2="value")
        print(f"✅ 同步函数缓存测试: {result2}")
        
        # 测试缓存命中
        result3 = await test_async_func("test", param2="value")
        print(f"✅ 缓存命中测试: {result3}")
        
        return True
        
    except Exception as e:
        print(f"❌ 请求缓存测试失败: {str(e)}")
        traceback.print_exc()
        return False


def fix_api_auth_code():
    """修复API认证代码中的问题"""
    print("\n=== 修复API认证代码 ===")
    
    api_auth_file = "utils/api_auth.py"
    
    if not os.path.exists(api_auth_file):
        print(f"找不到API认证文件: {api_auth_file}")
        return False
    
    try:
        # 读取文件内容
        with open(api_auth_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否需要修复
        modifications = []
        
        # 1. 修复可能的expires属性访问错误
        if "client.expires" in content and "client.expires_at" not in content:
            content = content.replace("client.expires", "client.expires_at")
            modifications.append("修复client.expires -> client.expires_at")
        
        # 2. 确保过期时间检查的安全性
        if "client_info[\"expires_at\"] < time.time()" in content:
            # 这个检查是正确的，不需要修改
            pass
        
        # 3. 检查缓存相关的问题
        if "await cached_result" in content:
            content = content.replace("await cached_result", "cached_result")
            modifications.append("修复缓存结果的await错误")
        
        # 4. 确保Redis客户端正确关闭
        redis_pattern = "redis_pool.connection()"
        if redis_pattern in content:
            content = content.replace(
                redis_pattern,
                "Redis(connection_pool=redis_pool)"
            )
            modifications.append("修复Redis连接池使用方式")
        
        # 保存修改
        if modifications:
            # 创建备份
            backup_file = f"{api_auth_file}.backup"
            with open(backup_file, "w", encoding="utf-8") as f:
                f.write(content)
            
            with open(api_auth_file, "w", encoding="utf-8") as f:
                f.write(content)
            
            print(f"✅ 已修复API认证文件，修改内容:")
            for mod in modifications:
                print(f"   - {mod}")
            print(f"   备份文件: {backup_file}")
        else:
            print("✅ API认证文件未发现需要修复的问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复API认证代码失败: {str(e)}")
        traceback.print_exc()
        return False


def fix_request_cache_code():
    """修复请求缓存代码中的问题"""
    print("\n=== 修复请求缓存代码 ===")
    
    cache_file = "utils/request_cache.py"
    
    if not os.path.exists(cache_file):
        print(f"找不到请求缓存文件: {cache_file}")
        return False
    
    try:
        # 读取文件内容
        with open(cache_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否需要修复
        modifications = []
        
        # 1. 确保缓存结果检查的安全性
        if "cached_result is not None" in content:
            # 添加类型检查
            old_pattern = "if cached_result is not None:"
            new_pattern = "if cached_result is not None and not asyncio.iscoroutine(cached_result):"
            if old_pattern in content and new_pattern not in content:
                content = content.replace(old_pattern, new_pattern)
                modifications.append("添加缓存结果的协程检查")
        
        # 2. 确保异步函数检查的正确性
        if "asyncio.iscoroutinefunction(func)" in content:
            # 这个检查是正确的，不需要修改
            pass
        
        # 保存修改
        if modifications:
            # 创建备份
            backup_file = f"{cache_file}.backup"
            with open(backup_file, "w", encoding="utf-8") as f:
                f.write(content)
            
            with open(cache_file, "w", encoding="utf-8") as f:
                f.write(content)
            
            print(f"✅ 已修复请求缓存文件，修改内容:")
            for mod in modifications:
                print(f"   - {mod}")
            print(f"   备份文件: {backup_file}")
        else:
            print("✅ 请求缓存文件未发现需要修复的问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复请求缓存代码失败: {str(e)}")
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("🔧 认证Token阶段错误修复工具")
    print("=" * 50)
    
    # 运行测试
    results = []
    
    # 测试1: API认证中间件
    results.append(await test_api_auth_middleware())
    
    # 测试2: 客户端信息缓存
    results.append(await test_client_info_caching())
    
    # 测试3: 请求缓存装饰器
    results.append(await test_request_cache())
    
    # 修复代码
    print("\n" + "=" * 50)
    print("🔧 开始修复代码")
    print("=" * 50)
    
    # 修复1: API认证代码
    results.append(fix_api_auth_code())
    
    # 修复2: 请求缓存代码
    results.append(fix_request_cache_code())
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 修复结果总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"🎯 成功项目: {passed}/{total}")
    
    test_names = [
        "API认证中间件测试",
        "客户端信息缓存测试",
        "请求缓存装饰器测试",
        "API认证代码修复",
        "请求缓存代码修复"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {name}: {status}")
    
    if passed == total:
        print("\n🎉 所有修复项目完成！")
        print("\n💡 建议:")
        print("   1. 重启应用以使修复生效")
        print("   2. 测试登录和API认证功能")
        print("   3. 检查应用日志确认错误已解决")
        return True
    else:
        print(f"\n⚠️  {total - passed} 个项目需要手动处理")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

-- 首先检查SMS配置表是否已存在
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_name = 'sms_configs'
    ) THEN
        -- 检查是否已有默认配置
        IF NOT EXISTS (
            SELECT 1
            FROM sms_configs
            WHERE config_name = 'default_sms'
        ) THEN
            -- 插入默认配置
            INSERT INTO sms_configs (
                config_name,
                provider,
                access_key,
                secret_key,
                sign_name,
                template_code,
                auto_create_user,
                code_expire_minutes,
                code_length,
                cooldown_seconds,
                is_active,
                description,
                created_at,
                updated_at
            ) VALUES (
                'default_sms',                          -- 配置名称
                'aliyun',                               -- 提供商
                '你的阿里云AccessKeyID',                -- 你需要替换这个值
                '你的阿里云AccessKeySecret',            -- 你需要替换这个值
                '你的短信签名',                         -- 你需要替换这个值
                '你的短信模板代码',                     -- 你需要替换这个值
                TRUE,                                   -- 是否自动创建用户
                10,                                     -- 验证码有效期（分钟）
                6,                                      -- 验证码长度
                60,                                     -- 验证码发送冷却时间（秒）
                TRUE,                                   -- 是否激活
                '阿里云短信服务默认配置',                -- 描述
                now(),                                  -- 创建时间
                now()                                   -- 更新时间
            );

            RAISE NOTICE '已成功添加默认SMS配置';
        ELSE
            -- 更新已存在的配置
            UPDATE sms_configs
            SET 
                provider = 'aliyun',
                access_key = '你的阿里云AccessKeyID',            -- 你需要替换这个值
                secret_key = '你的阿里云AccessKeySecret',        -- 你需要替换这个值
                sign_name = '你的短信签名',                       -- 你需要替换这个值
                template_code = '你的短信模板代码',               -- 你需要替换这个值
                auto_create_user = TRUE,
                code_expire_minutes = 10,
                code_length = 6,
                cooldown_seconds = 60,
                is_active = TRUE,
                description = '阿里云短信服务默认配置（已更新）',
                updated_at = now()
            WHERE config_name = 'default_sms';

            RAISE NOTICE '已成功更新默认SMS配置';
        END IF;
    ELSE
        RAISE NOTICE 'sms_configs表不存在，请先运行数据库迁移';
    END IF;
END $$; 
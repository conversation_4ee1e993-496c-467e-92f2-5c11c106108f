#!/usr/bin/env python3
"""
文件服务状态检查工具

检查文件服务的健康状态，包括：
1. 存储服务状态（本地存储、MinIO）
2. 数据库连接状态
3. 文件上传和下载功能
4. 配置信息
"""

import asyncio
import os
import sys
import json
import tempfile
import requests
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config import settings
from db.session import get_db
from services.storage_factory import get_storage_service, StorageFactory


async def check_database_connection():
    """检查数据库连接"""
    print("=== 检查数据库连接 ===")
    try:
        from sqlalchemy import text
        async for db in get_db():
            result = await db.execute(text("SELECT 1"))
            if result.scalar() == 1:
                print("✅ 数据库连接正常")
                return True
            else:
                print("❌ 数据库查询异常")
                return False
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False


async def check_storage_services():
    """检查存储服务"""
    print("\n=== 检查存储服务 ===")
    
    storage_status = {
        "default": None,
        "local": None,
        "minio": None
    }
    
    # 检查默认存储服务
    try:
        default_storage = get_storage_service()
        storage_type = getattr(settings, "STORAGE_TYPE", "local").lower()
        
        print(f"📁 默认存储类型: {storage_type}")
        print(f"📁 存储服务类: {default_storage.__class__.__name__}")
        
        storage_status["default"] = {
            "type": storage_type,
            "service_class": default_storage.__class__.__name__,
            "status": "available"
        }
        print("✅ 默认存储服务可用")
        
    except Exception as e:
        print(f"❌ 默认存储服务检查失败: {str(e)}")
        storage_status["default"] = {"status": "error", "message": str(e)}
    
    # 检查本地存储
    try:
        local_storage = StorageFactory.get_local_storage()
        base_path = local_storage.base_path
        temp_dir = local_storage.temp_dir
        
        print(f"\n📂 本地存储路径: {base_path}")
        print(f"📂 临时目录: {temp_dir}")
        
        base_exists = os.path.exists(base_path)
        temp_exists = os.path.exists(temp_dir)
        base_writable = os.access(base_path, os.W_OK) if base_exists else False
        temp_writable = os.access(temp_dir, os.W_OK) if temp_exists else False
        
        print(f"📂 基础路径存在: {'✅' if base_exists else '❌'}")
        print(f"📂 临时目录存在: {'✅' if temp_exists else '❌'}")
        print(f"📂 基础路径可写: {'✅' if base_writable else '❌'}")
        print(f"📂 临时目录可写: {'✅' if temp_writable else '❌'}")
        
        storage_status["local"] = {
            "base_path": base_path,
            "temp_dir": temp_dir,
            "base_path_exists": base_exists,
            "temp_dir_exists": temp_exists,
            "base_path_writable": base_writable,
            "temp_dir_writable": temp_writable,
            "status": "available" if (base_exists and temp_exists and base_writable and temp_writable) else "degraded"
        }
        
    except Exception as e:
        print(f"❌ 本地存储检查失败: {str(e)}")
        storage_status["local"] = {"status": "error", "message": str(e)}
    
    # 检查MinIO存储
    try:
        minio_storage = StorageFactory.get_minio_storage()
        endpoint = f"{settings.MINIO_ENDPOINT}:{settings.MINIO_API_PORT}"
        bucket = settings.MINIO_BUCKET
        
        print(f"\n🗄️  MinIO端点: {endpoint}")
        print(f"🗄️  MinIO存储桶: {bucket}")
        
        # 检查存储桶
        bucket_ready = await minio_storage.ensure_bucket_exists()
        print(f"🗄️  存储桶状态: {'✅ 就绪' if bucket_ready else '❌ 未就绪'}")
        
        storage_status["minio"] = {
            "endpoint": endpoint,
            "bucket": bucket,
            "bucket_ready": bucket_ready,
            "status": "connected" if bucket_ready else "error"
        }
        
    except Exception as e:
        print(f"❌ MinIO存储检查失败: {str(e)}")
        storage_status["minio"] = {"status": "error", "message": str(e)}
    
    return storage_status


def check_configuration():
    """检查配置信息"""
    print("\n=== 检查配置信息 ===")
    
    config_info = {
        "storage_type": getattr(settings, "STORAGE_TYPE", "local"),
        "storage_path": getattr(settings, "STORAGE_PATH", "storage"),
        "temp_dir": getattr(settings, "TEMP_DIR", "temp"),
        "minio": {
            "endpoint": settings.MINIO_ENDPOINT,
            "api_port": settings.MINIO_API_PORT,
            "bucket": settings.MINIO_BUCKET,
            "use_ssl": getattr(settings, "MINIO_USE_SSL", False)
        },
        "database": {
            "url": str(settings.DATABASE_URL)[:50] + "..." if len(str(settings.DATABASE_URL)) > 50 else str(settings.DATABASE_URL)
        }
    }
    
    print(f"⚙️  存储类型: {config_info['storage_type']}")
    print(f"⚙️  存储路径: {config_info['storage_path']}")
    print(f"⚙️  临时目录: {config_info['temp_dir']}")
    print(f"⚙️  MinIO端点: {config_info['minio']['endpoint']}:{config_info['minio']['api_port']}")
    print(f"⚙️  MinIO存储桶: {config_info['minio']['bucket']}")
    print(f"⚙️  MinIO SSL: {config_info['minio']['use_ssl']}")
    
    return config_info


async def test_file_upload_download():
    """测试文件上传和下载功能"""
    print("\n=== 测试文件上传下载 ===")
    
    try:
        # 创建测试文件
        test_content = f"测试文件内容 - {datetime.now().isoformat()}"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            test_file_path = f.name
        
        print(f"📄 创建测试文件: {test_file_path}")
        
        # 测试本地存储
        try:
            storage_service = get_storage_service()
            
            with open(test_file_path, 'rb') as f:
                file_info = await storage_service.save_file(
                    file_content=f,
                    filename="test_file.txt",
                    content_type="text/plain",
                    description="文件服务测试文件",
                    is_public=True
                )
            
            print(f"✅ 文件上传成功: {file_info['id']}")
            print(f"📄 文件URL: {file_info['file_url']}")
            
            # 测试文件下载
            async for db in get_db():
                content_result = await storage_service.get_file_content(file_info['id'], db=db)
                if content_result:
                    content, content_type = content_result
                    if test_content.encode() in content:
                        print("✅ 文件下载成功，内容验证通过")
                        return True
                    else:
                        print("❌ 文件下载内容验证失败")
                        return False
                else:
                    print("❌ 文件下载失败")
                    return False
                break
            
        except Exception as e:
            print(f"❌ 文件上传下载测试失败: {str(e)}")
            return False
        
        finally:
            # 清理测试文件
            try:
                os.unlink(test_file_path)
            except:
                pass
    
    except Exception as e:
        print(f"❌ 文件测试准备失败: {str(e)}")
        return False


def check_api_endpoints():
    """检查API端点"""
    print("\n=== 检查API端点 ===")
    
    base_url = "http://localhost:8000"
    endpoints = [
        "/health",
        "/api/v1/files/status",
        "/api/v1/files/"
    ]
    
    results = {}
    
    for endpoint in endpoints:
        try:
            url = f"{base_url}{endpoint}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {endpoint}: HTTP {response.status_code}")
                results[endpoint] = {"status": "ok", "code": response.status_code}
            else:
                print(f"⚠️  {endpoint}: HTTP {response.status_code}")
                results[endpoint] = {"status": "warning", "code": response.status_code}
                
        except requests.exceptions.ConnectionError:
            print(f"❌ {endpoint}: 连接失败")
            results[endpoint] = {"status": "error", "message": "连接失败"}
        except Exception as e:
            print(f"❌ {endpoint}: {str(e)}")
            results[endpoint] = {"status": "error", "message": str(e)}
    
    return results


async def main():
    """主函数"""
    print("🔍 文件服务状态检查工具")
    print("=" * 50)
    
    # 检查各个组件
    db_status = await check_database_connection()
    storage_status = await check_storage_services()
    config_info = check_configuration()
    api_status = check_api_endpoints()
    
    # 如果数据库连接正常，测试文件功能
    upload_test_status = False
    if db_status:
        upload_test_status = await test_file_upload_download()
    
    # 生成总结报告
    print("\n" + "=" * 50)
    print("📊 状态总结")
    print("=" * 50)
    
    overall_status = "healthy"
    issues = []
    
    if not db_status:
        overall_status = "unhealthy"
        issues.append("数据库连接失败")
    
    if storage_status.get("default", {}).get("status") == "error":
        overall_status = "degraded" if overall_status == "healthy" else "unhealthy"
        issues.append("默认存储服务异常")
    
    if not upload_test_status and db_status:
        overall_status = "degraded" if overall_status == "healthy" else "unhealthy"
        issues.append("文件上传下载测试失败")
    
    print(f"🎯 整体状态: {overall_status.upper()}")
    if issues:
        print("⚠️  发现问题:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("✅ 所有检查项目正常")
    
    # 保存详细报告
    report = {
        "timestamp": datetime.now().isoformat(),
        "overall_status": overall_status,
        "issues": issues,
        "database": {"status": "connected" if db_status else "error"},
        "storage": storage_status,
        "configuration": config_info,
        "api_endpoints": api_status,
        "upload_test": {"status": "passed" if upload_test_status else "failed"}
    }
    
    report_file = "file_service_status_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细报告已保存到: {report_file}")


if __name__ == "__main__":
    asyncio.run(main())

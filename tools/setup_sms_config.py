#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
脚本：初始化短信配置到数据库
该脚本用于将短信配置一次性导入到数据库中，之后仅从数据库读取配置
"""
import asyncio
import logging
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from db.session import AsyncSessionLocal
from models.sms_config import SMSConfig
from core.config import settings

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 配置参数
# 这些参数仅用于首次初始化，后续修改应当通过管理界面进行
CONFIG_NAME = "default_sms"
PROVIDER = "aliyun"
ACCESS_KEY = settings.ALIYUN_ACCESS_KEY_ID if hasattr(settings, "ALIYUN_ACCESS_KEY_ID") else None
SECRET_KEY = settings.ALIYUN_ACCESS_KEY_SECRET if hasattr(settings, "ALIYUN_ACCESS_KEY_SECRET") else None
SIGN_NAME = settings.ALIYUN_SMS_SIGN_NAME if hasattr(settings, "ALIYUN_SMS_SIGN_NAME") else None
TEMPLATE_CODE = settings.ALIYUN_SMS_TEMPLATE_CODE if hasattr(settings, "ALIYUN_SMS_TEMPLATE_CODE") else None
AUTO_CREATE_USER = True
CODE_EXPIRE_MINUTES = 10
CODE_LENGTH = 6
COOLDOWN_SECONDS = 60

async def setup_sms_config():
    """将短信配置初始化到数据库中"""
    async with AsyncSessionLocal() as db:
        # 检查是否已有配置
        from sqlalchemy import select
        result = await db.execute(select(SMSConfig).where(SMSConfig.config_name == CONFIG_NAME))
        exists = result.scalar_one_or_none()
        
        if exists:
            logger.info(f"短信配置 '{CONFIG_NAME}' 已存在，执行更新操作")
            # 更新现有配置
            exists.provider = PROVIDER
            exists.access_key = ACCESS_KEY
            exists.secret_key = SECRET_KEY
            exists.sign_name = SIGN_NAME
            exists.template_code = TEMPLATE_CODE
            exists.auto_create_user = AUTO_CREATE_USER
            exists.code_expire_minutes = CODE_EXPIRE_MINUTES
            exists.code_length = CODE_LENGTH
            exists.cooldown_seconds = COOLDOWN_SECONDS
            exists.is_active = True
            exists.description = "阿里云短信服务默认配置"
            db.add(exists)
        else:
            logger.info(f"创建新的短信配置 '{CONFIG_NAME}'")
            # 创建新配置
            new_config = SMSConfig(
                config_name=CONFIG_NAME,
                provider=PROVIDER,
                access_key=ACCESS_KEY,
                secret_key=SECRET_KEY,
                sign_name=SIGN_NAME,
                template_code=TEMPLATE_CODE,
                auto_create_user=AUTO_CREATE_USER,
                code_expire_minutes=CODE_EXPIRE_MINUTES,
                code_length=CODE_LENGTH,
                cooldown_seconds=COOLDOWN_SECONDS,
                is_active=True,
                description="阿里云短信服务默认配置"
            )
            db.add(new_config)
        
        # 提交更改
        await db.commit()
        logger.info("数据库中的SMS配置已更新")
        logger.info(f"配置信息: 提供商={PROVIDER}, 签名={SIGN_NAME}, 模板={TEMPLATE_CODE}")

if __name__ == "__main__":
    # 检查必要的配置是否存在
    if not all([ACCESS_KEY, SECRET_KEY, SIGN_NAME, TEMPLATE_CODE]):
        missing = []
        if not ACCESS_KEY: missing.append("ACCESS_KEY")
        if not SECRET_KEY: missing.append("SECRET_KEY")
        if not SIGN_NAME: missing.append("SIGN_NAME")
        if not TEMPLATE_CODE: missing.append("TEMPLATE_CODE")
        
        logger.error(f"缺少必要的配置参数: {', '.join(missing)}")
        logger.error("请设置以上参数后再运行此脚本")
        
        # 询问是否继续
        print("\n您可以直接在此脚本中设置这些参数:")
        print("1. 编辑脚本设置参数值")
        print("2. 或者在命令行中输入参数值")
        
        confirm = input("\n是否要在命令行中输入参数值? (y/n): ")
        if confirm.lower() == 'y':
            if not ACCESS_KEY:
                ACCESS_KEY = input("请输入阿里云ACCESS_KEY_ID: ")
            if not SECRET_KEY:
                SECRET_KEY = input("请输入阿里云ACCESS_KEY_SECRET: ")
            if not SIGN_NAME:
                SIGN_NAME = input("请输入短信签名: ")
            if not TEMPLATE_CODE:
                TEMPLATE_CODE = input("请输入短信模板代码: ")
                
            # 再次检查
            if not all([ACCESS_KEY, SECRET_KEY, SIGN_NAME, TEMPLATE_CODE]):
                logger.error("仍有必要参数未设置，终止操作")
                exit(1)
        else:
            exit(1)
    
    # 运行迁移脚本
    asyncio.run(setup_sms_config()) 
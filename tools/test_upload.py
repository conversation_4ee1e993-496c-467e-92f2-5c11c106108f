#!/usr/bin/env python3
"""
文件上传测试脚本
测试上传功能是否正常
"""
import sys
import os
import asyncio
import logging
import random
import requests
import json
from pathlib import Path
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('upload_test.log')
    ]
)
logger = logging.getLogger("upload-test")

# 测试配置
TEST_API_URL = "http://localhost:8000"
API_TOKEN = os.environ.get("API_TOKEN", "")  # 从环境变量获取令牌

def create_test_file(size_kb=100, content_type="text/plain", filename=None):
    """创建测试文件
    
    Args:
        size_kb: 文件大小(KB)
        content_type: 内容类型
        filename: 文件名，如果不提供则根据内容类型生成
        
    Returns:
        (文件路径, 文件名)元组
    """
    # 生成随机文件名
    if filename is None:
        if content_type == "text/plain":
            filename = f"test_{random.randint(1000, 9999)}.txt"
        elif content_type == "application/json":
            filename = f"test_{random.randint(1000, 9999)}.json"
        elif content_type == "image/png":
            filename = f"test_{random.randint(1000, 9999)}.png"
        else:
            filename = f"test_{random.randint(1000, 9999)}.bin"
    
    # 创建临时目录
    temp_dir = Path("temp")
    temp_dir.mkdir(exist_ok=True)
    
    # 文件路径
    file_path = temp_dir / filename
    
    # 创建随机内容
    content = os.urandom(size_kb * 1024)
    
    # 写入文件
    with open(file_path, "wb") as f:
        f.write(content)
    
    logger.info(f"已创建测试文件: {file_path}, 大小: {size_kb}KB")
    return file_path, filename

def test_file_upload(file_path, filename, category="test", tags="upload,test,debug"):
    """测试文件上传
    
    Args:
        file_path: 文件路径
        filename: 文件名
        category: 分类
        tags: 标签
        
    Returns:
        上传响应
    """
    logger.info(f"开始上传文件: {file_path}")
    
    # 设置认证头
    headers = {}
    if API_TOKEN:
        headers["Authorization"] = f"Bearer {API_TOKEN}"
    
    # 准备表单数据
    form_data = {
        "category": category,
        "tags": tags,
        "is_public": "true",
        "description": f"测试上传 - {datetime.now()}"
    }
    
    # 准备文件
    try:
        with open(file_path, "rb") as f:
            file_content = f.read()
            
        files = {
            "file": (filename, file_content, "application/octet-stream")
        }
        
        logger.info(f"文件已准备: {filename}, 大小: {len(file_content)} 字节")
        
        # 发送请求
        try:
            response = requests.post(
                f"{TEST_API_URL}/api/v1/files/upload",
                headers=headers,
                data=form_data,
                files=files,
                timeout=30
            )
            
            # 检查响应
            if response.status_code == 201:
                logger.info(f"上传成功: {response.status_code} {response.reason}")
                logger.info(f"响应内容: {response.text}")
                return response.json()
            else:
                logger.error(f"上传失败: {response.status_code} {response.reason}")
                logger.error(f"响应内容: {response.text}")
                return None
        except Exception as e:
            logger.error(f"请求失败: {str(e)}")
            return None
    except Exception as e:
        logger.error(f"准备文件失败: {str(e)}")
        return None

def test_multi_uploads():
    """测试多文件上传"""
    logger.info("=== 开始多文件上传测试 ===")
    
    file_sizes = [10, 100, 500, 1000]  # KB
    success_count = 0
    
    for size in file_sizes:
        # 创建测试文件
        file_path, filename = create_test_file(size_kb=size)
        
        # 上传文件
        result = test_file_upload(file_path, filename, category=f"test-{size}kb")
        
        if result:
            success_count += 1
        
        # 删除测试文件
        try:
            os.unlink(file_path)
        except Exception as e:
            logger.warning(f"清理测试文件失败: {str(e)}")
    
    logger.info(f"测试完成: {success_count}/{len(file_sizes)} 成功")
    return success_count

async def main():
    """主函数"""
    logger.info("=== 文件上传测试 ===")
    
    if not API_TOKEN:
        logger.warning("未设置API_TOKEN环境变量，可能需要认证")
    
    # 进行单个文件上传测试
    file_path, filename = create_test_file(size_kb=50)
    result = test_file_upload(file_path, filename)
    
    if result:
        logger.info("单文件上传测试成功")
        
        # 测试文件下载
        file_id = result["id"]
        file_url = f"{TEST_API_URL}/api/v1/files/{file_id}/download"
        
        try:
            headers = {}
            if API_TOKEN:
                headers["Authorization"] = f"Bearer {API_TOKEN}"
                
            response = requests.get(file_url, headers=headers)
            
            if response.status_code == 200:
                logger.info(f"文件下载成功: {response.status_code} {response.reason}")
                logger.info(f"下载文件大小: {len(response.content)} 字节")
            else:
                logger.error(f"文件下载失败: {response.status_code} {response.reason}")
        except Exception as e:
            logger.error(f"文件下载请求失败: {str(e)}")
    else:
        logger.error("单文件上传测试失败")
    
    # 清理测试文件
    try:
        os.unlink(file_path)
    except Exception as e:
        logger.warning(f"清理测试文件失败: {str(e)}")
    
    # 测试多文件上传
    success_count = test_multi_uploads()
    
    if success_count > 0:
        logger.info("上传测试整体成功")
        return True
    else:
        logger.error("所有上传测试均失败")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1) 
#!/usr/bin/env python3
"""
匿名文件上传测试脚本
不使用认证，用于测试文件处理逻辑
"""
import os
import sys
import requests
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("anon-upload-test")

# 测试配置
TEST_API_URL = "http://localhost:8000"

def create_test_file(size_kb=100):
    """创建测试文件"""
    # 创建临时目录
    Path("temp").mkdir(exist_ok=True)
    file_path = Path("temp") / f"test_anon_{size_kb}kb.txt"
    
    # 创建指定大小的随机数据
    with open(file_path, "wb") as f:
        f.write(os.urandom(size_kb * 1024))
    
    logger.info(f"创建测试文件: {file_path}, 大小: {size_kb}KB")
    return file_path

def test_upload(file_path):
    """测试上传文件"""
    # 准备表单数据
    with open(file_path, "rb") as f:
        files = {"file": (file_path.name, f, "application/octet-stream")}
        form_data = {
            "is_public": "true",
            "description": "匿名测试上传"
        }
        
        # 发送请求
        try:
            logger.info(f"正在上传文件: {file_path}")
            response = requests.post(
                f"{TEST_API_URL}/api/v1/files/public-upload",
                files=files,
                data=form_data
            )
            
            # 输出响应结果
            logger.info(f"上传响应状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text}")
            
            return response
        except Exception as e:
            logger.error(f"上传请求失败: {str(e)}")
            return None

def main():
    """主函数"""
    # 创建并上传测试文件
    file_path = create_test_file(size_kb=10)
    response = test_upload(file_path)
    
    # 清理测试文件
    try:
        os.unlink(file_path)
    except:
        pass
    
    # 检查结果
    if response and response.status_code in (200, 201):
        logger.info("测试成功!")
        return True
    else:
        logger.error("测试失败!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 
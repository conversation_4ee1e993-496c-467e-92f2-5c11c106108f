#!/usr/bin/env python3
"""
字节内容上传测试脚本
使用最简单的字节内容上传方式
"""
import os
import sys
import requests
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("byte-upload-test")

# 测试配置
TEST_API_URL = "http://localhost:8000"

def test_byte_upload():
    """使用直接的字节内容测试上传"""
    # 创建一个简单的字节内容 - 1KB的随机字节
    content = os.urandom(1024)
    logger.info(f"创建了1KB的随机字节")
    
    # 准备请求
    files = {"file": ("test_byte.bin", content, "application/octet-stream")}
    data = {
        "is_public": "true",
        "description": "纯字节测试上传"
    }
    
    # 发起请求
    try:
        logger.info("正在上传字节内容...")
        response = requests.post(
            f"{TEST_API_URL}/api/v1/files/public-upload",
            files=files,
            data=data
        )
        
        # 输出响应信息
        logger.info(f"上传响应状态码: {response.status_code}")
        logger.info(f"响应内容: {response.text}")
        
        if response.status_code in (200, 201):
            logger.info("上传成功!")
            return True
        else:
            logger.error("上传失败!")
            return False
    except Exception as e:
        logger.error(f"上传请求异常: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_byte_upload()
    sys.exit(0 if success else 1) 
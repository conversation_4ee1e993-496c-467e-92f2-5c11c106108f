#!/usr/bin/env python3
"""
MinIO文件存储测试脚本
测试匿名上传到MinIO后，通过预签名URL访问文件
"""
import os
import sys
import requests
import json
import logging
import hashlib
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("minio-file-test")

# 测试配置
TEST_API_URL = "http://localhost:8000"

def upload_to_minio_publicly():
    """匿名上传文件，期望存储到MinIO并返回预签名URL"""
    test_content = os.urandom(1024)  # 1KB的随机内容
    content_hash = hashlib.sha256(test_content).hexdigest()
    logger.info(f"创建了1KB的随机测试内容，哈希值: {content_hash}")
    
    files = {"file": ("minio_test_public.bin", test_content, "application/octet-stream")}
    data = {
        "is_public": "true", # 确保文件是公开的，以便获取预签名URL
        "description": "通过MinIO匿名上传的公开文件",
        "storage_type": "minio" # 显式指定使用MinIO存储
    }
    
    try:
        logger.info(f"正在上传公开文件到MinIO (预期)...")
        response = requests.post(
            f"{TEST_API_URL}/api/v1/files/public-upload", # 使用公共上传接口
            files=files,
            data=data
        )
        
        if response.status_code != 201:
            logger.error(f"上传失败，状态码: {response.status_code}")
            logger.error(f"错误信息: {response.text}")
            return None, None, None
            
        file_data = response.json()
        logger.info(f"文件上传调用成功: {json.dumps(file_data, indent=2)}")
        
        return file_data, content_hash, test_content
    except requests.exceptions.ConnectionError as e:
        logger.error(f"连接错误: 无法连接到服务器 {TEST_API_URL}。请确保服务器正在运行。错误: {e}")
        return None, None, None
    except Exception as e:
        logger.error(f"上传过程中出现异常: {str(e)}")
        return None, None, None

def test_presigned_url_access(file_data, original_hash, original_content):
    """测试通过预签名URL访问MinIO中的文件"""
    if not file_data or "file_url" not in file_data:
        logger.error("文件数据无效或缺少file_url")
        return False
        
    file_url = file_data.get("file_url")
    file_id = file_data.get("id")
    
    logger.info(f"文件ID: {file_id}")
    logger.info(f"获取到的文件URL (预期为MinIO预签名URL): {file_url}")
    
    if not file_url or not file_url.startswith("http"):
        logger.error("获取到的file_url不是一个有效的HTTP/HTTPS链接，可能不是预签名URL。")
        logger.error("如果STORAGE_TYPE已正确配置为minio，请检查MinIO服务和API端的URL生成逻辑。")
        return False
        
    try:
        logger.info(f"尝试通过预签名URL下载文件: {file_url}")
        response = requests.get(file_url, timeout=30) # 设置超时
        
        if response.status_code == 200:
            content = response.content
            download_hash = hashlib.sha256(content).hexdigest()
            
            logger.info(f"✅ 下载成功! 状态码: {response.status_code}, 大小: {len(content)} 字节")
            logger.info(f"下载内容的哈希值: {download_hash}")
            
            if download_hash == original_hash:
                logger.info("✅ 哈希验证成功: 上传内容与下载内容一致")
                if content == original_content:
                    logger.info("✅ 二进制内容完全匹配")
                    return True
                else:
                    logger.warning("⚠️ 二进制内容不完全匹配，但哈希相同。这可能不影响文件完整性。")
                    return True # 即使二进制略有差异，哈希一致也认为基本成功
            else:
                logger.error("❌ 哈希验证失败: 内容不一致!")
                logger.error(f"  原始哈希: {original_hash}")
                logger.error(f"  下载哈希: {download_hash}")
                # 可以考虑保存下载的文件进行分析
                # with open(f"downloaded_{file_id}.bin", "wb") as f:
                #     f.write(content)
                # logger.info(f"下载的文件已保存为 downloaded_{file_id}.bin")
                return False
        else:
            logger.error(f"❌ 通过预签名URL下载失败! 状态码: {response.status_code}")
            logger.error(f"错误响应内容: {response.text[:500]}...") # 显示部分错误信息
            if response.status_code == 403:
                logger.error("  403 Forbidden: 预签名URL可能已过期、权限不足或MinIO存储桶/对象策略配置问题。")
            elif response.status_code == 404:
                logger.error("  404 Not Found: MinIO中可能找不到对应的对象。")
            return False
            
    except requests.exceptions.Timeout:
        logger.error(f"❌ 请求预签名URL超时: {file_url}")
        return False
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ 请求预签名URL时发生网络错误: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"❌ 测试预签名URL访问时发生未知异常: {str(e)}")
        return False

def main():
    logger.info("=== MinIO 文件存储 - 匿名上传与预签名URL访问测试 ===")
    
    file_data, content_hash, original_content = upload_to_minio_publicly()
    
    if not file_data or not content_hash or original_content is None:
        logger.error("文件上传失败或未返回必要信息，无法继续测试。")
        return False
    
    logger.info("等待几秒钟，确保MinIO对象信息同步...")
    time.sleep(3) # 短暂等待，以防MinIO处理略有延迟
    
    success = test_presigned_url_access(file_data, content_hash, original_content)
    
    if success:
        logger.info("✅ MinIO预签名URL访问测试成功!")
    else:
        logger.error("❌ MinIO预签名URL访问测试失败!")
        logger.info("请检查以下几点：")
        logger.info("  1. FastAPI应用的 .env 文件中 STORAGE_TYPE 是否正确设置为 'minio'。")
        logger.info("  2. MinIO服务是否正在运行且网络可达。")
        logger.info("  3. FastAPI应用中的MinIO连接配置 (endpoint, access_key, secret_key, bucket_name) 是否正确。")
        logger.info("  4. MinIO存储桶的访问策略是否允许公开读取或生成有效的预签名URL。")
        logger.info("  5. services/minio_file_storage.py 中生成 file_url 的逻辑是否正确 (特别是is_public=True的情况)。")

    logger.info("=== 测试执行完毕 ===")
    return success

if __name__ == "__main__":
    test_success = main()
    sys.exit(0 if test_success else 1) 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统定时任务设置脚本

用于自动设置以下定时任务：
1. 日志维护任务 - 清理、轮转和归档日志
2. API缓存清理任务 - 清理过期的API缓存和令牌
3. 数据库备份任务 - 定期备份数据库

支持多种调度器：
- Linux/Mac: crontab
- Windows: 任务计划程序
"""

import os
import sys
import platform
import subprocess
import argparse
import logging
import tempfile
from pathlib import Path
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("cron_setup")

# 项目根目录
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

# 定时任务定义
TASKS = {
    "log_maintenance": {
        "name": "日志维护",
        "command": f"cd {PROJECT_ROOT} && python3 tools/log_maintenance.py --all",
        "schedule": {
            "cron": "0 2 * * *",  # 每天凌晨2点执行
            "win": "daily", 
            "win_time": "02:00"
        },
        "description": "自动清理过期日志文件，执行日志轮转和归档"
    },
    "api_cache_clean": {
        "name": "API缓存清理",
        "command": f"cd {PROJECT_ROOT} && python3 tools/clean_cache.py --all",
        "schedule": {
            "cron": "0 1 * * *",  # 每天凌晨1点执行
            "win": "daily",
            "win_time": "01:00"
        },
        "description": "清理过期的API缓存数据和失效令牌"
    },
    "db_backup": {
        "name": "数据库备份",
        "command": f"cd {PROJECT_ROOT} && python3 tools/db_backup.py --compress",
        "schedule": {
            "cron": "0 3 * * *",  # 每天凌晨3点执行
            "win": "daily",
            "win_time": "03:00"
        },
        "description": "执行数据库自动备份"
    },
    "token_blacklist_clean": {
        "name": "令牌黑名单清理",
        "command": f"cd {PROJECT_ROOT} && python3 tools/clean_token_blacklist.py",
        "schedule": {
            "cron": "0 4 * * *",  # 每天凌晨4点执行
            "win": "daily",
            "win_time": "04:00"
        },
        "description": "清理过期的令牌黑名单记录"
    }
}

def is_windows():
    """检查是否为Windows系统"""
    return platform.system() == "Windows"

def is_macos():
    """检查是否为macOS系统"""
    return platform.system() == "Darwin"

def is_linux():
    """检查是否为Linux系统"""
    return platform.system() == "Linux"

def setup_crontab(tasks, user=None):
    """
    在Linux/macOS上设置crontab
    
    Args:
        tasks: 要设置的任务字典
        user: 可选的用户名，如果为None则为当前用户
    """
    # 获取当前的crontab
    cmd = ["crontab", "-l"]
    if user:
        cmd.extend(["-u", user])
        
    try:
        current_crontab = subprocess.check_output(cmd, stderr=subprocess.PIPE).decode("utf-8")
    except subprocess.CalledProcessError:
        # 如果crontab为空或不存在
        current_crontab = ""
    
    # 为脚本任务创建标记
    start_marker = "# === 开始: Admin Server自动任务 ==="
    end_marker = "# === 结束: Admin Server自动任务 ==="
    
    # 从现有crontab中移除旧的任务部分
    if start_marker in current_crontab and end_marker in current_crontab:
        start_idx = current_crontab.find(start_marker)
        end_idx = current_crontab.find(end_marker) + len(end_marker)
        current_crontab = current_crontab[:start_idx] + current_crontab[end_idx:]
    
    # 添加新的任务
    new_crontab = current_crontab.rstrip() + "\n\n" if current_crontab.strip() else ""
    new_crontab += f"{start_marker}\n"
    new_crontab += f"# 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    
    for task_id, task in tasks.items():
        if not task.get("enabled", True):
            continue
            
        new_crontab += f"# {task['name']}: {task['description']}\n"
        new_crontab += f"{task['schedule']['cron']} {task['command']}\n\n"
    
    new_crontab += f"{end_marker}\n"
    
    # 写入临时文件
    with tempfile.NamedTemporaryFile(mode="w", delete=False) as temp_file:
        temp_file.write(new_crontab)
        temp_file_path = temp_file.name
    
    # 安装新的crontab
    cmd = ["crontab"]
    if user:
        cmd.extend(["-u", user])
    cmd.append(temp_file_path)
    
    try:
        subprocess.run(cmd, check=True)
        logger.info(f"成功设置crontab" + (f" (用户: {user})" if user else ""))
    except subprocess.CalledProcessError as e:
        logger.error(f"设置crontab失败: {str(e)}")
    finally:
        # 清理临时文件
        os.unlink(temp_file_path)

def setup_windows_tasks(tasks):
    """
    在Windows上设置任务计划
    
    Args:
        tasks: 要设置的任务字典
    """
    for task_id, task in tasks.items():
        if not task.get("enabled", True):
            continue
            
        # 删除现有任务（如果存在）
        subprocess.run(
            ["schtasks", "/Delete", "/TN", f"AdminServer\\{task_id}", "/F"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 创建新任务
        cmd = [
            "schtasks", "/Create", "/TN", f"AdminServer\\{task_id}",
            "/TR", task["command"],
            "/SC", task["schedule"]["win"],
            "/ST", task["schedule"]["win_time"],
            "/RU", "SYSTEM",
            "/RL", "HIGHEST",
            "/F"
        ]
        
        try:
            subprocess.run(cmd, check=True)
            logger.info(f"成功设置任务: {task['name']}")
        except subprocess.CalledProcessError as e:
            logger.error(f"设置任务 {task['name']} 失败: {str(e)}")

def list_tasks():
    """列出所有可用的定时任务"""
    logger.info("可用的定时任务:")
    
    for task_id, task in TASKS.items():
        schedule = task["schedule"]["cron"] if not is_windows() else f"{task['schedule']['win']} at {task['schedule']['win_time']}"
        logger.info(f"* {task_id}: {task['name']}")
        logger.info(f"  描述: {task['description']}")
        logger.info(f"  计划: {schedule}")
        logger.info(f"  命令: {task['command']}")
        logger.info("")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="系统定时任务设置工具")
    
    parser.add_argument(
        "--list",
        action="store_true",
        help="列出所有可用的定时任务"
    )
    
    parser.add_argument(
        "--setup",
        action="store_true",
        help="设置所有定时任务"
    )
    
    parser.add_argument(
        "--tasks",
        nargs="+",
        help="要设置的特定任务ID列表，如不指定则设置所有任务"
    )
    
    parser.add_argument(
        "--user",
        type=str,
        help="crontab用户名（仅适用于Linux/macOS）"
    )
    
    args = parser.parse_args()
    
    # 如果没有指定任何操作，显示帮助信息
    if not (args.list or args.setup):
        parser.print_help()
        return
    
    # 列出任务
    if args.list:
        list_tasks()
        return
    
    # 筛选任务
    selected_tasks = {}
    if args.tasks:
        # 只选择指定的任务
        for task_id in args.tasks:
            if task_id in TASKS:
                selected_tasks[task_id] = TASKS[task_id]
            else:
                logger.warning(f"未知任务ID: {task_id}")
    else:
        # 选择所有任务
        selected_tasks = TASKS
    
    # 设置任务
    if args.setup:
        if is_windows():
            setup_windows_tasks(selected_tasks)
        else:  # Linux/macOS
            setup_crontab(selected_tasks, args.user)
    
if __name__ == "__main__":
    main() 
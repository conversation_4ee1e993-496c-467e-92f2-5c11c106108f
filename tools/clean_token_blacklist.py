#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
令牌黑名单清理工具

清理过期的令牌黑名单记录，包括：
1. Redis中的令牌黑名单
2. 清理IP黑名单
"""

import os
import sys
import asyncio
import argparse
import logging
from datetime import datetime, timedelta

# 添加项目根目录到系统路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, project_root)

# 导入项目模块
from utils.logger import init_logging, app_logger
from db.redis import get_redis_pool
from core.config import settings

# 设置日志
def setup_logging():
    """设置日志"""
    init_logging()
    logger = app_logger
    logger.info("令牌黑名单清理工具已启动")
    return logger

# 清理Redis中的令牌黑名单
async def clean_token_blacklist(days=1, dry_run=False):
    """
    清理Redis中的令牌黑名单
    
    Args:
        days: 保留天数，如果为0则清理所有已过期的令牌
        dry_run: 是否只模拟执行而不实际删除
        
    Returns:
        清理的记录数
    """
    logger = app_logger
    
    # 获取Redis连接池
    redis_pool = get_redis_pool()
    if not redis_pool:
        logger.error("无法获取Redis连接池")
        return 0
    
    redis = redis_pool.connection()
    
    # 获取所有令牌黑名单记录
    blacklist_keys = await redis.keys("token:blacklist:*")
    
    if not blacklist_keys:
        logger.info("没有令牌黑名单记录需要清理")
        return 0
    
    logger.info(f"共找到 {len(blacklist_keys)} 个令牌黑名单记录")
    
    # 统计
    cleaned_count = 0
    
    # 如果days=0，表示只清理已过期的记录
    if days == 0:
        for key in blacklist_keys:
            # 检查TTL
            ttl = await redis.ttl(key)
            if ttl <= 0:  # 已过期或永不过期的记录
                if dry_run:
                    logger.info(f"将删除已过期的令牌黑名单: {key.decode()}")
                else:
                    await redis.delete(key)
                    logger.info(f"已删除已过期的令牌黑名单: {key.decode()}")
                cleaned_count += 1
    else:
        # 清理超过指定天数的记录
        cutoff_seconds = days * 86400  # 转换为秒
        
        for key in blacklist_keys:
            # 获取TTL
            ttl = await redis.ttl(key)
            
            # 如果TTL小于0，表示已过期或永不过期
            if ttl < 0:
                if dry_run:
                    logger.info(f"将删除已过期的令牌黑名单: {key.decode()}")
                else:
                    await redis.delete(key)
                    logger.info(f"已删除已过期的令牌黑名单: {key.decode()}")
                cleaned_count += 1
                continue
                
            # 获取记录的总有效期
            # 这里我们无法直接知道记录的原始TTL，但可以假设是默认的24小时
            original_ttl = 86400  # 默认24小时
            
            # 如果记录已经存在超过n天，删除它
            elapsed_time = original_ttl - ttl
            if elapsed_time > cutoff_seconds:
                if dry_run:
                    logger.info(f"将删除旧的令牌黑名单: {key.decode()} (已存在约 {elapsed_time//3600} 小时)")
                else:
                    await redis.delete(key)
                    logger.info(f"已删除旧的令牌黑名单: {key.decode()} (已存在约 {elapsed_time//3600} 小时)")
                cleaned_count += 1
    
    return cleaned_count

# 清理IP黑名单
async def clean_ip_blacklist(days=1, dry_run=False):
    """
    清理Redis中的IP黑名单
    
    Args:
        days: 保留天数，如果为0则清理所有已过期的记录
        dry_run: 是否只模拟执行而不实际删除
        
    Returns:
        清理的记录数
    """
    logger = app_logger
    
    # 获取Redis连接池
    redis_pool = get_redis_pool()
    if not redis_pool:
        logger.error("无法获取Redis连接池")
        return 0
    
    redis = redis_pool.connection()
    
    # 获取所有IP黑名单记录
    blacklist_keys = await redis.keys("ip:blacklist:*")
    
    if not blacklist_keys:
        logger.info("没有IP黑名单记录需要清理")
        return 0
    
    logger.info(f"共找到 {len(blacklist_keys)} 个IP黑名单记录")
    
    # 统计
    cleaned_count = 0
    
    # 如果days=0，表示只清理已过期的记录
    if days == 0:
        for key in blacklist_keys:
            # 检查TTL
            ttl = await redis.ttl(key)
            if ttl <= 0:  # 已过期或永不过期的记录
                if dry_run:
                    logger.info(f"将删除已过期的IP黑名单: {key.decode()}")
                else:
                    await redis.delete(key)
                    logger.info(f"已删除已过期的IP黑名单: {key.decode()}")
                cleaned_count += 1
    else:
        # 清理超过指定天数的记录
        cutoff_seconds = days * 86400  # 转换为秒
        
        for key in blacklist_keys:
            # 获取TTL
            ttl = await redis.ttl(key)
            
            # 如果TTL小于0，表示已过期或永不过期
            if ttl < 0:
                if dry_run:
                    logger.info(f"将删除已过期的IP黑名单: {key.decode()}")
                else:
                    await redis.delete(key)
                    logger.info(f"已删除已过期的IP黑名单: {key.decode()}")
                cleaned_count += 1
                continue
                
            # 获取记录的总有效期
            # 这里我们无法直接知道记录的原始TTL，但可以假设是默认的3600秒（1小时）
            original_ttl = 3600  # 默认1小时
            
            # 如果记录已经存在超过n天，删除它
            elapsed_time = original_ttl - ttl
            if elapsed_time > cutoff_seconds:
                if dry_run:
                    logger.info(f"将删除旧的IP黑名单: {key.decode()} (已存在约 {elapsed_time//60} 分钟)")
                else:
                    await redis.delete(key)
                    logger.info(f"已删除旧的IP黑名单: {key.decode()} (已存在约 {elapsed_time//60} 分钟)")
                cleaned_count += 1
    
    return cleaned_count

# 清理客户端吊销标记
async def clean_revoked_clients(days=30, dry_run=False):
    """
    清理客户端吊销标记
    
    Args:
        days: 保留天数
        dry_run: 是否只模拟执行而不实际删除
        
    Returns:
        清理的记录数
    """
    logger = app_logger
    
    # 获取Redis连接池
    redis_pool = get_redis_pool()
    if not redis_pool:
        logger.error("无法获取Redis连接池")
        return 0
    
    redis = redis_pool.connection()
    
    # 获取所有客户端吊销标记
    revoked_keys = await redis.keys("revoked:client:*")
    
    if not revoked_keys:
        logger.info("没有客户端吊销标记需要清理")
        return 0
    
    logger.info(f"共找到 {len(revoked_keys)} 个客户端吊销标记")
    
    # 统计
    cleaned_count = 0
    
    # 清理超过指定天数的记录
    cutoff_time = datetime.now() - timedelta(days=days)
    cutoff_timestamp = cutoff_time.timestamp()
    
    for key in revoked_keys:
        # 获取吊销标记的时间戳
        timestamp_str = await redis.get(key)
        if not timestamp_str:
            continue
            
        try:
            timestamp = float(timestamp_str)
            revoke_time = datetime.fromtimestamp(timestamp)
            
            # 如果吊销时间早于截止时间，删除它
            if revoke_time < cutoff_time:
                if dry_run:
                    logger.info(f"将删除旧的客户端吊销标记: {key.decode()} (吊销于 {revoke_time})")
                else:
                    await redis.delete(key)
                    logger.info(f"已删除旧的客户端吊销标记: {key.decode()} (吊销于 {revoke_time})")
                cleaned_count += 1
        except (ValueError, TypeError):
            # 如果时间戳格式不正确，也删除它
            if dry_run:
                logger.info(f"将删除无效的客户端吊销标记: {key.decode()}")
            else:
                await redis.delete(key)
                logger.info(f"已删除无效的客户端吊销标记: {key.decode()}")
            cleaned_count += 1
    
    return cleaned_count

async def main_async():
    """异步主函数"""
    parser = argparse.ArgumentParser(description="令牌黑名单清理工具")
    
    parser.add_argument(
        "--token",
        action="store_true",
        help="清理令牌黑名单"
    )
    
    parser.add_argument(
        "--ip",
        action="store_true",
        help="清理IP黑名单"
    )
    
    parser.add_argument(
        "--revoked",
        action="store_true",
        help="清理客户端吊销标记"
    )
    
    parser.add_argument(
        "--days",
        type=int,
        default=1,
        help="保留天数，默认为1天，0表示只清理已过期记录"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="模拟执行，不实际删除记录"
    )
    
    parser.add_argument(
        "--all",
        action="store_true",
        help="清理所有类型的记录"
    )
    
    args = parser.parse_args()
    
    # 如果未指定任何操作，显示帮助信息
    if not (args.token or args.ip or args.revoked or args.all):
        parser.print_help()
        return
    
    # 设置日志
    logger = setup_logging()
    
    # 记录执行信息
    logger.info(f"=== 令牌黑名单清理工具开始执行: {datetime.now()} ===")
    if args.dry_run:
        logger.info("模拟模式: 不会实际删除记录")
    
    # 执行清理操作
    token_count = 0
    ip_count = 0
    revoked_count = 0
    
    if args.token or args.all:
        logger.info("=== 清理令牌黑名单 ===")
        token_count = await clean_token_blacklist(args.days, args.dry_run)
        logger.info(f"已清理 {token_count} 个令牌黑名单记录")
    
    if args.ip or args.all:
        logger.info("=== 清理IP黑名单 ===")
        ip_count = await clean_ip_blacklist(args.days, args.dry_run)
        logger.info(f"已清理 {ip_count} 个IP黑名单记录")
    
    if args.revoked or args.all:
        logger.info("=== 清理客户端吊销标记 ===")
        # 客户端吊销标记默认保留30天
        days = 30 if args.days == 1 else args.days
        revoked_count = await clean_revoked_clients(days, args.dry_run)
        logger.info(f"已清理 {revoked_count} 个客户端吊销标记")
    
    # 记录总结
    total_count = token_count + ip_count + revoked_count
    logger.info(f"=== 令牌黑名单清理工具执行完成: {datetime.now()} ===")
    logger.info(f"总共清理了 {total_count} 条记录")

def main():
    """主函数"""
    asyncio.run(main_async())

if __name__ == "__main__":
    main() 
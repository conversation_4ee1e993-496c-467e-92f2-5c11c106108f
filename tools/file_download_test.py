#!/usr/bin/env python3
"""
文件获取和下载测试脚本
测试API获取文件信息和下载文件的功能
"""
import os
import sys
import json
import requests
import logging
import tempfile
import hashlib

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("file-download-test")

# 测试配置
TEST_API_URL = "http://localhost:8000"
TEST_USERNAME = "<EMAIL>"  # 替换为实际测试用户
TEST_PASSWORD = "admin"  # 替换为实际密码

def get_access_token():
    """登录获取访问令牌"""
    # 尝试多个可能的登录端点
    login_endpoints = [
        "/api/v1/auth-settings/jwt/login",
        "/api/v1/auth-settings/flexible-login",
        "/api/v1/auth/login/access-token", 
        "/api/v1/auth/jwt/login"
    ]
    
    # OAuth2表单认证
    form_data = {
        "username": TEST_USERNAME,
        "password": TEST_PASSWORD
    }
    
    # 直接JSON认证
    json_data = {
        "username": TEST_USERNAME,
        "password": TEST_PASSWORD
    }
    
    # 逐一尝试各登录端点
    for endpoint in login_endpoints:
        try:
            logger.info(f"尝试登录端点: {endpoint}")
            
            # 尝试表单提交方式
            response = requests.post(
                f"{TEST_API_URL}{endpoint}",
                data=form_data
            )
            
            if response.status_code == 200 or response.status_code == 201:
                token_data = response.json()
                access_token = token_data.get("access_token")
                if access_token:
                    logger.info(f"在端点 {endpoint} 登录成功")
                    return access_token
                    
            # 如果表单方式失败，尝试JSON方式
            response = requests.post(
                f"{TEST_API_URL}{endpoint}",
                json=json_data
            )
            
            if response.status_code == 200 or response.status_code == 201:
                token_data = response.json()
                access_token = token_data.get("access_token")
                if access_token:
                    logger.info(f"在端点 {endpoint} 登录成功")
                    return access_token
                    
        except Exception as e:
            logger.warning(f"尝试端点 {endpoint} 失败: {str(e)}")
            continue
    
    logger.error("所有登录尝试都失败")
    return None

def test_public_upload():
    """匿名上传文件测试"""
    # 创建测试内容
    test_content = os.urandom(2048)  # 2KB随机内容
    content_hash = hashlib.sha256(test_content).hexdigest()
    logger.info(f"创建了2KB的随机测试内容，哈希值: {content_hash}")
    
    # 上传文件
    files = {"file": ("test_file.bin", test_content, "application/octet-stream")}
    data = {
        "is_public": "true",
        "description": "测试获取和下载的文件"
    }
    
    try:
        logger.info("正在上传测试文件...")
        upload_response = requests.post(
            f"{TEST_API_URL}/api/v1/files/public-upload",
            files=files,
            data=data
        )
        
        if upload_response.status_code not in (200, 201):
            logger.error(f"上传失败，状态码: {upload_response.status_code}")
            logger.error(f"错误信息: {upload_response.text}")
            return None, None
            
        # 提取上传的文件ID
        upload_data = upload_response.json()
        file_id = upload_data["id"]
        logger.info(f"文件上传成功，文件ID: {file_id}")
        
        return file_id, content_hash
    except Exception as e:
        logger.error(f"上传过程中出现异常: {str(e)}")
        return None, None

def test_file_download(file_id, original_hash=None, access_token=None):
    """测试下载文件并验证哈希值"""
    try:
        # 获取文件信息
        logger.info(f"正在获取文件信息: {file_id}")
        
        headers = {}
        if access_token:
            headers["Authorization"] = f"Bearer {access_token}"
            
        info_response = requests.get(
            f"{TEST_API_URL}/api/v1/files/{file_id}",
            headers=headers
        )
        
        if info_response.status_code != 200:
            logger.error(f"获取文件信息失败，状态码: {info_response.status_code}")
            logger.error(f"错误信息: {info_response.text}")
            
            # 尝试其他获取方式
            logger.info("尝试直接下载文件...")
            return test_direct_download(file_id, original_hash, access_token)
            
        file_info = info_response.json()
        logger.info(f"成功获取文件信息: {json.dumps(file_info, indent=2)}")
        
        # 下载文件
        logger.info(f"正在下载文件: {file_id}")
        download_response = requests.get(
            f"{TEST_API_URL}/api/v1/files/{file_id}/download",
            headers=headers
        )
        
        if download_response.status_code != 200:
            logger.error(f"下载文件失败，状态码: {download_response.status_code}")
            logger.error(f"错误信息: {download_response.text}")
            
            # 尝试通过文件URL下载
            if "file_url" in file_info and file_info["file_url"]:
                return test_url_download(file_info["file_url"], original_hash)
            
            return False
        
        # 验证下载的内容
        downloaded_content = download_response.content
        downloaded_hash = hashlib.sha256(downloaded_content).hexdigest()
        
        logger.info(f"下载的文件大小: {len(downloaded_content)} 字节")
        logger.info(f"下载的文件哈希值: {downloaded_hash}")
        
        # 如果提供了原始哈希值，验证是否匹配
        if original_hash:
            if downloaded_hash == original_hash:
                logger.info("✅ 验证成功: 上传的内容和下载的内容哈希值匹配")
            else:
                logger.error("❌ 验证失败: 哈希值不匹配!")
                logger.error(f"原始哈希: {original_hash}")
                logger.error(f"下载哈希: {downloaded_hash}")
                return False
        
        # 保存文件到临时目录
        with tempfile.NamedTemporaryFile(delete=False, suffix=".bin") as temp_file:
            temp_file.write(downloaded_content)
            temp_path = temp_file.name
        
        logger.info(f"文件已保存到: {temp_path}")
        return True
        
    except Exception as e:
        logger.error(f"测试下载过程中出现异常: {str(e)}")
        return False

def test_direct_download(file_id, original_hash=None, access_token=None):
    """尝试直接从下载端点获取文件"""
    try:
        logger.info(f"尝试直接下载文件: {file_id}")
        
        headers = {}
        if access_token:
            headers["Authorization"] = f"Bearer {access_token}"
            
        # 尝试多个可能的下载路径
        download_paths = [
            f"/api/v1/files/{file_id}/download",
            f"/storage/files/{file_id}",
            f"/storage/{file_id}",
            f"/storage/{file_id[:2]}/{file_id[2:4]}/{file_id}"
        ]
        
        for path in download_paths:
            try:
                download_response = requests.get(
                    f"{TEST_API_URL}{path}",
                    headers=headers
                )
                
                if download_response.status_code == 200:
                    logger.info(f"成功从 {path} 下载文件")
                    downloaded_content = download_response.content
                    downloaded_hash = hashlib.sha256(downloaded_content).hexdigest()
                    
                    logger.info(f"下载的文件大小: {len(downloaded_content)} 字节")
                    logger.info(f"下载的文件哈希值: {downloaded_hash}")
                    
                    if original_hash and downloaded_hash != original_hash:
                        logger.warning("⚠️ 哈希值不匹配!")
                        
                    return True
            except Exception as e:
                logger.warning(f"从 {path} 下载失败: {str(e)}")
        
        logger.error("所有下载尝试都失败")
        return False
    except Exception as e:
        logger.error(f"直接下载测试中出现异常: {str(e)}")
        return False

def test_url_download(file_url, original_hash=None):
    """通过文件URL下载"""
    try:
        logger.info(f"尝试通过URL下载文件: {file_url}")
        
        # 如果URL是相对路径，添加基础URL
        if not file_url.startswith("http"):
            file_url = f"{TEST_API_URL}{file_url}"
            
        response = requests.get(file_url)
        
        if response.status_code == 200:
            downloaded_content = response.content
            downloaded_hash = hashlib.sha256(downloaded_content).hexdigest()
            
            logger.info(f"通过URL下载的文件大小: {len(downloaded_content)} 字节")
            logger.info(f"通过URL下载的文件哈希值: {downloaded_hash}")
            
            if original_hash:
                if downloaded_hash == original_hash:
                    logger.info("✅ URL下载验证成功: 哈希值匹配")
                else:
                    logger.warning("❌ URL下载验证失败: 哈希值不匹配")
                    logger.warning(f"原始哈希: {original_hash}")
                    logger.warning(f"下载哈希: {downloaded_hash}")
                    
            return True
        else:
            logger.error(f"通过URL下载失败，状态码: {response.status_code}")
            logger.error(f"错误信息: {response.text}")
            return False
    except Exception as e:
        logger.error(f"URL下载测试中出现异常: {str(e)}")
        return False

def test_list_files(access_token=None):
    """测试获取文件列表"""
    try:
        logger.info("正在获取文件列表...")
        
        headers = {}
        if access_token:
            headers["Authorization"] = f"Bearer {access_token}"
            
        response = requests.get(
            f"{TEST_API_URL}/api/v1/files/?limit=10",
            headers=headers
        )
        
        if response.status_code != 200:
            logger.error(f"获取文件列表失败，状态码: {response.status_code}")
            logger.error(f"错误信息: {response.text}")
            if access_token is None:
                logger.warning("未提供认证令牌，访问受限")
            return False
            
        files_data = response.json()
        logger.info(f"成功获取文件列表，共 {files_data.get('total', 0)} 个文件")
        
        if files_data.get("items"):
            for i, file in enumerate(files_data["items"][:5]):  # 只显示前5个
                logger.info(f"文件 {i+1}: ID={file.get('id')}, 名称={file.get('filename')}")
        
        return True
    except Exception as e:
        logger.error(f"获取文件列表时出现异常: {str(e)}")
        return False

def run_complete_test():
    """运行完整的上传下载测试流程"""
    # 获取访问令牌
    access_token = get_access_token()
    
    if access_token:
        logger.info("已获取访问令牌，将使用认证方式访问API")
    else:
        logger.warning("未能获取访问令牌，将尝试匿名访问（部分功能可能受限）")
    
    # 测试文件列表
    logger.info("------- 测试文件列表 -------")
    test_list_files(access_token)
    
    # 上传并测试下载
    logger.info("------- 测试上传和下载 -------")
    file_id, content_hash = test_public_upload()
    
    if file_id:
        logger.info("测试文件上传成功，准备测试下载...")
        download_success = test_file_download(file_id, content_hash, access_token)
        return download_success
    else:
        logger.error("测试文件上传失败，无法继续下载测试")
        return False

def test_known_file(file_id):
    """测试下载已知文件ID的文件"""
    # 获取访问令牌
    access_token = get_access_token()
    
    if access_token:
        logger.info("已获取访问令牌，将使用认证方式访问API")
    else:
        logger.warning("未能获取访问令牌，将尝试匿名访问（部分功能可能受限）")
    
    logger.info(f"------- 测试下载已知文件: {file_id} -------")
    return test_file_download(file_id, None, access_token)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 如果提供了文件ID参数，测试下载指定文件
        file_id = sys.argv[1]
        success = test_known_file(file_id)
    else:
        # 否则执行完整测试
        success = run_complete_test()
    
    if success:
        logger.info("✅ 测试成功完成!")
    else:
        logger.error("❌ 测试失败!")
    
    sys.exit(0 if success else 1) 
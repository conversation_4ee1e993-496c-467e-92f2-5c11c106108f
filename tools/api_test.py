#!/usr/bin/env python3
"""
API测试脚本 - 测试用户认证和文件操作
使用测试用户信息进行登录、文件上传、查询和下载等操作
"""
import os
import sys
import requests
import json
from pprint import pprint
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath("."))

# 服务器URL
BASE_URL = "http://localhost:8000"  # 根据实际情况调整端口
API_V1 = "/api/v1"

# 测试用户信息
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

# 测试文件路径
TEST_FILE_PATH = "test.txt"

def login():
    """使用测试用户登录并获取JWT令牌"""
    print("正在登录...")
    
    try:
        # 使用标准OAuth2表单提交登录
        response = requests.post(
            f"{BASE_URL}{API_V1}/auth-settings/jwt/login",
            data={
                "username": TEST_USER["username"],
                "password": TEST_USER["password"]
            }
        )
        
        response.raise_for_status()
        
        # 提取访问令牌
        result = response.json()
        print(f"登录响应: {json.dumps(result, indent=2)}")
        
        access_token = result.get("access_token")
        
        if not access_token:
            # 尝试从标准响应格式中提取
            if "data" in result and "access_token" in result["data"]:
                access_token = result["data"]["access_token"]
            else:
                raise ValueError("响应中未找到访问令牌")
        
        print(f"登录成功: 获取到访问令牌 ({access_token[:10]}...)")
        return access_token
    except Exception as e:
        print(f"登录失败: {str(e)}")
        if response:
            print(f"响应内容: {response.text}")
        return None

def test_files_api(token):
    """测试文件上传、列表和下载"""
    # 创建测试文件
    test_content = f"这是一个测试文件 - {time.time()}"
    test_filename = f"test_{int(time.time())}.txt"
    
    with open(test_filename, "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print(f"创建测试文件: {test_filename}")
    
    try:
        # 准备文件上传
        print("\n测试文件上传:")
        
        with open(test_filename, "rb") as f:
            # 准备请求
            files = {"file": (test_filename, f, "text/plain")}
            data = {"is_public": "true", "category": "测试"}
            headers = {"Authorization": f"Bearer {token}"}
            
            print(f"上传请求: URL={BASE_URL}{API_V1}/files/upload")
            print(f"请求头: {headers}")
            print(f"表单数据: {data}")
            
            # 发送请求
            response = requests.post(
                f"{BASE_URL}{API_V1}/files/upload",
                files=files,
                data=data,
                headers=headers
            )
        
        # 输出结果
        print(f"上传状态码: {response.status_code}")
        print(f"上传响应头: {dict(response.headers)}")
        
        try:
            result = response.json()
            print(f"上传响应: {json.dumps(result, indent=2)}")
        except:
            print(f"上传响应: {response.text}")
            
        # 根据响应状态执行后续步骤
        if response.status_code == 201 or response.status_code == 200:
            print("文件上传成功")
            
            # 尝试从响应中提取文件ID
            file_id = None
            try:
                if "data" in result:
                    file_id = result["data"]["id"]
                else:
                    file_id = result["id"]
                    
                print(f"文件ID: {file_id}")
                
                # 获取文件列表
                print("\n测试文件列表:")
                list_response = requests.get(
                    f"{BASE_URL}{API_V1}/files/",
                    headers=headers
                )
                
                if list_response.status_code == 200:
                    list_result = list_response.json()
                    print(f"文件列表: {json.dumps(list_result, indent=2)}")
                else:
                    print(f"获取文件列表失败: {list_response.status_code}")
                    
                # 如果有文件ID，获取文件详情
                if file_id:
                    print(f"\n测试文件详情: {file_id}")
                    info_response = requests.get(
                        f"{BASE_URL}{API_V1}/files/{file_id}",
                        headers=headers
                    )
                    
                    if info_response.status_code == 200:
                        info_result = info_response.json()
                        print(f"文件详情: {json.dumps(info_result, indent=2)}")
                    else:
                        print(f"获取文件详情失败: {info_response.status_code}")
                    
                    # 下载文件
                    print(f"\n测试文件下载: {file_id}")
                    download_response = requests.get(
                        f"{BASE_URL}{API_V1}/files/{file_id}/download",
                        headers=headers,
                        stream=True
                    )
                    
                    if download_response.status_code == 200:
                        download_filename = f"downloaded_{test_filename}"
                        with open(download_filename, "wb") as f:
                            for chunk in download_response.iter_content(chunk_size=8192):
                                f.write(chunk)
                        print(f"文件下载成功，保存为: {download_filename}")
                        
                        # 验证内容
                        with open(download_filename, "r", encoding="utf-8") as f:
                            content = f.read()
                            if content == test_content:
                                print("文件内容验证成功")
                            else:
                                print(f"文件内容不匹配: '{content}' vs '{test_content}'")
                    else:
                        print(f"文件下载失败: {download_response.status_code}")
            except Exception as e:
                print(f"后续操作失败: {str(e)}")
        else:
            print("文件上传失败")
            
    except Exception as e:
        print(f"测试文件API失败: {str(e)}")
    finally:
        # 清理测试文件
        try:
            if os.path.exists(test_filename):
                os.remove(test_filename)
                print(f"删除测试文件: {test_filename}")
                
            download_filename = f"downloaded_{test_filename}"
            if os.path.exists(download_filename):
                os.remove(download_filename)
                print(f"删除下载文件: {download_filename}")
        except:
            pass

def run_tests():
    """运行API测试"""
    print("=" * 60)
    print("开始API测试")
    print("=" * 60)
    
    # 1. 登录获取令牌
    token = login()
    if not token:
        print("登录失败，终止测试")
        return
    
    print("\n" + "=" * 60)
    
    # 2. 测试文件API
    test_files_api(token)
    
    print("\n" + "=" * 60)
    print("API测试完成")
    print("=" * 60)

if __name__ == "__main__":
    # 调整BASE_URL端口
    import socket
    def check_port(port):
        """检查端口是否能连接"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            try:
                s.connect(("localhost", port))
                return True
            except:
                return False
    
    # 自动检测端口
    for port in [8000, 8080, 8888]:
        if check_port(port):
            BASE_URL = f"http://localhost:{port}"
            print(f"检测到服务器运行在: {BASE_URL}")
            break
    
    run_tests() 
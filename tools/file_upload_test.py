#!/usr/bin/env python3
"""
文件上传API测试脚本
专门测试文件上传和下载功能
"""
import requests
import json
import os
import sys
import time
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8000"
API_PATH = "/api/v1"
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """登录并获取令牌"""
    print("===== 登录获取令牌 =====")
    
    login_url = f"{BASE_URL}{API_PATH}/auth-settings/jwt/login"
    
    try:
        response = requests.post(
            login_url,
            data={
                "username": USERNAME,
                "password": PASSWORD
            }
        )
        
        print(f"登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            token = result.get("access_token")
            
            if token:
                print(f"成功获取令牌: {token[:10]}...")
                return token
            else:
                print(f"响应中没有令牌: {json.dumps(result, indent=2)}")
                return None
        else:
            print(f"登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"登录请求异常: {str(e)}")
        return None

def test_file_upload(token):
    """测试文件上传"""
    if not token:
        print("没有令牌，无法执行文件上传测试")
        return None
    
    print("\n===== 测试文件上传 =====")
    
    # 创建测试文件
    test_filename = f"test_upload_{int(time.time())}.txt"
    test_content = f"这是一个测试文件，创建于 {datetime.now().isoformat()}"
    
    with open(test_filename, "w", encoding="utf-8") as f:
        f.write(test_content)
    
    print(f"创建测试文件: {test_filename}")
    
    try:
        # 方法1：使用requests.post直接上传
        print("\n方法1: 使用requests.post直接上传")
        
        url = f"{BASE_URL}{API_PATH}/files/upload"
        headers = {"Authorization": f"Bearer {token}"}
        
        # 打印请求信息
        print(f"上传URL: {url}")
        print(f"请求头: {headers}")
        
        # 准备表单数据
        with open(test_filename, "rb") as f:
            files = {"file": (test_filename, f, "text/plain")}
            data = {"is_public": "true", "category": "测试"}
            
            print(f"表单数据: {data}")
            
            # 发送请求
            response = requests.post(
                url,
                headers=headers,
                files=files,
                data=data
            )
        
        # 分析响应
        print(f"上传状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        try:
            result = response.json()
            print(f"响应体: {json.dumps(result, indent=2)}")
            
            # 检查结果
            if response.status_code in (200, 201):
                file_id = None
                
                # 尝试从响应中提取文件ID
                if "data" in result and "id" in result["data"]:
                    file_id = result["data"]["id"]
                elif "id" in result:
                    file_id = result["id"]
                
                if file_id:
                    print(f"上传成功，文件ID: {file_id}")
                    return file_id
                else:
                    print("无法从响应中获取文件ID")
            else:
                print("文件上传失败")
                
                # 尝试使用curl命令
                print("\n尝试使用curl命令上传:")
                curl_cmd = (
                    f'curl -v -X POST "{url}" '
                    f'-H "Authorization: Bearer {token}" '
                    f'-F "file=@{test_filename}" '
                    f'-F "is_public=true" '
                    f'-F "category=测试"'
                )
                print(curl_cmd)
                
                try_curl = input("是否执行curl命令? (y/n): ")
                if try_curl.lower() == 'y':
                    print("\n执行curl命令:")
                    os.system(curl_cmd)
                
                return None
        except Exception as e:
            print(f"解析响应失败: {str(e)}")
            print(f"原始响应: {response.text}")
            return None
    finally:
        # 清理测试文件
        if os.path.exists(test_filename):
            os.remove(test_filename)
            print(f"删除测试文件: {test_filename}")

def test_file_list(token):
    """测试获取文件列表"""
    if not token:
        print("没有令牌，无法执行文件列表测试")
        return
    
    print("\n===== 测试文件列表 =====")
    
    url = f"{BASE_URL}{API_PATH}/files/"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"响应: {json.dumps(result, indent=2)}")
            
            # 提取文件列表
            files = []
            if "data" in result and "items" in result["data"]:
                files = result["data"]["items"]
            elif "items" in result:
                files = result["items"]
            
            if files:
                print(f"找到 {len(files)} 个文件:")
                for i, file in enumerate(files[:5]):  # 最多显示5个
                    print(f"  {i+1}. {file.get('filename')} ({file.get('id')})")
            else:
                print("未找到文件")
        else:
            print(f"获取文件列表失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {str(e)}")

def test_file_info_and_download(token, file_id):
    """测试获取文件信息和下载"""
    if not token or not file_id:
        print("没有令牌或文件ID，无法执行测试")
        return
    
    print(f"\n===== 测试文件详情和下载 =====")
    
    # 1. 获取文件信息
    print("\n获取文件信息:")
    info_url = f"{BASE_URL}{API_PATH}/files/{file_id}"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        info_response = requests.get(info_url, headers=headers)
        
        print(f"状态码: {info_response.status_code}")
        
        if info_response.status_code == 200:
            result = info_response.json()
            print(f"文件信息: {json.dumps(result, indent=2)}")
            
            # 提取文件名
            filename = None
            if "data" in result and "filename" in result["data"]:
                filename = result["data"]["filename"]
            elif "filename" in result:
                filename = result["filename"]
                
            # 2. 下载文件
            if filename:
                print(f"\n下载文件 {filename}:")
                
                download_url = f"{BASE_URL}{API_PATH}/files/{file_id}/download"
                download_response = requests.get(download_url, headers=headers, stream=True)
                
                print(f"下载状态码: {download_response.status_code}")
                
                if download_response.status_code == 200:
                    # 保存文件
                    save_as = f"downloaded_{filename}"
                    with open(save_as, "wb") as f:
                        for chunk in download_response.iter_content(chunk_size=8192):
                            f.write(chunk)
                    
                    print(f"文件已保存为: {save_as}")
                    
                    # 显示文件内容
                    try:
                        with open(save_as, "r") as f:
                            content = f.read()
                            print(f"文件内容: {content[:100]}...")
                    except:
                        print("无法读取文件内容")
                    
                    # 清理
                    if os.path.exists(save_as):
                        os.remove(save_as)
                        print(f"已删除下载的文件: {save_as}")
                else:
                    print(f"下载失败: {download_response.text}")
        else:
            print(f"获取文件信息失败: {info_response.text}")
    except Exception as e:
        print(f"请求异常: {str(e)}")

def main():
    """主函数"""
    print("========== 文件上传API测试 ==========")
    
    # 1. 登录获取令牌
    token = login()
    if not token:
        print("登录失败，终止测试")
        return
    
    # 2. 测试文件列表
    test_file_list(token)
    
    # 3. 测试文件上传
    file_id = test_file_upload(token)
    
    # 4. 测试文件详情和下载
    if file_id:
        test_file_info_and_download(token, file_id)
    
    print("\n========== 测试完成 ==========")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(0) 
#!/usr/bin/env python3
"""
简易文件上传测试脚本
"""
import os
import requests
import logging
from pathlib import Path
import sys

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("upload-test")

# 测试用户
TEST_USER = "<EMAIL>"
TEST_PASSWORD = "admin1234"
TEST_API_URL = "http://localhost:8000"

def login():
    """登录获取令牌"""
    try:
        # 尝试灵活登录接口
        response = requests.post(
            f"{TEST_API_URL}/api/v1/auth-settings/flexible-login",
            json={
                "username": TEST_USER,
                "password": TEST_PASSWORD
            },
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get("access_token")
            logger.info(f"获取令牌成功: {access_token[:10]}...")
            return access_token
        else:
            # 尝试另一个登录接口
            logger.warning(f"灵活登录失败: {response.status_code} - {response.text}")
            logger.info("尝试JWT登录接口...")
            
            response = requests.post(
                f"{TEST_API_URL}/api/v1/auth-settings/jwt/login",
                data={
                    "username": TEST_USER,
                    "password": TEST_PASSWORD
                }
            )
            
            if response.status_code == 200:
                token_data = response.json()
                access_token = token_data.get("access_token")
                logger.info(f"获取令牌成功: {access_token[:10]}...")
                return access_token
            else:
                logger.error(f"所有登录尝试均失败: {response.status_code} - {response.text}")
                return None
    except Exception as e:
        logger.error(f"登录请求异常: {str(e)}")
        return None

def create_test_file(size_kb=100):
    """创建测试文件"""
    # 创建临时目录
    Path("temp").mkdir(exist_ok=True)
    file_path = Path("temp") / f"test_file_{size_kb}kb.txt"
    
    # 创建指定大小的随机数据
    with open(file_path, "wb") as f:
        f.write(os.urandom(size_kb * 1024))
    
    logger.info(f"创建测试文件: {file_path}, 大小: {size_kb}KB")
    return file_path

def test_upload(file_path, token=None):
    """测试上传文件"""
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    # 准备表单数据
    with open(file_path, "rb") as f:
        files = {"file": (file_path.name, f, "application/octet-stream")}
        form_data = {
            "category": "test",
            "tags": "test,upload",
            "is_public": "true",
            "description": "测试上传文件"
        }
        
        # 发送请求
        try:
            logger.info(f"正在上传文件: {file_path}")
            response = requests.post(
                f"{TEST_API_URL}/api/v1/files/upload",
                headers=headers,
                files=files,
                data=form_data
            )
            
            # 输出响应结果
            logger.info(f"上传响应状态码: {response.status_code}")
            logger.info(f"响应内容: {response.text}")
            
            return response
        except Exception as e:
            logger.error(f"上传请求失败: {str(e)}")
            return None

def main():
    """主函数"""
    # 登录获取令牌
    token = login()
    
    if not token:
        logger.error("未能获取有效令牌，测试中断")
        return False
    
    # 创建并上传测试文件
    file_path = create_test_file(size_kb=50)
    response = test_upload(file_path, token)
    
    # 清理测试文件
    try:
        os.unlink(file_path)
    except:
        pass
    
    # 检查结果
    if response and response.status_code in (200, 201):
        logger.info("测试成功!")
        return True
    else:
        logger.error("测试失败!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 
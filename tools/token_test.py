#!/usr/bin/env python3
"""
JWT令牌验证测试脚本
测试不同端点的JWT令牌验证
"""
import requests
import json
import sys

# 配置
BASE_URL = "http://localhost:8000"
API_PATH = "/api/v1"
USERNAME = "admin"
PASSWORD = "admin123"

# 获取登录令牌
def get_token():
    """获取JWT令牌"""
    print("===== 测试登录获取令牌 =====")
    
    # 登录URL
    login_url = f"{BASE_URL}{API_PATH}/auth-settings/jwt/login"
    
    try:
        # 发送登录请求
        response = requests.post(
            login_url,
            data={
                "username": USERNAME,
                "password": PASSWORD
            }
        )
        
        print(f"登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            token = result.get("access_token")
            if token:
                print(f"成功获取令牌: {token[:10]}...")
                return token
            else:
                print(f"响应中没有令牌: {json.dumps(result, indent=2)}")
                return None
        else:
            print(f"登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"登录请求异常: {str(e)}")
        return None

# 测试API端点
def test_endpoint(endpoint, token=None, method="GET"):
    """测试指定的API端点"""
    url = f"{BASE_URL}{API_PATH}{endpoint}"
    print(f"\n===== 测试端点: {url} =====")
    
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            print(f"不支持的方法: {method}")
            return
        
        print(f"状态码: {response.status_code}")
        try:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2)}")
        except:
            print(f"响应: {response.text}")
    except Exception as e:
        print(f"请求异常: {str(e)}")

# 主函数
def main():
    """主函数"""
    print("========== JWT令牌验证测试 ==========")
    
    # 获取令牌
    token = get_token()
    
    # 测试不需要认证的端点
    print("\n----- 测试无需认证的端点 -----")
    test_endpoint("/health", token=None)
    
    # 测试需要认证的端点 - 不带令牌
    print("\n----- 测试需要认证的端点(不带令牌) -----")
    test_endpoint("/users/me", token=None)
    
    # 测试需要认证的端点 - 带令牌
    if token:
        print("\n----- 测试需要认证的端点(带令牌) -----")
        test_endpoint("/users/me", token=token)
    
    print("\n========== 测试完成 ==========")

if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
"""
测试登录修复

测试修复后的JWT令牌创建功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.security import create_access_token, create_refresh_token, generate_tokens
from core.config import settings


def test_token_creation():
    """测试令牌创建功能"""
    print("=== 测试令牌创建功能 ===")
    
    user_id = "1"
    
    try:
        # 测试create_access_token
        print("1. 测试create_access_token...")
        access_token = create_access_token(data=user_id)
        print(f"✅ 访问令牌创建成功: {access_token[:50]}...")
        
        # 测试create_refresh_token
        print("\n2. 测试create_refresh_token...")
        refresh_token = create_refresh_token(data=user_id)
        print(f"✅ 刷新令牌创建成功: {refresh_token[:50]}...")
        
        # 测试generate_tokens
        print("\n3. 测试generate_tokens...")
        access_token2, refresh_token2 = generate_tokens(subject=user_id)
        print(f"✅ 批量令牌创建成功:")
        print(f"   访问令牌: {access_token2[:50]}...")
        print(f"   刷新令牌: {refresh_token2[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 令牌创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_auth_endpoint():
    """测试认证端点"""
    print("\n=== 测试认证端点 ===")
    
    try:
        # 导入认证相关模块
        from api.api_v1.endpoints.auth import generate_tokens
        from auth.users import User
        
        # 创建一个模拟用户对象
        class MockUser:
            def __init__(self, user_id):
                self.id = user_id
        
        user = MockUser(1)
        
        # 测试generate_tokens函数
        print("测试generate_tokens函数...")
        token_result = await generate_tokens(user)
        
        print(f"✅ 认证端点令牌生成成功:")
        print(f"   访问令牌: {token_result.access_token[:50]}...")
        print(f"   刷新令牌: {token_result.refresh_token[:50]}...")
        print(f"   令牌类型: {token_result.token_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 认证端点测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_import_consistency():
    """测试导入一致性"""
    print("\n=== 测试导入一致性 ===")

    try:
        # 测试从不同模块导入的函数是否一致
        from core.security import create_access_token as core_create_access_token
        from api.api_v1.endpoints.auth import create_access_token as auth_create_access_token

        print("✅ 核心模块导入成功")
        print("✅ 认证模块导入成功")

        # 检查函数签名
        import inspect

        core_sig = inspect.signature(core_create_access_token)
        auth_sig = inspect.signature(auth_create_access_token)

        print(f"核心模块函数签名: {core_sig}")
        print(f"认证模块函数签名: {auth_sig}")

        # 测试调用
        user_id = "1"

        print("\n测试核心模块函数调用...")
        core_token = core_create_access_token(data=user_id)
        print(f"✅ 核心模块令牌: {core_token[:50]}...")

        print("\n测试认证模块函数调用...")
        auth_token = await auth_create_access_token(data={"sub": user_id})
        print(f"✅ 认证模块令牌: {auth_token[:50]}...")

        return True

    except Exception as e:
        print(f"❌ 导入一致性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("🔧 JWT令牌创建修复测试")
    print("=" * 50)
    
    # 显示配置信息
    print(f"JWT密钥: {settings.JWT_SECRET[:20]}...")
    print(f"JWT算法: {settings.JWT_ALGORITHM}")
    print(f"访问令牌过期时间: {settings.ACCESS_TOKEN_EXPIRE_MINUTES}分钟")
    print(f"刷新令牌过期时间: {settings.REFRESH_TOKEN_EXPIRE_DAYS}天")
    print()
    
    # 运行测试
    results = []
    
    # 测试1: 令牌创建功能
    results.append(test_token_creation())
    
    # 测试2: 认证端点
    results.append(await test_auth_endpoint())
    
    # 测试3: 导入一致性
    results.append(await test_import_consistency())
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"🎯 测试通过: {passed}/{total}")
    
    test_names = [
        "令牌创建功能",
        "认证端点",
        "导入一致性"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name}: {status}")
    
    if passed == total:
        print("\n🎉 所有测试通过！JWT令牌创建功能已修复。")
        return True
    else:
        print(f"\n⚠️  {total - passed} 个测试失败，需要进一步修复。")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

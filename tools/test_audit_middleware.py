#!/usr/bin/env python
"""
测试审计日志中间件 - 模拟HTTP请求并验证日志记录功能

此脚本创建一个简单的FastAPI应用，添加审计日志中间件，然后模拟请求验证中间件功能。
"""
import os
import sys
import json
import asyncio
import httpx
from datetime import datetime
from pathlib import Path
from fastapi import FastAPI, Request, Depends
from fastapi.testclient import TestClient
from sqlalchemy import select, delete

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent
sys.path.append(str(project_root))

from db.session import AsyncSessionLocal, get_db
from utils.audit_middleware import AuditLogMiddleware
from models.audit_log import AuditLog

# 创建测试应用
app = FastAPI(title="审计日志中间件测试")

# 添加审计日志中间件
app.add_middleware(
    AuditLogMiddleware,
    exclude_paths=["/excluded"],
    exclude_namespaces=["/static/"],
    include_request_body=True,
    filter_sensitive_data=True
)

# 测试端点 - 读取操作
@app.get("/api/v1/test")
async def test_read():
    """测试GET请求"""
    return {"message": "GET请求成功"}

# 测试端点 - 创建操作
@app.post("/api/v1/test")
async def test_create(request: Request):
    """测试POST请求"""
    body = await request.json()
    return {"message": "POST请求成功", "received": body}

# 测试端点 - 更新操作
@app.put("/api/v1/test/{item_id}")
async def test_update(item_id: int, request: Request):
    """测试PUT请求"""
    body = await request.json()
    return {"message": "PUT请求成功", "item_id": item_id, "received": body}

# 测试端点 - 删除操作
@app.delete("/api/v1/test/{item_id}")
async def test_delete(item_id: int):
    """测试DELETE请求"""
    return {"message": "DELETE请求成功", "item_id": item_id}

# 测试端点 - 排除的路径
@app.get("/excluded")
async def excluded_path():
    """这个路径被排除，不会记录日志"""
    return {"message": "这个请求不会被记录"}

# 查看审计日志
@app.get("/logs")
async def view_logs(db: AsyncSessionLocal = Depends(get_db)):
    """查看记录的审计日志"""
    query = select(AuditLog).order_by(AuditLog.created_at.desc()).limit(10)
    result = await db.execute(query)
    logs = result.scalars().all()
    
    return [
        {
            "id": log.id,
            "path": log.path,
            "method": log.method,
            "action": log.action,
            "resource": log.resource,
            "created_at": log.created_at.isoformat(),
            "request_body": log.request_body,
        }
        for log in logs
    ]

# 清理日志
@app.delete("/logs")
async def clear_logs(db: AsyncSessionLocal = Depends(get_db)):
    """清理测试产生的审计日志"""
    query = delete(AuditLog).where(AuditLog.path.like("/api/v1/test%"))
    await db.execute(query)
    await db.commit()
    return {"message": "测试日志已清理"}

async def main():
    """测试审计日志中间件"""
    # 创建测试客户端
    client = TestClient(app)
    
    print("开始测试审计日志中间件...")
    
    # 清理之前的测试数据
    response = client.delete("/logs")
    print(f"清理之前的测试数据: {response.json()}")
    
    # 1. 测试GET请求
    print("\n1. 测试GET请求...")
    response = client.get("/api/v1/test")
    print(f"响应: {response.status_code} {response.json()}")
    
    # 2. 测试POST请求，包含敏感数据
    print("\n2. 测试POST请求(包含敏感数据)...")
    response = client.post(
        "/api/v1/test",
        json={"name": "test", "password": "sensitive_password", "data": {"key": "value"}}
    )
    print(f"响应: {response.status_code} {response.json()}")
    
    # 3. 测试PUT请求
    print("\n3. 测试PUT请求...")
    response = client.put(
        "/api/v1/test/123",
        json={"name": "updated", "token": "secret_token"}
    )
    print(f"响应: {response.status_code} {response.json()}")
    
    # 4. 测试DELETE请求
    print("\n4. 测试DELETE请求...")
    response = client.delete("/api/v1/test/123")
    print(f"响应: {response.status_code} {response.json()}")
    
    # 5. 测试排除的路径
    print("\n5. 测试排除的路径...")
    response = client.get("/excluded")
    print(f"响应: {response.status_code} {response.json()}")
    
    # 等待一下让审计日志处理队列处理完成
    await asyncio.sleep(2)
    
    # 查询记录的日志
    print("\n查询记录的审计日志:")
    response = client.get("/logs")
    logs = response.json()
    
    print(f"共找到 {len(logs)} 条日志记录:")
    for log in logs:
        print(f"  路径: {log['path']}, 方法: {log['method']}, 操作: {log['action']}")
        if log['request_body']:
            print(f"  请求体: {json.dumps(log['request_body'], ensure_ascii=False)}")
    
    # 验证结果
    actions = [log['action'] for log in logs]
    print("\n验证结果:")
    print(f"  读取操作(read): {'read' in actions}")
    print(f"  创建操作(create): {'create' in actions}")
    print(f"  更新操作(update): {'update' in actions}")
    print(f"  删除操作(delete): {'delete' in actions}")
    
    # 清理测试数据
    response = client.delete("/logs")
    print(f"\n清理测试数据: {response.json()}")
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(main()) 
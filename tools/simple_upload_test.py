#!/usr/bin/env python3
"""
简单文件上传测试脚本
"""
import requests
import json
import os

# 基础URL
BASE_URL = "http://localhost:8000/api/v1"

# 测试用户
USERNAME = "admin"
PASSWORD = "admin123"

# 步骤1: 登录获取令牌
def get_token():
    """登录获取token"""
    print("=== 登录测试 ===")
    
    # 尝试多个登录端点
    endpoints = [
        "/auth-settings/jwt/login",  # 自定义JWT登录
        "/auth/jwt/login",          # 标准JWT登录
        "/auth/login",              # 通用登录
        "/auth/flexible-login"      # 灵活登录
    ]
    
    token = None
    
    for endpoint in endpoints:
        login_url = f"{BASE_URL}{endpoint}"
        print(f"尝试登录端点: {login_url}")
        
        try:
            # 请求令牌
            response = requests.post(
                login_url,
                data={
                    "username": USERNAME,
                    "password": PASSWORD
                } if "flexible" not in endpoint else json.dumps({
                    "username": USERNAME,
                    "password": PASSWORD
                }),
                headers={"Content-Type": "application/x-www-form-urlencoded"} 
                if "flexible" not in endpoint else 
                {"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"登录成功: {json.dumps(result, indent=2)}")
                
                # 提取令牌
                if "access_token" in result:
                    token = result["access_token"]
                    print(f"找到访问令牌: {token[:10]}...")
                    return token
                elif "data" in result and "access_token" in result["data"]:
                    token = result["data"]["access_token"]
                    print(f"找到访问令牌(在数据中): {token[:10]}...")
                    return token
            else:
                print(f"登录失败 ({response.status_code}): {response.text}")
        except Exception as e:
            print(f"请求错误: {str(e)}")
    
    return token

# 步骤2: 上传文件
def upload_file(token):
    """上传测试文件"""
    print("\n=== 文件上传测试 ===")
    
    if not token:
        print("没有令牌，无法上传文件")
        return
    
    # 创建临时测试文件
    test_file = "simple_test.txt"
    with open(test_file, "w") as f:
        f.write("这是一个简单的测试文件")
    
    try:
        # 准备上传请求
        url = f"{BASE_URL}/files/upload"
        print(f"上传文件到: {url}")
        
        # 准备headers (尝试多种格式)
        headers_list = [
            {"Authorization": f"Bearer {token}"},
            {"Authorization": f"bearer {token}"},
            {"Authorization": token}
        ]
        
        # 尝试不同的headers格式
        success = False
        
        for i, headers in enumerate(headers_list):
            print(f"\n尝试headers格式 #{i+1}: {headers}")
            
            try:
                with open(test_file, "rb") as f:
                    files = {"file": (test_file, f)}
                    data = {"is_public": "true"}
                    
                    response = requests.post(url, headers=headers, files=files, data=data)
                    
                    print(f"上传状态码: {response.status_code}")
                    try:
                        result = response.json()
                        print(f"上传响应: {json.dumps(result, indent=2)}")
                    except:
                        print(f"响应内容: {response.text}")
                    
                    if response.status_code in (200, 201):
                        print("上传成功!")
                        success = True
                        break
            except Exception as e:
                print(f"上传请求错误: {str(e)}")
        
        if not success:
            print("\n所有尝试均失败，打印curl命令供手动测试:")
            print(f'curl -X POST "{url}" -H "Authorization: Bearer {token}" -F "file=@{test_file}" -F "is_public=true"')
    finally:
        # 清理
        if os.path.exists(test_file):
            os.remove(test_file)

# 主函数
def main():
    """主函数"""
    token = get_token()
    upload_file(token)

if __name__ == "__main__":
    main() 
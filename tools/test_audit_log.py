#!/usr/bin/env python
"""
测试审计日志功能

此脚本直接使用CRUD函数添加一条审计日志，然后从数据库读取验证是否成功。
"""
import os
import sys
import asyncio
import json
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent
sys.path.append(str(project_root))

from db.session import AsyncSessionLocal
from crud.crud_audit_log import create_audit_log, get_audit_logs
from sqlalchemy import text

async def test_audit_log():
    """测试审计日志功能"""
    print("开始测试审计日志功能...")
    
    # 创建测试数据
    test_data = {
        "user_id": None,  # 匿名操作
        "username": "test_user",
        "action": "create",
        "resource": "test_resource",
        "resource_id": "123",
        "method": "POST",
        "path": "/api/v1/test",
        "query_params": {"test": "value"},
        "request_body": {"name": "Test Item", "password": "sensitive_data"},
        "response_code": 201,
        "ip_address": "127.0.0.1",
        "user_agent": "Test Script",
        "execution_time": 0.123,
        "details": {"server": "test-server"},
        "message": "测试审计日志功能",
        "success": True
    }
    
    print("\n1. 创建测试审计日志...")
    async with AsyncSessionLocal() as db:
        # 创建一条审计日志
        log = await create_audit_log(db=db, **test_data)
        print(f"创建成功: ID={log.id}")
        
        # 检查审计日志是否已保存到数据库
        print("\n2. 查询刚创建的审计日志...")
        logs = await get_audit_logs(
            db=db,
            resource="test_resource",
            limit=5
        )
        
        if logs:
            print(f"查询成功，找到 {len(logs)} 条记录")
            for log in logs:
                print(f"  ID: {log.id}")
                print(f"  用户: {log.username}")
                print(f"  操作: {log.action}")
                print(f"  资源: {log.resource}")
                print(f"  路径: {log.path}")
                print(f"  敏感数据处理测试: {json.dumps(log.request_body)}")
                print(f"  时间: {log.created_at}")
        else:
            print("未找到测试审计日志记录！")
        
        # 测试过滤查询
        print("\n3. 测试过滤条件查询...")
        filtered_logs = await get_audit_logs(
            db=db,
            username="test_user",
            action="create",
            success=True,
            limit=1
        )
        
        if filtered_logs:
            print(f"过滤查询成功，找到 {len(filtered_logs)} 条记录")
        else:
            print("过滤查询未找到记录！")
        
        # 删除测试数据
        print("\n4. 清理测试数据...")
        await db.execute(
            text("DELETE FROM audit_logs WHERE message = '测试审计日志功能'")
        )
        await db.commit()
        print("测试数据已清理")
    
    print("\n测试完成！")

if __name__ == "__main__":
    asyncio.run(test_audit_log()) 
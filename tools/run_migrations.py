#!/usr/bin/env python
"""
运行数据库迁移工具

使用方法:
    python tools/run_migrations.py [upgrade|downgrade|revision|heads|current] [选项]

选项:
    --revision <版本ID>  指定升级/降级到的版本号或头标签
    --message <消息>     创建新迁移时的消息
    --sql               生成SQL而不执行
"""
import os
import sys
import argparse
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent
sys.path.append(str(project_root))

from alembic.config import Config
from alembic import command

# 配置Alembic
def get_alembic_config():
    """获取Alembic配置"""
    alembic_cfg = Config(str(project_root / "alembic.ini"))
    alembic_cfg.set_main_option("script_location", "alembic_migrations")
    return alembic_cfg

async def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="数据库迁移工具")
    parser.add_argument(
        "action", 
        choices=["upgrade", "downgrade", "revision", "heads", "current", "merge"],
        help="迁移操作: 升级(upgrade)、降级(downgrade)、创建迁移(revision)、查看当前头(heads)、查看当前版本(current)、合并(merge)"
    )
    parser.add_argument("--revision", help="指定升级/降级到的版本号或头标签", default="head")
    parser.add_argument("--message", help="创建新迁移时的消息")
    parser.add_argument("--sql", action="store_true", help="生成SQL而不执行")
    parser.add_argument("--autogenerate", action="store_true", help="自动生成迁移")
    
    args = parser.parse_args()
    
    # 获取Alembic配置
    config = get_alembic_config()
    
    # 执行相应的操作
    try:
        if args.action == "upgrade":
            command.upgrade(config, args.revision, sql=args.sql)
            print(f"数据库已升级到版本: {args.revision}")
        
        elif args.action == "downgrade":
            command.downgrade(config, args.revision, sql=args.sql)
            print(f"数据库已降级到版本: {args.revision}")
        
        elif args.action == "revision":
            if not args.message:
                raise ValueError("创建新迁移需要提供消息参数 --message")
            
            command.revision(
                config, 
                message=args.message, 
                autogenerate=args.autogenerate
            )
            print(f"已创建新的迁移文件: {args.message}")
        
        elif args.action == "heads":
            command.heads(config)
        
        elif args.action == "current":
            command.current(config)
            
        elif args.action == "merge":
            if not args.message:
                raise ValueError("合并迁移需要提供消息参数 --message")
            
            command.merge(config, message=args.message)
            print(f"已创建合并迁移: {args.message}")
    
    except Exception as e:
        print(f"迁移操作失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 
#!/usr/bin/env python3
"""
Redis连接和缓存问题修复脚本

修复以下问题：
1. Redis连接错误
2. 'Redis' object has no attribute 'hmset_dict' 错误
3. 令牌缓存问题
"""
import os
import sys
import argparse

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath("."))

def check_redis_module():
    """检查Redis模块是否正确安装"""
    print("===== 检查Redis模块 =====")
    
    try:
        import redis
        print(f"Redis模块已安装: {redis.__version__}")
        
        # 检查关键函数
        client = redis.Redis()
        dir_results = dir(client)
        
        # 检查hmset_dict方法
        if 'hmset_dict' not in dir_results:
            print("警告: Redis客户端没有'hmset_dict'方法，这可能导致错误")
            print("Available hmset methods:", [m for m in dir_results if 'hmset' in m])
            
            # 检查Redis版本兼容性
            if hasattr(client, 'hmset'):
                print("发现'hmset'方法，可以用作替代")
            else:
                print("未找到hmset相关方法，需要更新Redis客户端")
        
        return True
    except ImportError:
        print("Redis模块未安装")
        print("建议运行: pip install redis")
        return False
    except Exception as e:
        print(f"检查Redis模块时出错: {str(e)}")
        return False

def fix_redis_service():
    """修复Redis服务模块中的问题"""
    print("\n===== 修复Redis服务模块 =====")
    
    redis_service_file = "services/redis_service.py"
    
    if not os.path.exists(redis_service_file):
        print(f"找不到Redis服务文件: {redis_service_file}")
        return False
    
    # 创建备份
    backup_file = f"{redis_service_file}.bak"
    if not os.path.exists(backup_file):
        import shutil
        shutil.copy2(redis_service_file, backup_file)
        print(f"已创建备份: {backup_file}")
    
    # 读取文件内容
    with open(redis_service_file, "r") as f:
        content = f.read()
    
    # 检查并修复hmset_dict错误
    if "hmset_dict" in content:
        content = content.replace(
            "redis_client.hmset_dict", 
            "redis_client.hmset"
        )
        print("修复: 将'hmset_dict'替换为'hmset'")
    
    # 保存修改
    with open(redis_service_file, "w") as f:
        f.write(content)
    
    print(f"已更新Redis服务文件: {redis_service_file}")
    return True

def fix_auth_cache():
    """修复认证缓存模块中的问题"""
    print("\n===== 修复认证缓存模块 =====")
    
    auth_cache_file = "utils/auth_cache.py"
    
    if not os.path.exists(auth_cache_file):
        print(f"找不到认证缓存文件: {auth_cache_file}")
        return False
    
    # 创建备份
    backup_file = f"{auth_cache_file}.bak"
    if not os.path.exists(backup_file):
        import shutil
        shutil.copy2(auth_cache_file, backup_file)
        print(f"已创建备份: {backup_file}")
    
    # 读取文件内容
    with open(auth_cache_file, "r") as f:
        content = f.read()
    
    # 检查并修复可能的异步问题
    if "await_only" in content:
        # 检查是否有greenlet_spawn相关的问题
        content = content.replace(
            "await self.redis.set(", 
            "await self._safe_redis_set("
        )
        
        # 添加安全的Redis操作方法
        if "_safe_redis_set" not in content:
            # 查找类定义结束的位置
            import re
            class_def_match = re.search(r"class AuthCache\(.*\):", content)
            
            if class_def_match:
                safe_method = """
    async def _safe_redis_set(self, key, value, ex=None):
        \"\"\"安全的Redis set操作，处理异步上下文问题\"\"\"
        try:
            return await self.redis.set(key, value, ex=ex)
        except Exception as e:
            import logging
            logging.getLogger(__name__).error(f"Redis set操作失败: {str(e)}")
            return False
"""
                insertion_point = class_def_match.end()
                content = content[:insertion_point] + safe_method + content[insertion_point:]
                print("添加了安全的Redis操作方法")
    
    # 保存修改
    with open(auth_cache_file, "w") as f:
        f.write(content)
    
    print(f"已更新认证缓存文件: {auth_cache_file}")
    return True

def fix_api_auth():
    """修复API认证中间件中的问题"""
    print("\n===== 修复API认证中间件 =====")
    
    api_auth_file = "utils/api_auth.py"
    
    if not os.path.exists(api_auth_file):
        print(f"找不到API认证文件: {api_auth_file}")
        return False
    
    # 创建备份
    backup_file = f"{api_auth_file}.bak"
    if not os.path.exists(backup_file):
        import shutil
        shutil.copy2(api_auth_file, backup_file)
        print(f"已创建备份: {backup_file}")
    
    # 读取文件内容
    with open(api_auth_file, "r") as f:
        content = f.read()
    
    # 检查并修复'int' object has no attribute 'unlink'和'expires'错误
    # 这通常发生在误将整数作为对象处理的情况
    modified = False
    
    # 修复unlink错误
    if "client.unlink" in content:
        content = content.replace(
            "client.unlink(", 
            "self._safe_unlink(client, "
        )
        
        # 添加安全的unlink方法
        if "_safe_unlink" not in content:
            # 查找类定义结束的位置
            import re
            class_def_match = re.search(r"class APIClientAuth\(.*\):", content)
            
            if class_def_match:
                safe_method = """
    def _safe_unlink(self, client, key):
        \"\"\"安全的unlink操作，处理类型错误\"\"\"
        try:
            if hasattr(client, 'unlink') and callable(client.unlink):
                return client.unlink(key)
            elif hasattr(client, 'delete') and callable(client.delete):
                return client.delete(key)
        except Exception as e:
            import logging
            logging.getLogger(__name__).error(f"Redis unlink操作失败: {str(e)}")
            return False
"""
                insertion_point = class_def_match.end()
                content = content[:insertion_point] + safe_method + content[insertion_point:]
                modified = True
    
    # 修复expires错误
    if ".expires" in content:
        # 添加检查属性存在的条件
        content = content.replace(
            "if client.expires(", 
            "if hasattr(client, 'expires') and callable(client.expires) and client.expires("
        )
        modified = True
    
    # 保存修改
    if modified:
        with open(api_auth_file, "w") as f:
            f.write(content)
        print(f"已更新API认证文件: {api_auth_file}")
    else:
        print("API认证文件未发现需要修复的问题")
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Redis连接和缓存问题修复工具")
    parser.add_argument("--check", action="store_true", help="仅检查不修复")
    parser.add_argument("--fix-all", action="store_true", help="修复所有问题")
    parser.add_argument("--fix-redis", action="store_true", help="修复Redis服务")
    parser.add_argument("--fix-cache", action="store_true", help="修复认证缓存")
    parser.add_argument("--fix-auth", action="store_true", help="修复API认证")
    
    args = parser.parse_args()
    fix_mode = args.fix_all or args.fix_redis or args.fix_cache or args.fix_auth
    
    print("===== Redis连接和缓存问题修复工具 =====")
    
    # 检查Redis模块
    check_redis_module()
    
    if not fix_mode and not args.check:
        print("\n请指定要执行的操作 (--check, --fix-all, --fix-redis, --fix-cache, --fix-auth)")
        return
    
    if args.check:
        print("\n仅执行检查，不进行修复")
        return
    
    # 执行修复
    if args.fix_all or args.fix_redis:
        fix_redis_service()
    
    if args.fix_all or args.fix_cache:
        fix_auth_cache()
    
    if args.fix_all or args.fix_auth:
        fix_api_auth()
    
    print("\n===== 修复完成 =====")
    print("请重启服务器以应用修复。")

if __name__ == "__main__":
    main() 
#!/usr/bin/env python
"""
检查数据库表结构的工具
"""
import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
project_root = current_dir.parent
sys.path.append(str(project_root))

from sqlalchemy import inspect, text
from db.session import AsyncSessionLocal

async def check_database_tables():
    """检查数据库中的表及其结构"""
    print("正在连接数据库...")
    
    async with AsyncSessionLocal() as db:
        conn = await db.connection()
        
        # 使用run_sync在同步上下文中执行inspect操作
        def get_tables(connection):
            inspector = inspect(connection)
            return inspector.get_table_names()
        
        # 获取所有表名
        tables = await conn.run_sync(get_tables)
        print(f"数据库中的表 ({len(tables)}):")
        for table in sorted(tables):
            print(f"  - {table}")
        
        # 检查审计日志表是否存在
        if 'audit_logs' in tables:
            print("\n审计日志表 (audit_logs) 存在!")
            
            # 检查列信息
            result = await db.execute(
                text("SELECT column_name, data_type FROM information_schema.columns "
                     "WHERE table_name = 'audit_logs' ORDER BY ordinal_position")
            )
            
            # 输出列信息
            print("\n审计日志表的列:")
            columns = result.all()
            for row in columns:
                column_name, data_type = row
                print(f"  - {column_name} ({data_type})")
            
            # 检查索引信息
            result = await db.execute(
                text("SELECT indexname, indexdef FROM pg_indexes "
                     "WHERE tablename = 'audit_logs'")
            )
            
            # 输出索引信息
            print("\n审计日志表的索引:")
            indexes = result.all()
            for row in indexes:
                index_name, index_def = row
                print(f"  - {index_name}")
                print(f"    {index_def}")
            
            # 检查外键信息
            result = await db.execute(
                text("SELECT tc.constraint_name, tc.table_name, kcu.column_name, "
                     "ccu.table_name AS foreign_table_name, ccu.column_name AS foreign_column_name "
                     "FROM information_schema.table_constraints AS tc "
                     "JOIN information_schema.key_column_usage AS kcu ON tc.constraint_name = kcu.constraint_name "
                     "JOIN information_schema.constraint_column_usage AS ccu ON ccu.constraint_name = tc.constraint_name "
                     "WHERE constraint_type = 'FOREIGN KEY' AND tc.table_name='audit_logs'")
            )
            
            # 输出外键信息
            print("\n审计日志表的外键关系:")
            foreign_keys = result.all()
            if foreign_keys:
                for row in foreign_keys:
                    constraint_name, table_name, column_name, foreign_table_name, foreign_column_name = row
                    print(f"  - {constraint_name}: {table_name}.{column_name} -> {foreign_table_name}.{foreign_column_name}")
            else:
                print("  (没有外键关系)")
        else:
            print("\n审计日志表 (audit_logs) 不存在!")

if __name__ == "__main__":
    asyncio.run(check_database_tables()) 
#!/usr/bin/env python
"""
简单创建用户脚本 - 使用原始SQL
"""
import asyncio
import os
import sys
from typing import List, Optional

# 添加当前目录到Python路径，确保可以导入项目模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.session import AsyncSessionLocal
from core.security import get_password_hash
from sqlalchemy.sql import text

async def create_test_user():
    """创建测试用户"""
    async with AsyncSessionLocal() as db:
        # 检查用户是否已存在
        query = text("SELECT * FROM users WHERE username = :username")
        result = await db.execute(query, {"username": "yulong"})
        user = result.first()
        
        if user:
            print(f"用户 yulong 已存在，ID: {user.id}")
            return user.id
        
        # 创建用户
        hashed_password = get_password_hash("a123456789")
        
        insert_query = text("""
            INSERT INTO users (
                username, 
                email, 
                hashed_password, 
                is_active, 
                is_superuser, 
                is_verified
            ) VALUES (
                :username,
                :email,
                :hashed_password,
                :is_active,
                :is_superuser,
                :is_verified
            ) RETURNING id
        """)
        
        result = await db.execute(
            insert_query, 
            {
                "username": "yulong",
                "email": "<EMAIL>",
                "hashed_password": hashed_password,
                "is_active": True,
                "is_superuser": False,
                "is_verified": True
            }
        )
        user_id = result.scalar_one()
        
        # 查询user角色ID
        role_query = text("SELECT id FROM roles WHERE name = :role_name")
        role_result = await db.execute(role_query, {"role_name": "user"})
        role_row = role_result.first()
        
        if role_row:
            role_id = role_row[0]  # 获取第一列的值
            
            # 添加用户-角色关联
            role_user_query = text("""
                INSERT INTO user_role (user_id, role_id)
                VALUES (:user_id, :role_id)
            """)
            
            await db.execute(
                role_user_query, 
                {"user_id": user_id, "role_id": role_id}
            )
            
            print(f"为用户 yulong 添加角色 user")
        else:
            print(f"角色 user 不存在")
        
        await db.commit()
        
        print(f"用户 yulong 创建成功，ID: {user_id}")
        return user_id

async def main():
    """主函数"""
    print("开始创建测试用户...")
    
    user_id = await create_test_user()
    
    if user_id:
        print(
            "\n测试用户创建完成! 可以使用以下凭据登录:\n"
            "用户名: yulong\n"
            "密码: a123456789\n"
        )
    
    print("脚本执行完成!")

if __name__ == "__main__":
    asyncio.run(main()) 
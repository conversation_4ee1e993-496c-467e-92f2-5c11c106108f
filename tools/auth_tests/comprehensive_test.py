#!/usr/bin/env python
"""
API综合测试脚本
用于测试所有接口，包括认证、权限和菜单接口
"""

import sys
import os
import json
import asyncio
import argparse
import httpx
from datetime import datetime
from typing import Dict, List, Optional, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
sys.path.append(project_root)

from core.config import settings

class APITester:
    """API测试工具类"""
    
    def __init__(self, base_url="http://localhost:8000"):
        """初始化测试工具"""
        self.base_url = base_url
        self.admin_token = None
        self.user_token = None
        self.test_user_id = None
        self.results = {
            "passed": 0,
            "failed": 0,
            "total": 0,
            "tests": []
        }
    
    def add_result(self, name: str, passed: bool, response=None, error=None):
        """添加测试结果"""
        result = {
            "name": name,
            "passed": passed,
            "timestamp": datetime.now().isoformat()
        }
        
        if response:
            try:
                result["status_code"] = response.status_code
                result["response"] = response.json() if response.headers.get("content-type") == "application/json" else str(response.text)[:200]
            except:
                result["response"] = str(response.text)[:200]
        
        if error:
            result["error"] = str(error)
        
        self.results["tests"].append(result)
        self.results["total"] += 1
        
        if passed:
            self.results["passed"] += 1
            print(f"✅ {name}")
        else:
            self.results["failed"] += 1
            print(f"❌ {name}: {error if error else f'状态码 {response.status_code}'}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("\n===== 开始API综合测试 =====")
        
        # 测试基本功能
        await self.test_healthcheck()
        
        # 测试标准JWT登录
        await self.test_jwt_login()
        
        # 测试API权限
        if self.admin_token:
            await self.test_admin_apis()
        
        if self.user_token:
            await self.test_user_apis()
        
        # 测试完成，打印结果
        print("\n===== 测试结果汇总 =====")
        print(f"总测试数: {self.results['total']}")
        print(f"通过: {self.results['passed']}")
        print(f"失败: {self.results['failed']}")
        print(f"通过率: {self.results['passed'] / self.results['total'] * 100:.2f}%")
        
        # 保存测试报告
        report_path = os.path.join(project_root, "test_report.json")
        with open(report_path, "w", encoding="utf-8") as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"测试报告已保存到: {report_path}")
    
    async def test_healthcheck(self):
        """测试健康检查接口"""
        print("\n----- 测试健康检查接口 -----")
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                response = await client.get(f"{self.base_url}/")
                passed = response.status_code == 200
                self.add_result("健康检查接口", passed, response)
            except Exception as e:
                self.add_result("健康检查接口", False, error=str(e))
    
    async def test_jwt_login(self):
        """测试JWT登录"""
        print("\n----- 测试JWT登录 -----")
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            # 管理员登录
            print("1. 测试管理员登录")
            admin_login_data = {
                "username": "admin",
                "password": "admin123",
            }
            
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/auth/jwt/login",
                    data=admin_login_data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                
                passed = response.status_code == 200
                self.add_result("管理员JWT登录", passed, response)
                
                if passed:
                    data = response.json()
                    self.admin_token = data.get("access_token")
                    print(f"管理员令牌: {self.admin_token[:10]}...{self.admin_token[-10:] if self.admin_token else ''}")
            except Exception as e:
                self.add_result("管理员JWT登录", False, error=str(e))
            
            # 普通用户登录
            print("\n2. 测试普通用户登录")
            user_login_data = {
                "username": "yulong",
                "password": "a123456789",
            }
            
            try:
                response = await client.post(
                    f"{self.base_url}/api/v1/auth/jwt/login",
                    data=user_login_data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                
                passed = response.status_code == 200
                self.add_result("普通用户JWT登录", passed, response)
                
                if passed:
                    data = response.json()
                    self.user_token = data.get("access_token")
                    print(f"普通用户令牌: {self.user_token[:10]}...{self.user_token[-10:] if self.user_token else ''}")
            except Exception as e:
                self.add_result("普通用户JWT登录", False, error=str(e))
    
    async def test_admin_apis(self):
        """测试管理员API访问"""
        if not self.admin_token:
            print("\n❌ 没有管理员令牌，跳过管理员API测试")
            return
        
        print("\n----- 测试管理员API访问 -----")
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": f"Bearer {self.admin_token}",
                "Content-Type": "application/json"
            }
            
            # 测试获取当前用户信息
            print("1. 测试获取当前用户信息")
            try:
                response = await client.get(f"{self.base_url}/api/v1/users/me", headers=headers)
                passed = response.status_code == 200
                self.add_result("管理员获取个人信息", passed, response)
                
                if passed:
                    data = response.json()
                    user_id = data.get("data", {}).get("id")
                    if user_id == 1:
                        print(f"用户ID: {user_id} (admin)")
                    else:
                        print(f"用户ID: {user_id}")
            except Exception as e:
                self.add_result("管理员获取个人信息", False, error=str(e))
            
            # 测试获取用户列表
            print("\n2. 测试获取用户列表")
            try:
                response = await client.get(f"{self.base_url}/api/v1/users", headers=headers)
                passed = response.status_code == 200
                self.add_result("管理员获取用户列表", passed, response)
                
                if passed:
                    data = response.json()
                    users = data.get("data", [])
                    user_count = len(users)
                    print(f"用户数量: {user_count}")
                    
                    # 记录普通用户ID，用于后续测试
                    for user in users:
                        if user.get("username") == "yulong":
                            self.test_user_id = user.get("id")
                            break
            except Exception as e:
                self.add_result("管理员获取用户列表", False, error=str(e))
            
            # 测试获取角色列表
            print("\n3. 测试获取角色列表")
            try:
                response = await client.get(f"{self.base_url}/api/v1/roles", headers=headers)
                passed = response.status_code == 200
                self.add_result("管理员获取角色列表", passed, response)
                
                if passed:
                    data = response.json()
                    roles = data.get("data", [])
                    role_count = len(roles)
                    print(f"角色数量: {role_count}")
            except Exception as e:
                self.add_result("管理员获取角色列表", False, error=str(e))
            
            # 测试获取权限列表
            print("\n4. 测试获取权限列表")
            try:
                response = await client.get(f"{self.base_url}/api/v1/permissions", headers=headers)
                passed = response.status_code == 200
                self.add_result("管理员获取权限列表", passed, response)
                
                if passed:
                    data = response.json()
                    permissions = data.get("data", [])
                    perm_count = len(permissions)
                    print(f"权限数量: {perm_count}")
            except Exception as e:
                self.add_result("管理员获取权限列表", False, error=str(e))
            
            # 测试获取菜单树
            print("\n5. 测试获取菜单树")
            try:
                response = await client.get(f"{self.base_url}/api/v1/menus/tree", headers=headers)
                passed = response.status_code == 200
                self.add_result("管理员获取菜单树", passed, response)
                
                if passed:
                    data = response.json()
                    menus = data.get("data", [])
                    menu_count = len(menus)
                    print(f"根菜单数量: {menu_count}")
            except Exception as e:
                self.add_result("管理员获取菜单树", False, error=str(e))
    
    async def test_user_apis(self):
        """测试普通用户API访问"""
        if not self.user_token:
            print("\n❌ 没有普通用户令牌，跳过普通用户API测试")
            return
        
        print("\n----- 测试普通用户API访问 -----")
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            headers = {
                "Authorization": f"Bearer {self.user_token}",
                "Content-Type": "application/json"
            }
            
            # 测试获取当前用户信息
            print("1. 测试获取当前用户信息")
            try:
                response = await client.get(f"{self.base_url}/api/v1/users/me", headers=headers)
                passed = response.status_code == 200
                self.add_result("普通用户获取个人信息", passed, response)
                
                if passed:
                    data = response.json()
                    user_id = data.get("data", {}).get("id")
                    print(f"用户ID: {user_id} (yulong)")
            except Exception as e:
                self.add_result("普通用户获取个人信息", False, error=str(e))
            
            # 测试获取用户列表 (应该有权限)
            print("\n2. 测试获取用户列表")
            try:
                response = await client.get(f"{self.base_url}/api/v1/users", headers=headers)
                passed = response.status_code == 200
                self.add_result("普通用户获取用户列表", passed, response)
                
                if passed:
                    data = response.json()
                    users = data.get("data", [])
                    user_count = len(users)
                    print(f"用户数量: {user_count}")
            except Exception as e:
                self.add_result("普通用户获取用户列表", False, error=str(e))
            
            # 测试获取菜单树 (应该有权限)
            print("\n3. 测试获取菜单树")
            try:
                response = await client.get(f"{self.base_url}/api/v1/menus/tree", headers=headers)
                passed = response.status_code == 200
                self.add_result("普通用户获取菜单树", passed, response)
                
                if passed:
                    data = response.json()
                    menus = data.get("data", [])
                    menu_count = len(menus)
                    print(f"根菜单数量: {menu_count}")
            except Exception as e:
                self.add_result("普通用户获取菜单树", False, error=str(e))
            
            # 测试创建菜单 (应该没有权限)
            print("\n4. 测试创建菜单 (应该没有权限)")
            try:
                test_menu = {
                    "name": "test_menu",
                    "path": "/test",
                    "title": "测试菜单",
                    "component": "Test",
                    "sort_order": 100
                }
                
                response = await client.post(
                    f"{self.base_url}/api/v1/menus",
                    headers=headers,
                    json=test_menu
                )
                
                # 这里应该失败（返回403）
                passed = response.status_code == 403
                self.add_result("普通用户创建菜单(应该失败)", passed, response)
                
                if passed:
                    print("测试通过: 普通用户无法创建菜单")
                else:
                    print(f"测试失败: 普通用户不应该能创建菜单，状态码: {response.status_code}")
            except Exception as e:
                self.add_result("普通用户创建菜单(应该失败)", False, error=str(e))
            
            # 测试获取当前用户权限
            print("\n5. 测试获取当前用户权限")
            try:
                response = await client.get(f"{self.base_url}/api/v1/permissions/user/me", headers=headers)
                passed = response.status_code == 200
                self.add_result("普通用户获取自己的权限", passed, response)
                
                if passed:
                    data = response.json()
                    permissions = data.get("data", [])
                    print(f"权限数量: {len(permissions)}")
                    print(f"权限列表: {', '.join(permissions[:5])}..." if len(permissions) > 5 else f"权限列表: {', '.join(permissions)}")
            except Exception as e:
                self.add_result("普通用户获取自己的权限", False, error=str(e))

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="API综合测试工具")
    parser.add_argument("--url", default="http://localhost:8000", help="API服务器地址")
    parser.add_argument("--test-server", action="store_true", help="是否使用测试服务器(8088端口)")
    args = parser.parse_args()
    
    base_url = args.url
    if args.test_server:
        base_url = "http://localhost:8088"
    
    print(f"测试目标: {base_url}")
    
    tester = APITester(base_url=base_url)
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main()) 
# 认证测试工具

本目录包含与用户认证和授权相关的测试和修复工具。这些工具用于诊断、测试和修复JWT认证相关问题。

## 目录内容

1. **JWT令牌测试工具**
   - `check_token.py` - 检查JWT令牌是否与当前环境密钥匹配
   - `test_jwt_auth.py` - 分析JWT认证流程
   - `verify_password.py` - 密码验证测试工具

2. **认证服务测试**
   - `fix_login.py` - JWT认证测试服务器，用于诊断令牌问题
   - `fix_auth_endpoint.py` - 认证端点修复工具
   - `auth_test_comprehensive.py` - 全面的认证测试套件

3. **用户管理测试**
   - `create_user.py` - 创建用户的脚本
   - `create_user_simple.py` - 创建用户的简化脚本
   - `login.py` - 登录测试工具
   - `test_login.py` - 登录功能测试

4. **文档**
   - `LOGIN_FIX.md` - 登录问题修复方案简要说明
   - `README-LOGIN-FIX.md` - 详细的登录问题修复文档
   - `SWAGGER-AUTH-FIX.md` - Swagger UI认证修复方案

## 常用命令

1. 检查JWT令牌是否有效：
   ```bash
   python tools/auth_tests/check_token.py <您的令牌>
   ```

2. 启动JWT认证测试服务器：
   ```bash
   python tools/auth_tests/fix_login.py
   ```

3. 测试JWT认证流程：
   ```bash
   python tools/auth_tests/test_jwt_auth.py
   ```

4. 创建测试用户：
   ```bash
   python tools/auth_tests/create_user.py
   ```

## 常见问题

1. **Swagger UI认证问题**：如果在Swagger UI中设置JWT令牌后仍然返回401错误，请参考`SWAGGER-AUTH-FIX.md`文档。

2. **无法使用用户名登录**：查看`README-LOGIN-FIX.md`文档了解如何启用用户名登录。

3. **令牌验证失败**：使用`check_token.py`工具检查令牌是否与当前环境的密钥匹配。可能存在不同环境使用不同JWT密钥的问题。

## 注意事项

- 所有测试工具应该只在开发或测试环境中使用，不要在生产环境中使用。
- 一些测试脚本会创建测试用户或修改数据库，使用前请确保已备份数据。
- 修改后的依赖项可能需要重新启动应用才能生效。 
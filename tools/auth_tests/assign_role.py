#!/usr/bin/env python
"""
为用户分配角色的脚本
"""
import sys
import os
import psycopg2
import psycopg2.extras
from dotenv import load_dotenv

# 加载环境变量
dotenv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), '.env')
load_dotenv(dotenv_path)

# 数据库配置
DB_HOST = os.getenv("POSTGRES_SERVER")
DB_PORT = os.getenv("POSTGRES_PORT")
DB_NAME = os.getenv("POSTGRES_DB")
DB_USER = os.getenv("POSTGRES_USER")
DB_PASS = os.getenv("POSTGRES_PASSWORD")

def get_connection():
    """获取数据库连接"""
    return psycopg2.connect(
        host=DB_HOST,
        port=DB_PORT,
        dbname=DB_NAME,
        user=DB_USER,
        password=DB_PASS
    )

def assign_role(user_id, role_name):
    """为用户分配角色"""
    try:
        conn = get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # 查询用户
        cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))
        user = cursor.fetchone()
        
        if not user:
            print(f"错误: 找不到ID为 {user_id} 的用户")
            return False
            
        print(f"用户: {user['username']} (ID: {user['id']})")
        
        # 查询角色
        cursor.execute("SELECT * FROM roles WHERE name = %s", (role_name,))
        role = cursor.fetchone()
        
        if not role:
            print(f"错误: 找不到名为 {role_name} 的角色")
            return False
            
        print(f"角色: {role['name']} (ID: {role['id']})")
        
        # 检查用户是否已有该角色
        cursor.execute(
            "SELECT * FROM user_role WHERE user_id = %s AND role_id = %s", 
            (user_id, role['id'])
        )
        existing = cursor.fetchone()
        
        if existing:
            print(f"用户已经拥有角色 {role_name}")
            return True
        
        # 分配角色
        cursor.execute(
            "INSERT INTO user_role (user_id, role_id) VALUES (%s, %s)",
            (user_id, role['id'])
        )
        conn.commit()
        
        print(f"已成功为用户 {user['username']} 分配角色 {role_name}")
        return True
        
    except Exception as e:
        print(f"分配角色出错: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    if len(sys.argv) != 3:
        print("用法: python assign_role.py <用户ID> <角色名>")
        return
        
    try:
        user_id = int(sys.argv[1])
        role_name = sys.argv[2]
        
        print(f"===== 为用户ID={user_id}分配角色 {role_name} =====")
        success = assign_role(user_id, role_name)
        
        if success:
            print("===== 分配完成 =====")
        else:
            print("===== 分配失败 =====")
            
    except ValueError:
        print("错误: 用户ID必须是整数")

if __name__ == "__main__":
    main() 
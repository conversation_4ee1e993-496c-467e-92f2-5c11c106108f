#!/usr/bin/env python
"""
修复认证端点 - 添加支持用户名登录的自定义端点
"""
import asyncio
import os
import sys
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

# 添加当前目录到Python路径，确保可以导入项目模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.config import settings

# 自定义登录模型
class AuthEndpointCode(BaseModel):
    """认证端点实现"""
    
    # 定义模型
    class UserLoginRequest(BaseModel):
        """用户登录请求"""
        username: str = Field(..., description="用户名或邮箱")
        password: str = Field(..., description="密码")
    
    class LoginResponse(BaseModel):
        """登录响应"""
        access_token: str
        token_type: str = "bearer"
    
    class UserResponse(BaseModel):
        """用户响应"""
        id: int
        email: str
        username: str
        is_active: bool
        is_verified: bool
        is_superuser: bool
    
    # 自定义登录端点
    login_endpoint_code = """
from fastapi import APIRouter, Depends, HTTPException, status, Body
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, Field
from typing import Optional

from db.session import get_db
from models.user import User
from core.security import verify_password, create_access_token
from schemas.response import ResponseModel

router = APIRouter()

class UserLoginRequest(BaseModel):
    """用户登录请求"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")

class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str
    token_type: str = "bearer"

@router.post("/login", response_model=LoginResponse)
async def login(
    login_data: UserLoginRequest = Body(...),
    db: AsyncSession = Depends(get_db)
):
    """
    自定义登录端点，支持用户名或邮箱登录
    
    Args:
        login_data: 登录数据
        db: 数据库会话
        
    Returns:
        带有访问令牌的响应
        
    Raises:
        HTTPException: 登录凭据无效或用户未激活
    """
    # 尝试通过用户名或邮箱查找用户
    user = None
    
    # 1. 先尝试通过用户名查找
    stmt = select(User).where(User.username == login_data.username)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    # 2. 如果未找到，尝试通过邮箱查找
    if user is None:
        stmt = select(User).where(User.email == login_data.username)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
    
    # 3. 如果仍未找到，则凭据无效
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="LOGIN_BAD_CREDENTIALS",
        )
    
    # 4. 验证用户是否激活
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="LOGIN_BAD_CREDENTIALS",
        )
    
    # 5. 验证密码
    if not verify_password(login_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="LOGIN_BAD_CREDENTIALS",
        )
    
    # 6. 创建访问令牌
    access_token = create_access_token(data=str(user.id))
    
    # 7. 返回令牌
    return {
        "access_token": access_token,
        "token_type": "bearer"
    }
"""

# 自定义认证模块实现
def generate_custom_auth_file() -> str:
    """生成自定义认证模块代码"""
    
    auth_module_code = """
"""
自定义认证模块 - 支持用户名和邮箱登录
"""
from fastapi import APIRouter, Depends, HTTPException, status, Body, Request, Response
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime

from db.session import get_db
from models.user import User
from core.security import verify_password, create_access_token, create_refresh_token
from auth.exceptions import AuthError, InvalidCredentialsError
from core.users import get_user_manager
from utils.auth_cache import AuthCache
from services.redis_service import RedisService

router = APIRouter()

class UserLoginRequest(BaseModel):
    """用户登录请求"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")

class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"

class TokenRefreshRequest(BaseModel):
    """令牌刷新请求"""
    refresh_token: str

@router.post("/flexible-login", response_model=LoginResponse, tags=["认证"])
async def flexible_login(
    login_data: UserLoginRequest,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    灵活登录端点，支持用户名或邮箱登录
    
    Args:
        login_data: 登录数据
        request: 请求对象
        db: 数据库会话
        
    Returns:
        带有访问令牌的响应
        
    Raises:
        HTTPException: 登录凭据无效或用户未激活
    """
    try:
        # 尝试通过用户名或邮箱查找用户
        user = None
        
        # 1. 先尝试通过用户名查找
        stmt = select(User).where(User.username == login_data.username)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        # 2. 如果未找到，尝试通过邮箱查找
        if user is None:
            stmt = select(User).where(User.email == login_data.username)
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
        
        # 3. 如果仍未找到，则凭据无效
        if user is None:
            raise InvalidCredentialsError()
        
        # 4. 验证用户是否激活
        if not user.is_active:
            raise InvalidCredentialsError()
        
        # 5. 验证密码
        if not verify_password(login_data.password, user.hashed_password):
            raise InvalidCredentialsError()
        
        # 6. 创建访问令牌和刷新令牌
        access_token = create_access_token(data=str(user.id))
        refresh_token = create_refresh_token(data=str(user.id))
        
        # 7. 获取用户管理器，以便调用登录后回调
        async for user_manager in get_user_manager():
            # 执行登录后的回调
            if hasattr(user_manager, 'on_after_login'):
                await user_manager.on_after_login(user, request)
            break
        
        # 8. 更新最后登录时间
        user.last_login = datetime.now(timezone.utc)
        db.add(user)
        await db.commit()
        
        # 9. 缓存令牌数据
        try:
            redis_cache = await RedisService.get_cache()
            auth_cache = AuthCache(redis_cache)
            
            # 缓存令牌数据
            token_data = {"sub": str(user.id)}
            await auth_cache.set_token_data(access_token, token_data)
        except Exception as e:
            # 记录异常，但不中断登录流程
            print(f"缓存令牌数据失败: {str(e)}")
        
        # 10. 返回令牌
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }
    except AuthError as e:
        raise HTTPException(
            status_code=e.status_code,
            detail=e.detail,
        )
    except Exception as e:
        # 记录异常
        print(f"登录过程出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"服务器内部错误: {str(e)}",
        )

# FastAPI-Users官方JWT登录端点的代理端点
@router.post("/jwt/login", tags=["认证"])
async def jwt_login_proxy(
    credentials: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db),
    request: Request = None,
):
    """
    JWT登录代理端点，自动尝试用户名和邮箱登录
    
    Args:
        credentials: OAuth2表单认证凭据
        db: 数据库会话
        
    Returns:
        登录结果
    """
    # 创建登录请求
    login_data = UserLoginRequest(
        username=credentials.username,
        password=credentials.password
    )
    
    # 调用自定义登录端点
    return await flexible_login(login_data, request, db)
"""
    
    return auth_module_code

# 创建auth异常模块
def generate_auth_exceptions_file() -> str:
    """生成认证异常模块代码"""
    
    exceptions_code = '''
"""
认证相关异常
"""
from fastapi import status

class AuthError(Exception):
    """认证相关异常基类"""
    def __init__(self, detail: str, status_code: int = status.HTTP_400_BAD_REQUEST):
        self.detail = detail
        self.status_code = status_code
        super().__init__(detail)

class InvalidCredentialsError(AuthError):
    """无效的认证凭据"""
    def __init__(self, detail: str = "LOGIN_BAD_CREDENTIALS"):
        super().__init__(detail, status_code=status.HTTP_400_BAD_REQUEST)

class UserNotFoundError(AuthError):
    """用户不存在"""
    def __init__(self, detail: str = "用户不存在"):
        super().__init__(detail, status_code=status.HTTP_404_NOT_FOUND)

class TokenError(AuthError):
    """令牌错误"""
    def __init__(self, detail: str = "无效的令牌"):
        super().__init__(detail, status_code=status.HTTP_401_UNAUTHORIZED)
'''
    
    return exceptions_code

# 更新API路由模块方法
def update_api_v1_api_file() -> str:
    """生成更新API路由的代码"""
    
    api_update_code = """
from fastapi import APIRouter

from api.api_v1.endpoints import users, auth, roles, permissions, menus, configs, custom_auth
from core.users import fastapi_users, jwt_backend, cookie_backend
from schemas.user import User, UserCreate, UserUpdate

api_router = APIRouter()

# 路由分组 - 使用中文标签名
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(roles.router, prefix="/roles", tags=["角色管理"])
api_router.include_router(permissions.router, prefix="/permissions", tags=["权限管理"])
api_router.include_router(menus.router, prefix="/menus", tags=["菜单管理"])
api_router.include_router(configs.router, prefix="/configs", tags=["系统配置"])

# 添加自定义认证路由
api_router.include_router(custom_auth.router, prefix="/auth", tags=["认证"])

# FastAPI-Users路由 - 统一使用"认证"标签
# 注意: 我们已经添加了自定义认证路由代替了jwt_backend的登录端点
api_router.include_router(
    fastapi_users.get_auth_router(cookie_backend), 
    prefix="/auth/cookie", 
    tags=["认证"]
)
api_router.include_router(
    fastapi_users.get_register_router(User, UserCreate),
    prefix="/auth", 
    tags=["认证"]
)
api_router.include_router(
    fastapi_users.get_reset_password_router(),
    prefix="/auth", 
    tags=["认证"]
)
api_router.include_router(
    fastapi_users.get_verify_router(User),
    prefix="/auth", 
    tags=["认证"]
)
api_router.include_router(
    fastapi_users.get_users_router(User, UserUpdate),
    prefix="/fastapi_users",
    tags=["用户管理"]
)
"""
    
    return api_update_code

async def fix_auth_endpoints():
    """修复认证端点"""
    print("===== 开始修复认证端点 =====")
    
    # 1. 创建auth目录和exceptions.py文件
    auth_dir = "auth"
    if not os.path.exists(auth_dir):
        os.makedirs(auth_dir)
        print(f"创建目录: {auth_dir}")
    
    # 2. 创建auth异常模块
    auth_exceptions_path = os.path.join(auth_dir, "exceptions.py")
    with open(auth_exceptions_path, "w") as f:
        f.write(generate_auth_exceptions_file())
    print(f"创建文件: {auth_exceptions_path}")
    
    # 3. 创建自定义认证模块
    custom_auth_dir = os.path.join("api", "api_v1", "endpoints")
    custom_auth_path = os.path.join(custom_auth_dir, "custom_auth.py")
    with open(custom_auth_path, "w") as f:
        f.write(generate_custom_auth_file())
    print(f"创建文件: {custom_auth_path}")
    
    # 4. 更新API路由
    api_v1_api_path = os.path.join("api", "api_v1", "api.py")
    with open(api_v1_api_path, "w") as f:
        f.write(update_api_v1_api_file())
    print(f"更新文件: {api_v1_api_path}")
    
    print("\n===== 修复完成 =====")
    print("已添加自定义认证端点，支持用户名和邮箱登录")
    print("登录端点:")
    print(f"1. POST {settings.API_V1_STR}/auth/jwt/login - 支持用户名或邮箱")
    print(f"2. POST {settings.API_V1_STR}/auth/flexible-login - 自定义端点")
    print("\n请重启服务器以应用更改:")
    print("pkill -9 uvicorn && uvicorn main:app --reload")

if __name__ == "__main__":
    asyncio.run(fix_auth_endpoints()) 
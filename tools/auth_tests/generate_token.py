#!/usr/bin/env python
"""
为指定用户生成JWT令牌
"""
import os
import sys
import jwt
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
sys.path.append(project_root)

# 加载环境变量
load_dotenv()

from core.config import settings
from core.security import create_access_token

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python generate_token.py <用户ID> [过期时间(分钟)]")
        return
        
    try:
        user_id = sys.argv[1]
        
        # 解析过期时间（可选）
        expires_delta = None
        if len(sys.argv) > 2:
            expires_delta = timedelta(minutes=int(sys.argv[2]))
        
        # 创建令牌
        token = create_access_token(data=user_id, expires_delta=expires_delta)
        
        # 显示令牌信息
        print("\n===== JWT令牌生成成功 =====")
        print(f"用户ID: {user_id}")
        print(f"令牌: {token}")
        if expires_delta:
            print(f"过期时间: {expires_delta.total_seconds()/60}分钟")
        else:
            print(f"过期时间: {settings.ACCESS_TOKEN_EXPIRE_MINUTES}分钟 (默认)")
        
        # 解码令牌（不验证签名）展示信息
        payload = jwt.decode(token, options={"verify_signature": False})
        exp_time = datetime.fromtimestamp(payload['exp'])
        iat_time = datetime.fromtimestamp(payload['iat'])
        print(f"生成时间: {iat_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"过期时间: {exp_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 显示curl命令示例
        print("\n使用示例:")
        print("测试用户信息接口:")
        print(f"curl -X 'GET' 'http://127.0.0.1:8000/api/v1/users/me' -H 'accept: application/json' -H 'Authorization: Bearer {token}'")
        print("\n测试用户列表接口:")
        print(f"curl -X 'GET' 'http://127.0.0.1:8000/api/v1/users?page=1&page_size=10' -H 'accept: application/json' -H 'Authorization: Bearer {token}'")
        print("===========================\n")
        
    except Exception as e:
        print(f"生成令牌出错: {str(e)}")

if __name__ == "__main__":
    main() 
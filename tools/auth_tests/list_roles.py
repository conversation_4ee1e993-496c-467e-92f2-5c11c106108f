#!/usr/bin/env python
"""
列出所有角色的脚本
"""
import os
import psycopg2
import psycopg2.extras
from dotenv import load_dotenv

# 加载环境变量
dotenv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), '.env')
load_dotenv(dotenv_path)

# 数据库配置
DB_HOST = os.getenv("POSTGRES_SERVER")
DB_PORT = os.getenv("POSTGRES_PORT")
DB_NAME = os.getenv("POSTGRES_DB")
DB_USER = os.getenv("POSTGRES_USER")
DB_PASS = os.getenv("POSTGRES_PASSWORD")

def get_connection():
    """获取数据库连接"""
    return psycopg2.connect(
        host=DB_HOST,
        port=DB_PORT,
        dbname=DB_NAME,
        user=DB_USER,
        password=DB_PASS
    )

def list_roles():
    """列出所有角色"""
    try:
        conn = get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # 查询所有角色
        cursor.execute("SELECT * FROM roles ORDER BY id")
        roles = cursor.fetchall()
        
        if not roles:
            print("没有找到任何角色")
            return
            
        print("系统角色列表:")
        for role in roles:
            print(f"  - {role['name']} (ID: {role['id']}, 描述: {role['description']})")
            
            # 查询角色权限
            cursor.execute("""
                SELECT p.* FROM permissions p
                JOIN role_permission rp ON p.id = rp.permission_id
                WHERE rp.role_id = %s
                ORDER BY p.name
            """, (role['id'],))
            
            permissions = cursor.fetchall()
            
            if permissions:
                print(f"    权限:")
                for perm in permissions:
                    print(f"      - {perm['name']}")
            else:
                print(f"    (无权限)")
                
            print("")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("===== 系统角色列表 =====")
    list_roles()
    print("===== 列表完成 =====")

if __name__ == "__main__":
    main() 
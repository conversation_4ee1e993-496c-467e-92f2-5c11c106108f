#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合认证测试工具
用于测试OAuth、CAS和SMS三种认证方式
"""
import os
import sys
import json
import asyncio
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
sys.path.append(project_root)

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import FastAPI, Depends, Query, Request, BackgroundTasks
from fastapi.responses import JSONResponse
import uvicorn
from typing import Optional, Dict, Any, List

# 导入项目依赖
from db.session import get_db
from core.config import settings
from schemas.response import ResponseModel
from utils.auth_config_manager import auth_config_manager

# 创建测试应用
app = FastAPI(title="认证系统测试工具")

# 获取可用的OAuth提供商
@app.get("/test/oauth/providers", tags=["OAuth测试"])
async def get_oauth_providers(
    db: AsyncSession = Depends(get_db)
):
    """获取所有可用的OAuth提供商"""
    try:
        from auth.backends.oauth import oauth_clients, get_oauth_providers_info
        
        # 确保刷新OAuth配置
        await auth_config_manager.refresh_all_configs(db)
        
        # 获取提供商信息
        providers = await get_oauth_providers_info()
        
        return ResponseModel(
            data=providers,
            msg="获取OAuth提供商成功"
        )
    except Exception as e:
        return ResponseModel(
            code="B0001",
            msg=f"获取OAuth提供商失败: {str(e)}",
            data=None
        )

# 创建OAuth授权URL
@app.get("/test/oauth/authorize", tags=["OAuth测试"])
async def create_oauth_authorize_url(
    provider: str,
    db: AsyncSession = Depends(get_db)
):
    """创建OAuth授权URL"""
    try:
        # 导入OAuth客户端
        from auth.backends.oauth import get_oauth_client
        
        # 获取指定提供商的客户端
        client = await get_oauth_client(provider, db)
        if not client:
            return ResponseModel(
                code="B0001",
                msg=f"未找到OAuth提供商: {provider}",
                data=None
            )
        
        # 构建回调URL
        redirect_uri = f"{settings.API_SERVER_URL}/api/v1/auth/oauth/callback/{provider}"
        
        # 获取授权URL
        authorization_url = await client.get_authorization_url(redirect_uri, state=provider)
        
        return ResponseModel(
            data={"authorization_url": authorization_url},
            msg="获取授权URL成功"
        )
    except Exception as e:
        return ResponseModel(
            code="B0001",
            msg=f"获取授权URL失败: {str(e)}",
            data=None
        )

# 验证OAuth令牌
@app.post("/test/oauth/validate", tags=["OAuth测试"])
async def validate_oauth_token(
    provider: str,
    access_token: str,
    db: AsyncSession = Depends(get_db)
):
    """验证OAuth访问令牌并获取用户信息"""
    try:
        # 导入OAuth令牌验证
        from auth.backends.oauth import validate_oauth_token
        
        # 验证令牌
        user_info = await validate_oauth_token(provider, access_token, db)
        if not user_info:
            return ResponseModel(
                code="B0001",
                msg="验证OAuth令牌失败",
                data=None
            )
        
        return ResponseModel(
            data=user_info,
            msg="验证OAuth令牌成功"
        )
    except Exception as e:
        return ResponseModel(
            code="B0001",
            msg=f"验证OAuth令牌失败: {str(e)}",
            data=None
        )

# 获取CAS配置
@app.get("/test/cas/config", tags=["CAS测试"])
async def get_cas_config(
    db: AsyncSession = Depends(get_db)
):
    """获取当前CAS配置"""
    try:
        # 导入CAS配置
        from auth.backends.cas import cas_config, load_cas_config
        
        # 确保刷新CAS配置
        await load_cas_config(db)
        
        # 返回配置
        result = {
            "enabled": bool(cas_config.get("server_url")),
            "server_url": cas_config.get("server_url"),
            "service_url": cas_config.get("service_url"),
            "auto_create_user": cas_config.get("auto_create_user"),
            "default_role": cas_config.get("default_role"),
            "version": cas_config.get("version"),
            "validate_cert": cas_config.get("validate_cert"),
        }
        
        return ResponseModel(
            data=result,
            msg="获取CAS配置成功"
        )
    except Exception as e:
        return ResponseModel(
            code="B0001",
            msg=f"获取CAS配置失败: {str(e)}",
            data=None
        )

# 验证CAS票据
@app.post("/test/cas/validate", tags=["CAS测试"])
async def validate_cas_ticket(
    ticket: str,
    service: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """验证CAS票据"""
    try:
        # 导入CAS验证函数
        from auth.backends.cas import validate_cas_ticket
        
        # 验证票据
        username = await validate_cas_ticket(ticket, service, db)
        
        return ResponseModel(
            data={"username": username},
            msg="验证CAS票据成功"
        )
    except Exception as e:
        return ResponseModel(
            code="B0001",
            msg=f"验证CAS票据失败: {str(e)}",
            data=None
        )

# 获取SMS配置
@app.get("/test/sms/config", tags=["SMS测试"])
async def get_sms_config(
    db: AsyncSession = Depends(get_db)
):
    """获取当前SMS配置"""
    try:
        # 导入SMS配置
        from auth.backends.sms import sms_config, load_sms_config
        
        # 确保刷新SMS配置
        await load_sms_config(db)
        
        # 返回配置(去除敏感信息)
        result = {
            "enabled": bool(sms_config.get("provider")),
            "provider": sms_config.get("provider"),
            "sign_name": sms_config.get("sign_name"),
            "auto_create_user": sms_config.get("auto_create_user"),
            "code_expire_minutes": sms_config.get("code_expire_minutes"),
            "code_length": sms_config.get("code_length"),
            "cooldown_seconds": sms_config.get("cooldown_seconds"),
        }
        
        return ResponseModel(
            data=result,
            msg="获取SMS配置成功"
        )
    except Exception as e:
        return ResponseModel(
            code="B0001",
            msg=f"获取SMS配置失败: {str(e)}",
            data=None
        )

# 发送SMS验证码
@app.post("/test/sms/send", tags=["SMS测试"])
async def send_sms_code(
    phone: str,
    db: AsyncSession = Depends(get_db)
):
    """发送SMS验证码(仅测试环境)"""
    try:
        # 导入SMS发送函数
        from auth.backends.sms import send_sms_code, generate_random_code
        
        # 生成验证码
        code = await generate_random_code(6)
        
        # 发送验证码
        success = await send_sms_code(phone, code, db)
        
        if success:
            return ResponseModel(
                data={"code": code} if settings.DEBUG else None,
                msg="发送SMS验证码成功"
            )
        else:
            return ResponseModel(
                code="B0001",
                msg="发送SMS验证码失败",
                data=None
            )
    except Exception as e:
        return ResponseModel(
            code="B0001",
            msg=f"发送SMS验证码失败: {str(e)}",
            data=None
        )

# 验证SMS验证码
@app.post("/test/sms/validate", tags=["SMS测试"])
async def validate_sms_code(
    phone: str,
    code: str,
    db: AsyncSession = Depends(get_db)
):
    """验证SMS验证码"""
    try:
        # 导入SMS验证函数
        from auth.backends.sms import verify_sms_code
        
        # 验证验证码
        is_valid = await verify_sms_code(phone, code, db)
        
        if is_valid:
            return ResponseModel(
                data={"valid": True},
                msg="验证码有效"
            )
        else:
            return ResponseModel(
                data={"valid": False},
                msg="验证码无效或已过期"
            )
    except Exception as e:
        return ResponseModel(
            code="B0001",
            msg=f"验证SMS验证码失败: {str(e)}",
            data=None
        )

# 刷新所有认证配置
@app.post("/test/auth/refresh-configs", tags=["配置管理"])
async def refresh_auth_configs(
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """刷新所有认证配置"""
    try:
        # 刷新配置
        await auth_config_manager.refresh_all_configs(db)
        
        # 启动定期刷新任务
        auth_config_manager.start_refresh_task(background_tasks)
        
        return ResponseModel(
            data={"success": True},
            msg="刷新认证配置成功"
        )
    except Exception as e:
        return ResponseModel(
            code="B0001",
            msg=f"刷新认证配置失败: {str(e)}",
            data=None
        )

# 检查认证配置
@app.get("/test/auth/status", tags=["配置管理"])
async def check_auth_status(
    db: AsyncSession = Depends(get_db)
):
    """检查所有认证配置状态"""
    try:
        # 导入各种认证配置
        from auth.backends.oauth import oauth_clients
        from auth.backends.cas import cas_config
        from auth.backends.sms import sms_config
        
        # 刷新配置
        await auth_config_manager.refresh_all_configs(db)
        
        # 准备状态信息
        oauth_status = {
            "enabled": bool(oauth_clients),
            "providers": list(oauth_clients.keys()) if oauth_clients else []
        }
        
        cas_status = {
            "enabled": bool(cas_config.get("server_url")),
            "server_url": cas_config.get("server_url"),
        }
        
        sms_status = {
            "enabled": bool(sms_config.get("provider")),
            "provider": sms_config.get("provider"),
        }
        
        status = {
            "oauth": oauth_status,
            "cas": cas_status,
            "sms": sms_status,
            "refresh_running": auth_config_manager.is_running
        }
        
        return ResponseModel(
            data=status,
            msg="获取认证状态成功"
        )
    except Exception as e:
        return ResponseModel(
            code="B0001",
            msg=f"获取认证状态失败: {str(e)}",
            data=None
        )

# 根节点路由
@app.get("/", tags=["首页"])
async def root():
    """首页"""
    return {
        "title": "认证系统测试工具",
        "description": "用于测试OAuth、CAS和SMS三种认证方式",
        "version": "1.0.0",
        "apis": [
            {"name": "OAuth提供商列表", "url": "/test/oauth/providers"},
            {"name": "OAuth授权URL", "url": "/test/oauth/authorize?provider=github"},
            {"name": "OAuth令牌验证", "url": "/test/oauth/validate"},
            {"name": "CAS配置", "url": "/test/cas/config"},
            {"name": "CAS票据验证", "url": "/test/cas/validate"},
            {"name": "SMS配置", "url": "/test/sms/config"},
            {"name": "SMS发送验证码", "url": "/test/sms/send"},
            {"name": "SMS验证码验证", "url": "/test/sms/validate"},
            {"name": "刷新认证配置", "url": "/test/auth/refresh-configs"},
            {"name": "认证状态检查", "url": "/test/auth/status"},
        ]
    }

# 启动应用
async def main():
    """主函数"""
    # 输出启动信息
    print("\n===== 认证系统测试工具 =====")
    print("测试服务器将在 http://127.0.0.1:8089 上运行")
    print("\n说明: 此测试工具可用于测试OAuth、CAS和SMS三种认证方式")
    print("===============================\n")
    
    # 启动服务器
    config = uvicorn.Config(app, host="127.0.0.1", port=8089)
    server = uvicorn.Server(config)
    await server.serve()

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="认证系统测试工具")
    parser.add_argument("--refresh", action="store_true", help="启动时刷新所有认证配置")
    args = parser.parse_args()
    
    # 创建事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    # 如果需要刷新配置
    if args.refresh:
        async def refresh_configs():
            # 导入数据库
            from db.session import AsyncSessionLocal
            
            # 创建会话
            async with AsyncSessionLocal() as db:
                # 刷新配置
                await auth_config_manager.refresh_all_configs(db)
                print("已刷新所有认证配置")
        
        loop.run_until_complete(refresh_configs())
    
    # 运行主函数
    loop.run_until_complete(main()) 
#!/usr/bin/env python
"""
JWT验证分析脚本
"""
import asyncio
import os
import sys
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
sys.path.append(project_root)

from fastapi.security import OAuth2PasswordRequestForm
from core.users import get_user_manager, jwt_backend
from db.session import AsyncSessionLocal

async def test_jwt_auth_manually():
    """测试JWT认证过程"""
    # 提供的凭据
    username = "yulong"
    password = "a123456789"
    
    print(f"使用凭据: username={username}, password={password}")
    
    # 创建OAuth2PasswordRequestForm实例
    # FastAPI-Users登录API需要这种格式的请求
    credentials = OAuth2PasswordRequestForm(
        username=username,
        password=password,
        scope=""
    )
    
    # 获取用户管理器和数据库会话
    async with AsyncSessionLocal() as db:
        # 创建用户管理器
        user_db = None
        async for um in get_user_manager(user_db=user_db):
            user_manager = um
            break
        
        if not user_manager:
            print("无法创建用户管理器")
            return
        
        # 设置user_manager的session属性
        user_manager.session = db
        
        try:
            # 尝试使用提供的凭据进行认证
            # 这个过程等同于JWT登录API的内部流程
            print("\n1. 使用用户管理器尝试认证...")
            user = await user_manager.authenticate(credentials)
            
            if user:
                print(f"认证成功! 用户ID: {user.id}")
                print(f"用户名: {user.username}")
                print(f"是否活跃: {user.is_active}")
                print(f"是否超级用户: {user.is_superuser}")
                
                # 创建令牌
                print("\n2. 生成JWT令牌...")
                token_data = await jwt_backend.get_strategy().write_token(user)
                print(f"JWT令牌: {token_data}")
                
                # 验证令牌
                print("\n3. 验证令牌...")
                user_from_token = await jwt_backend.get_strategy().read_token(token_data, user_manager)
                
                if user_from_token:
                    print(f"令牌验证成功! 用户ID: {user_from_token.id}")
                else:
                    print("令牌验证失败")
            else:
                print("认证失败: 用户为空")
                
        except Exception as e:
            print(f"认证过程中出现错误: {str(e)}")
            
            # 尝试手动查询用户
            print("\n尝试手动查询用户...")
            try:
                user_by_name = await user_manager.get_by_username(username)
                if user_by_name:
                    print(f"通过用户名找到用户: {user_by_name.username}")
                    print(f"存储的密码哈希: {user_by_name.hashed_password}")
                    
                    # 验证密码
                    from core.security import verify_password
                    is_valid = verify_password(password, user_by_name.hashed_password)
                    print(f"密码验证结果: {'成功' if is_valid else '失败'}")
                    
                    if not is_valid:
                        print("错误原因：密码验证失败")
                    else:
                        print("密码验证成功，但认证失败，可能是用户管理器设置问题")
                else:
                    print(f"未找到用户名为 {username} 的用户")
            except Exception as e:
                print(f"手动查询用户出错: {str(e)}")

async def main():
    """主函数"""
    print("===== JWT认证流程分析 =====")
    await test_jwt_auth_manually()
    print("===== 分析完成 =====")

if __name__ == "__main__":
    asyncio.run(main()) 
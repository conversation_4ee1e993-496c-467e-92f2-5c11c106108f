#!/usr/bin/env python
"""
使用原始SQL查询检查用户角色和权限
"""
import asyncio
import sys
import os
import psycopg2
import psycopg2.extras
from dotenv import load_dotenv

# 加载环境变量
dotenv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), '.env')
load_dotenv(dotenv_path)

# 数据库配置
DB_HOST = os.getenv("POSTGRES_SERVER")
DB_PORT = os.getenv("POSTGRES_PORT")
DB_NAME = os.getenv("POSTGRES_DB")
DB_USER = os.getenv("POSTGRES_USER")
DB_PASS = os.getenv("POSTGRES_PASSWORD")

def get_connection():
    """获取数据库连接"""
    return psycopg2.connect(
        host=DB_HOST,
        port=DB_PORT,
        dbname=DB_NAME,
        user=DB_USER,
        password=DB_PASS
    )

def check_user_permissions(user_id):
    """使用原始SQL查询检查用户角色和权限"""
    try:
        conn = get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # 查询用户信息
        cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))
        user = cursor.fetchone()
        
        if not user:
            print(f"找不到ID为 {user_id} 的用户")
            return
            
        print(f"用户信息: ID={user['id']}, 用户名={user['username']}, 邮箱={user['email']}")
        print(f"超级管理员: {'是' if user['is_superuser'] else '否'}")
        
        # 如果是超级管理员，拥有所有权限
        if user['is_superuser']:
            print("超级管理员拥有所有权限")
            
            # 查询所有权限
            cursor.execute("SELECT * FROM permissions")
            permissions = cursor.fetchall()
            
            print("\n系统所有权限:")
            for perm in permissions:
                print(f"  - {perm['name']}")
                
            return
        
        # 查询用户角色
        cursor.execute("""
            SELECT r.* FROM roles r
            JOIN user_role ur ON r.id = ur.role_id
            WHERE ur.user_id = %s
        """, (user_id,))
        
        roles = cursor.fetchall()
        
        if roles:
            print("\n用户角色:")
            for role in roles:
                print(f"  - {role['name']}")
                
                # 查询角色权限
                cursor.execute("""
                    SELECT p.* FROM permissions p
                    JOIN role_permission rp ON p.id = rp.permission_id
                    WHERE rp.role_id = %s
                """, (role['id'],))
                
                permissions = cursor.fetchall()
                
                if permissions:
                    print(f"    权限:")
                    for perm in permissions:
                        print(f"      - {perm['name']}")
                else:
                    print(f"    (无权限)")
        else:
            print("\n用户没有任何角色")
            
        # 查询用户所有权限
        cursor.execute("""
            SELECT DISTINCT p.* FROM permissions p
            JOIN role_permission rp ON p.id = rp.permission_id
            JOIN roles r ON r.id = rp.role_id
            JOIN user_role ur ON r.id = ur.role_id
            WHERE ur.user_id = %s
        """, (user_id,))
        
        all_permissions = cursor.fetchall()
        
        if all_permissions:
            print("\n用户所有权限:")
            for perm in all_permissions:
                print(f"  - {perm['name']}")
                
            # 检查特定权限
            has_user_read = any(p['name'] == 'user:read' for p in all_permissions)
            print(f"\n是否有user:read权限: {'是' if has_user_read else '否'}")
        else:
            print("\n用户没有任何权限")
            print(f"\n是否有user:read权限: 否")
            
    except Exception as e:
        print(f"查询出错: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python check_user_raw.py <用户ID>")
        return
        
    try:
        user_id = int(sys.argv[1])
        print(f"===== 检查用户ID={user_id}的角色和权限 =====")
        check_user_permissions(user_id)
        print("===== 检查完成 =====")
    except ValueError:
        print("错误: 用户ID必须是整数")

if __name__ == "__main__":
    main() 
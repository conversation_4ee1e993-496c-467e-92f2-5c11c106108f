#!/usr/bin/env python
"""
JWT令牌验证工具 - 检查令牌是否与当前环境的密钥匹配
"""
import sys
import os
import jwt
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
sys.path.append(project_root)

from core.config import settings

def decode_jwt(token):
    """
    解码JWT令牌并打印详细信息
    
    Args:
        token: JWT令牌字符串
    """
    print("\n===== JWT令牌验证 =====")
    
    # 1. 不验证签名的解码
    try:
        print("1. 不验证签名解码结果:")
        payload = jwt.decode(token, options={"verify_signature": False})
        print(f"   负载: {payload}")
        
        # 检查令牌是否过期
        exp = payload.get('exp', 0)
        now = datetime.now().timestamp()
        if exp < now:
            print(f"   状态: 令牌已过期，过期时间: {datetime.fromtimestamp(exp)}")
        else:
            print(f"   状态: 令牌有效，过期时间: {datetime.fromtimestamp(exp)}")
            
        # 检查用户ID
        user_id = payload.get('sub')
        if user_id:
            print(f"   用户ID: {user_id}")
        else:
            print("   错误: 令牌中没有用户ID (sub)")
    except Exception as e:
        print(f"   解码失败: {str(e)}")
    
    # 2. 使用当前环境密钥验证
    try:
        print("\n2. 使用当前环境密钥验证:")
        print(f"   当前JWT密钥: {settings.JWT_SECRET[:5]}...{settings.JWT_SECRET[-5:]}")
        verified_payload = jwt.decode(token, settings.JWT_SECRET, algorithms=[settings.JWT_ALGORITHM])
        print(f"   验证成功! 负载: {verified_payload}")
        print("   结论: 令牌与当前环境密钥匹配")
    except jwt.InvalidSignatureError:
        print("   错误: 令牌签名无效 - 令牌不是使用当前环境的密钥签发的")
    except jwt.ExpiredSignatureError:
        print("   错误: 令牌已过期 - 虽然签名有效，但令牌已过期")
    except Exception as e:
        print(f"   验证失败: {str(e)}")
    
    print("\n===== 建议操作 =====")
    print("1. 如果令牌签名无效，请使用当前环境登录获取新令牌")
    print("2. 如果令牌已过期，请重新登录刷新令牌")
    print("3. 如果需要在环境间保持令牌一致，请确保所有环境使用相同的JWT_SECRET")
    print("===================================\n")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python check_token.py <JWT令牌>")
        sys.exit(1)
    
    token = sys.argv[1]
    decode_jwt(token) 
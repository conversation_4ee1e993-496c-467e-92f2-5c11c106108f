# 登录问题修复方案

## 问题描述

FastAPI Users库默认只支持使用邮箱进行登录，而无法使用用户名登录。用户尝试使用用户名登录时会返回"LOGIN_BAD_CREDENTIALS"错误。

## 解决方案

我们实现了一个自定义认证端点，支持同时使用用户名或邮箱登录。具体实现：

1. 创建了`auth/exceptions.py`模块，定义认证相关异常
2. 创建了`api/api_v1/endpoints/custom_auth.py`模块，实现支持用户名/邮箱登录的端点
3. 在`api/api_v1/api.py`中注册了自定义认证路由
4. 在`core/security.py`中添加了refresh_token相关功能
5. 在`core/config.py`中添加了刷新令牌过期配置

## 登录端点

现在项目支持以下登录端点：

1. **JWT登录端点**：`POST /api/v1/auth/jwt/login`
   - 支持用户名或邮箱登录
   - 使用标准OAuth2表单提交（username和password字段）
   - 返回访问令牌和刷新令牌

2. **灵活登录端点**：`POST /api/v1/auth/flexible-login`
   - 支持用户名或邮箱登录
   - 使用JSON请求体，格式：`{"username": "用户名或邮箱", "password": "密码"}`
   - 返回访问令牌和刷新令牌

## 测试用户

系统预置了以下测试用户：

1. **普通用户**
   - 用户名：yulong
   - 邮箱：<EMAIL>
   - 密码：a123456789

2. **管理员用户**
   - 用户名：admin
   - 邮箱：<EMAIL>
   - 密码：admin123

## 登录过程

1. 当用户提交登录请求时，系统会：
   - 先尝试通过用户名查找用户
   - 如果未找到，再尝试通过邮箱查找
   - 验证用户是否存在且已激活
   - 验证密码是否正确
   - 生成访问令牌和刷新令牌
   - 执行登录后回调（更新最后登录时间等）
   - 缓存令牌数据（可选）
   - 返回令牌

2. 用户在后续请求中使用Bearer令牌认证：
   - 在请求头中添加：`Authorization: Bearer {access_token}`

## 改进建议

1. 添加令牌刷新端点，实现无缝令牌续期
2. 实现多端同时登录控制（如限制同一用户最多登录设备数量）
3. 添加登录日志，记录登录IP、设备信息等
4. 实现基于Redis的令牌黑名单，支持令牌主动失效

# JWT认证修复指南

## 问题分析

经过诊断，我们发现Swagger中的Authorize能够设置JWT令牌，但**使用该令牌访问API端点却返回"401 Unauthorized"错误**。我们通过创建测试服务确认，同样的令牌在自定义认证中有效，但在FastAPI Users的认证中无效。

## 根本原因

本问题的根本原因是FastAPI Users库的JWT策略实现与FastAPI默认的安全机制存在差异：

1. FastAPI Users使用自己的JWT策略来验证令牌
2. 令牌验证过程中存在特定的格式要求或其他验证步骤
3. 当前的`CachedJWTStrategy`类实现中可能存在问题，影响Authorize功能

## 解决方案

### 方案1：修复FastAPI Users的JWT策略（推荐）

修改`core/users.py`中的`CachedJWTStrategy`类，确保它正确处理来自认证头的令牌：

```python
class CachedJWTStrategy(JWTStrategy):
    """支持缓存的JWT策略"""
    
    async def read_token(
        self, token: str, user_manager: Optional[BaseUserManager] = None
    ) -> Optional[User]:
        """
        读取令牌，优先从缓存获取令牌数据
        
        Args:
            token: JWT令牌
            user_manager: 用户管理器
            
        Returns:
            验证成功返回用户对象，否则返回None
        """
        try:
            # 使用RedisService获取缓存实例
            from services.redis_service import RedisService
            from utils.auth_cache import AuthCache
            
            redis_cache = await RedisService.get_cache()
            auth_cache = AuthCache(redis_cache)
            
            # 调试输出
            print(f"Token: {token[:10]}...")
            
            # 尝试从缓存中获取令牌数据
            token_data = await auth_cache.get_token_data(token)
            if token_data:
                # 如果缓存命中，从令牌数据中获取用户ID
                user_id = token_data.get("sub")
                if user_id and user_manager:
                    # 使用用户ID获取用户
                    return await user_manager.get_user(int(user_id))
                    
            # 缓存未命中，使用常规方式验证令牌
            user = await super().read_token(token, user_manager)
            
            # 如果验证成功，缓存令牌数据
            if user:
                # 从令牌中提取数据
                token_data = self.decode_token(token)
                # 缓存令牌数据
                await auth_cache.set_token_data(token, token_data)
            
            return user
            
        except Exception as e:
            # 出现异常时记录日志并回退到标准方法
            import logging
            logging.getLogger(__name__).error(f"缓存令牌验证失败: {str(e)}")
            return await super().read_token(token, user_manager)
```

### 方案2：创建自定义认证依赖项

在`api/deps.py`中创建替代依赖项，绕过FastAPI Users的认证：

```python
from fastapi import Depends, HTTPException, status, Header
from typing import Optional
from auth.users import User
from db.session import get_db
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import jwt
from core.config import settings

# 从授权头获取令牌
async def get_token_from_authorization(
    authorization: Optional[str] = Header(None, description="JWT令牌，格式: Bearer <token>")
) -> Optional[str]:
    """从Authorization头获取令牌"""
    if not authorization:
        return None
    
    parts = authorization.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, 
            detail="无效的授权头",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    return parts[1]

# 解码JWT令牌
def decode_token(token: str):
    """解码JWT令牌"""
    try:
        return jwt.decode(token, settings.JWT_SECRET, algorithms=[settings.JWT_ALGORITHM])
    except jwt.PyJWTError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"无效的令牌: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

# 获取当前用户
async def get_current_user(
    token: str = Depends(get_token_from_authorization),
    db: AsyncSession = Depends(get_db)
) -> User:
    """获取当前用户"""
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 解码令牌获取用户ID
    payload = decode_token(token)
    user_id = payload.get("sub")
    
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的令牌内容",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 从数据库获取用户
    query = select(User).where(User.id == int(user_id))
    result = await db.execute(query)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="找不到用户",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user

# 用于替代current_active_user的依赖项
current_active_user_custom = get_current_user
```

然后，在`api/api_v1/endpoints/users.py`中替换依赖项：

```python
# 将原来的
from auth.users import User, current_active_user

# 更改为
from auth.users import User
from api.deps import current_active_user_custom as current_active_user
```

### 方案3：标准JWT认证

如果问题持续存在，可以考虑完全替换FastAPI Users的JWT认证，使用标准的FastAPI JWT认证方案：

1. 在`main.py`中添加全局依赖项覆盖，应用到所有保护的端点
2. 实现自定义的JWT认证处理逻辑，独立于FastAPI Users

## 最佳实践

为避免类似问题，建议采取以下最佳实践：

1. **统一认证格式**：确保令牌格式在所有地方保持一致
2. **增加日志记录**：为认证过程添加更详细的日志
3. **定期测试认证流程**：使用本次诊断的测试脚本定期验证
4. **提供明确的错误信息**：在认证失败时返回具体原因

## 即时解决方案

如果需要立即解决问题但不想修改代码，可使用以下方式登录并获取令牌：

```bash
curl -X 'POST' 'http://127.0.0.1:8000/api/v1/auth/flexible-login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "yulong",
    "password": "a123456789"
  }'
```

或者使用我们创建的测试服务器：

```bash
curl -X 'POST' 'http://127.0.0.1:8088/api/v1/test/create-token?user_id=5'
``` 
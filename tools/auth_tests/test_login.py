#!/usr/bin/env python
"""
登录测试脚本
"""
import asyncio
import httpx
import json
from pprint import pprint

# 服务URL
API_URL = "http://localhost:8000"

async def test_jwt_login():
    """测试JWT登录"""
    print("\n=== 测试JWT登录 ===")
    
    async with httpx.AsyncClient() as client:
        # 首先获取令牌URL确认
        try:
            docs_response = await client.get(f"{API_URL}/api/v1/openapi.json")
            if docs_response.status_code == 200:
                openapi_data = docs_response.json()
                jwt_path = openapi_data.get("components", {}).get("securitySchemes", {}).get("bearerAuth", {})
                print(f"OpenAPI JWT设置: {json.dumps(jwt_path, indent=2)}")
        except Exception as e:
            print(f"获取OpenAPI信息失败: {str(e)}")
            
        # 使用युlong用户登录
        login_data = {
            "username": "yulong",
            "password": "a123456789",
        }
        
        print("发送登录请求:")
        print(f"URL: {API_URL}/api/v1/auth/jwt/login")
        print(f"用户名: {login_data['username']}")
        print(f"密码: {login_data['password']}")
        
        response = await client.post(
            f"{API_URL}/api/v1/auth/jwt/login",
            data=login_data,
            headers={
                "Content-Type": "application/x-www-form-urlencoded"
            }
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        if response.status_code == 200:
            data = response.json()
            print("JWT登录成功!")
            print("响应数据:")
            pprint(data)
            return data.get("access_token")
        else:
            print("JWT登录失败!")
            print("错误信息:")
            pprint(response.json())
            print("\n请求信息:")
            print(f"URL: {API_URL}/api/v1/auth/jwt/login")
            print(f"数据: {login_data}")
            
            # 尝试使用原始认证格式请求
            print("\n尝试使用原始OAuth2格式请求:")
            oauth_response = await client.post(
                f"{API_URL}/api/v1/auth/jwt/login",
                data={
                    "grant_type": "password",  # OAuth2 格式
                    "username": "yulong",
                    "password": "a123456789",
                },
                headers={
                    "Content-Type": "application/x-www-form-urlencoded"
                }
            )
            print(f"原始OAuth2状态码: {oauth_response.status_code}")
            if oauth_response.status_code == 200:
                print("OAuth2格式登录成功!")
                return oauth_response.json().get("access_token")
            else:
                print("OAuth2格式登录失败:")
                pprint(oauth_response.json())
            
            return None

async def test_protected_endpoint(token):
    """测试受保护的端点"""
    if not token:
        print("没有有效令牌，跳过测试受保护的端点")
        return
        
    print("\n=== 测试受保护的端点 ===")
    
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{API_URL}/api/v1/users/me",
            headers={
                "Authorization": f"Bearer {token}"
            }
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("访问成功!")
            print("响应数据:")
            pprint(response.json())
        else:
            print("访问失败!")
            print("错误信息:")
            pprint(response.json())

async def main():
    """主函数"""
    print("开始登录测试...")
    
    # 测试JWT登录
    token = await test_jwt_login()
    
    # 测试受保护的端点
    if token:
        await test_protected_endpoint(token)
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(main()) 
#!/usr/bin/env python
"""
JWT认证测试服务器 - 修复版
用于测试和诊断JWT认证问题
解决了原fix_login.py中的异步运行问题
"""

import sys
import os
import uvicorn
from fastapi import FastAPI, Depends, HTTPException, Request, Query
from fastapi.responses import JSONResponse
from datetime import datetime, timedelta
import jwt

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
sys.path.append(project_root)

from db.session import get_db
from auth.users import current_active_user, User
from api.deps import get_current_user, get_token_from_authorization
from core.config import settings
from core.security import create_access_token

app = FastAPI(title="JWT认证测试服务器", docs_url="/docs")

# 公开端点，无需认证
@app.get("/api/v1/public/test")
async def public_test():
    """公开测试端点，不需要认证"""
    return {"message": "这是一个公开端点，无需认证"}

# 使用FastAPI Users认证的端点
@app.get("/api/v1/test/fastapi-users")
async def fastapi_users_test(current_user: User = Depends(current_active_user)):
    """
    使用FastAPI Users认证的测试端点
    
    此端点通过FastAPI Users提供的current_active_user依赖验证JWT令牌。
    如果使用自定义JWT令牌，此端点可能返回401未授权错误。
    """
    return {
        "message": "FastAPI Users认证成功",
        "user": {
            "id": current_user.id,
            "username": current_user.username,
            "email": current_user.email
        }
    }

# 使用自定义JWT认证的端点
@app.get("/api/v1/test/custom-jwt")
async def custom_jwt_test(current_user: User = Depends(get_current_user)):
    """
    使用自定义JWT认证的测试端点
    
    此端点通过自定义的get_current_user依赖验证JWT令牌。
    无论是FastAPI Users生成的令牌还是手动创建的令牌都应能通过验证。
    """
    return {
        "message": "自定义JWT认证成功",
        "user": {
            "id": current_user.id,
            "username": current_user.username,
            "email": current_user.email
        }
    }

# 验证JWT令牌
@app.get("/api/v1/test/token-validation")
async def validate_token(token: str = Depends(get_token_from_authorization)):
    """
    验证JWT令牌是否有效
    
    此端点仅验证令牌格式和签名是否有效，不做其他验证。
    """
    try:
        payload = jwt.decode(token, settings.JWT_SECRET, algorithms=[settings.JWT_ALGORITHM])
        return {
            "valid": True,
            "payload": payload,
            "message": "令牌有效"
        }
    except jwt.ExpiredSignatureError:
        return {
            "valid": False,
            "error": "令牌已过期",
            "message": "请刷新令牌或重新登录"
        }
    except jwt.InvalidTokenError as e:
        return {
            "valid": False,
            "error": str(e),
            "message": "无效的令牌"
        }

# 创建测试令牌
@app.post("/api/v1/test/create-token")
async def create_token(
    user_id: int = Query(..., description="用户ID"),
    expires_minutes: int = Query(30, description="令牌有效期（分钟）")
):
    """
    为指定用户创建测试JWT令牌
    
    此端点创建与真实登录生成的令牌完全相同的JWT令牌，用于测试。
    """
    # 创建令牌
    token = create_access_token(
        subject=str(user_id),
        expires_delta=timedelta(minutes=expires_minutes)
    )
    
    # 解码令牌以显示信息
    payload = jwt.decode(token, settings.JWT_SECRET, algorithms=[settings.JWT_ALGORITHM])
    
    return {
        "access_token": token,
        "token_type": "bearer",
        "expires_in": expires_minutes * 60,
        "user_id": user_id,
        "created_at": datetime.now(timezone.utc).isoformat(),
        "payload": payload
    }

# 查看当前环境配置
@app.get("/api/v1/test/config")
async def view_config():
    """查看当前环境配置"""
    return {
        "jwt_secret_preview": f"{settings.JWT_SECRET[:5]}...{settings.JWT_SECRET[-5:]}",
        "jwt_algorithm": settings.JWT_ALGORITHM,
        "access_token_expire_minutes": settings.ACCESS_TOKEN_EXPIRE_MINUTES,
    }

def main():
    """主函数 - 运行API服务器"""
    print("===== JWT认证调试服务器 =====")
    print(f"测试服务器将在 http://127.0.0.1:8088 上运行")
    print("可用端点:")
    print("- GET /api/v1/public/test (公开，无需认证)")
    print("- GET /api/v1/test/fastapi-users (使用FastAPI Users认证)")
    print("- GET /api/v1/test/custom-jwt (使用自定义JWT认证)")
    print("- GET /api/v1/test/token-validation (验证令牌)")
    print("- POST /api/v1/test/create-token?user_id=<用户ID> (创建测试令牌)")
    print("- GET /api/v1/test/config (查看当前环境配置)")
    print("使用示例:")
    print("1. 获取用户令牌:")
    print("   curl -X POST 'http://127.0.0.1:8088/api/v1/test/create-token?user_id=5'")
    print("2. 测试令牌验证:")
    print("   curl -X GET 'http://127.0.0.1:8088/api/v1/test/token-validation' -H 'Authorization: Bearer <您的令牌>'")
    print("3. 获取用户信息:")
    print("   curl -X GET 'http://127.0.0.1:8088/api/v1/test/custom-jwt' -H 'Authorization: Bearer <您的令牌>'")
    print("说明: 此测试服务仅用于诊断JWT认证问题")
    print("=============================")
    
    try:
        # 正确方式：直接使用uvicorn运行，不在异步环境中调用
        uvicorn.run(app, host="127.0.0.1", port=8088)
    except OSError as e:
        if "address already in use" in str(e):
            print("错误: 端口8088已被占用，请关闭占用该端口的程序后重试")
            print("可以尝试使用不同端口: python fixed_login.py --port=8089")
            
            # 尝试使用备用端口
            try:
                print("尝试使用备用端口8089...")
                uvicorn.run(app, host="127.0.0.1", port=8089)
            except Exception as e2:
                print(f"启动服务器失败: {e2}")
        else:
            print(f"启动服务器时出错: {e}")

if __name__ == "__main__":
    main()  # 直接调用main函数，不使用asyncio.run() 
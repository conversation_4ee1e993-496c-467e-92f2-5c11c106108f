#!/usr/bin/env python
"""
设置用户为超级管理员的脚本
"""
import sys
import os
import psycopg2
import psycopg2.extras
from dotenv import load_dotenv

# 加载环境变量
dotenv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), '.env')
load_dotenv(dotenv_path)

# 数据库配置
DB_HOST = os.getenv("POSTGRES_SERVER")
DB_PORT = os.getenv("POSTGRES_PORT")
DB_NAME = os.getenv("POSTGRES_DB")
DB_USER = os.getenv("POSTGRES_USER")
DB_PASS = os.getenv("POSTGRES_PASSWORD")

def get_connection():
    """获取数据库连接"""
    return psycopg2.connect(
        host=DB_HOST,
        port=DB_PORT,
        dbname=DB_NAME,
        user=DB_USER,
        password=DB_PASS
    )

def set_superuser(user_id, is_superuser=True):
    """设置用户为超级管理员"""
    try:
        conn = get_connection()
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # 查询用户
        cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))
        user = cursor.fetchone()
        
        if not user:
            print(f"错误: 找不到ID为 {user_id} 的用户")
            return False
            
        current_status = user['is_superuser']
        status_text = "超级管理员" if current_status else "普通用户"
        print(f"用户: {user['username']} (ID: {user['id']}), 当前状态: {status_text}")
        
        # 如果状态已经是目标状态，直接返回
        if current_status == is_superuser:
            status_text = "超级管理员" if is_superuser else "普通用户"
            print(f"用户已经是{status_text}，无需修改")
            return True
        
        # 更新用户状态
        cursor.execute(
            "UPDATE users SET is_superuser = %s WHERE id = %s",
            (is_superuser, user_id)
        )
        conn.commit()
        
        status_text = "超级管理员" if is_superuser else "普通用户"
        print(f"已成功将用户 {user['username']} 设置为{status_text}")
        return True
        
    except Exception as e:
        print(f"设置超级管理员出错: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python set_superuser.py <用户ID> [true/false]")
        return
        
    try:
        user_id = int(sys.argv[1])
        
        # 解析是否设置为超级管理员，默认为True
        is_superuser = True
        if len(sys.argv) > 2:
            is_superuser_str = sys.argv[2].lower()
            if is_superuser_str in ['false', '0', 'no', 'n', 'f']:
                is_superuser = False
        
        status_text = "超级管理员" if is_superuser else "普通用户"
        print(f"===== 将用户ID={user_id}设置为{status_text} =====")
        success = set_superuser(user_id, is_superuser)
        
        if success:
            print("===== 设置完成 =====")
        else:
            print("===== 设置失败 =====")
            
    except ValueError:
        print("错误: 用户ID必须是整数")

if __name__ == "__main__":
    main() 
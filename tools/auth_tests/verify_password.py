#!/usr/bin/env python
"""
验证用户密码是否匹配
"""
import asyncio
import os
import sys
from typing import Optional, Tuple

# 添加当前目录到Python路径，确保可以导入项目模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.session import AsyncSessionLocal
from core.security import verify_password
from sqlalchemy.sql import text

async def get_user_by_username(username: str) -> Optional[Tuple]:
    """获取用户信息"""
    async with AsyncSessionLocal() as db:
        query = text("SELECT id, username, hashed_password FROM users WHERE username = :username")
        result = await db.execute(query, {"username": username})
        user = result.first()
        return user

async def check_password(username: str, password: str) -> bool:
    """验证用户密码"""
    user = await get_user_by_username(username)
    if not user:
        print(f"用户 {username} 不存在")
        return False
    
    user_id, username, hashed_password = user
    
    # 验证密码
    is_valid = verify_password(password, hashed_password)
    
    print(f"用户ID: {user_id}")
    print(f"用户名: {username}")
    print(f"存储的密码哈希: {hashed_password}")
    print(f"输入的密码: {password}")
    print(f"密码验证结果: {'成功' if is_valid else '失败'}")
    
    return is_valid

async def main():
    """主函数"""
    print("开始验证用户密码...")
    
    # 测试用户yulong
    await check_password("yulong", "a123456789")
    
    # 也测试内置管理员用户
    print("\n验证管理员账号:")
    await check_password("admin", "admin123")
    
    print("\n验证完成!")

if __name__ == "__main__":
    asyncio.run(main()) 
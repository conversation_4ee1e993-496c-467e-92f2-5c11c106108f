#!/usr/bin/env python
"""
检查用户角色的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
sys.path.append(project_root)

from sqlalchemy import select, join
from db.session import AsyncSessionLocal
from models.user import User
from models.role import Role

async def get_user_roles(user_id):
    """获取用户角色"""
    async with AsyncSessionLocal() as db:
        # 获取用户信息
        query = select(User).where(User.id == user_id)
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        if not user:
            print(f"找不到ID为 {user_id} 的用户")
            return
            
        print(f"用户信息: ID={user.id}, 用户名={user.username}, 邮箱={user.email}")
        print(f"超级管理员: {'是' if user.is_superuser else '否'}")
        
        # 如果是超级管理员，拥有所有权限
        if user.is_superuser:
            print("超级管理员拥有所有权限")
        
        # 输出用户角色
        if hasattr(user, 'roles') and user.roles:
            print(f"\n用户角色:")
            for role in user.roles:
                print(f"  - {role.name}")
        else:
            print("\n用户没有任何角色")

async def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python check_user_roles.py <用户ID>")
        return
        
    try:
        user_id = int(sys.argv[1])
        print(f"===== 检查用户ID={user_id}的角色 =====")
        await get_user_roles(user_id)
        print("===== 检查完成 =====")
    except ValueError:
        print("错误: 用户ID必须是整数")
    except Exception as e:
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main()) 
#!/usr/bin/env python
"""
检查用户权限的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
sys.path.append(project_root)

from sqlalchemy import select
from db.session import AsyncSessionLocal
from models.user import User
from models.role import Permission, Role, role_permission, user_role

async def get_user_permissions(user_id):
    """获取用户权限"""
    async with AsyncSessionLocal() as db:
        # 获取用户信息
        query = select(User).where(User.id == user_id)
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        if not user:
            print(f"找不到ID为 {user_id} 的用户")
            return
            
        print(f"用户信息: ID={user.id}, 用户名={user.username}, 邮箱={user.email}")
        print(f"超级管理员: {'是' if user.is_superuser else '否'}")
        
        # 如果是超级管理员，拥有所有权限
        if user.is_superuser:
            print("超级管理员拥有所有权限")
            return
        
        # 获取用户角色
        query = select(Role).join(user_role, user_role.c.role_id == Role.id).where(user_role.c.user_id == user_id)
        result = await db.execute(query)
        roles = result.scalars().all()
        
        print(f"\n用户角色: {[role.name for role in roles]}")
        
        if not roles:
            print("用户没有任何角色")
            return
        
        # 获取所有权限
        all_permissions = []
        for role in roles:
            print(f"\n角色 '{role.name}' 的权限:")
            query = select(Permission).join(role_permission, role_permission.c.permission_id == Permission.id).where(role_permission.c.role_id == role.id)
            result = await db.execute(query)
            permissions = result.scalars().all()
            
            if permissions:
                for p in permissions:
                    print(f"  - {p.name}")
                all_permissions.extend(permissions)
            else:
                print("  (无权限)")
        
        # 去重
        unique_permissions = {p.name: p for p in all_permissions}.values()
        print(f"\n用户所有权限: {[p.name for p in unique_permissions]}")
        
        # 检查特定权限
        has_user_read = any(p.name == "user:read" for p in unique_permissions)
        print(f"\n是否有user:read权限: {'是' if has_user_read else '否'}")

async def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python check_user_permissions.py <用户ID>")
        return
        
    try:
        user_id = int(sys.argv[1])
        print(f"===== 检查用户ID={user_id}的权限 =====")
        await get_user_permissions(user_id)
        print("===== 检查完成 =====")
    except ValueError:
        print("错误: 用户ID必须是整数")
    except Exception as e:
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main()) 
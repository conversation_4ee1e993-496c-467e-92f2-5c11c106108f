# 登录问题修复指南

## 问题分析

经过诊断，我们发现FastAPI Users框架默认使用**邮箱**而非**用户名**进行身份验证。当用户尝试使用用户名登录时，会返回"LOGIN_BAD_CREDENTIALS"错误。

## 解决方案

### 方案1: 创建辅助登录脚本

已创建`login.py`脚本，自动将用户名转换为对应的邮箱进行登录。

使用方法:
```bash
python login.py <用户名> <密码>
```

### 方案2: 修改FastAPI Users配置

要修改FastAPI Users框架使用用户名而非邮箱进行身份验证，需要修改以下文件:

1. `core/users.py`:
   - 在`UserManager`类中覆盖`authenticate`方法，使其支持用户名验证

```python
async def authenticate(self, credentials):
    # 支持用户名和邮箱登录
    # 尝试通过用户名查找用户
    user = await self.get_by_username(credentials.username)
    if not user:
        # 尝试通过邮箱查找用户
        user = await self.get_by_email(credentials.username)
    
    if not user or not user.is_active:
        return None
    
    verified, updated_password_hash = self.password_helper.verify_and_update(
        credentials.password, user.hashed_password
    )
    if not verified:
        return None
    
    # 更新密码哈希（如果需要）
    if updated_password_hash:
        await self._update_password(user, updated_password_hash)
    
    return user
```

2. 为`UserManager`添加`get_by_username`方法:

```python
async def get_by_username(self, username: str):
    # 根据用户名获取用户
    statement = select(self.model).where(self.model.username == username)
    user = await self.session.execute(statement)
    return user.scalar_one_or_none()
```

### 方案3: 在前端使用邮箱登录

如果前端负责登录表单，可以将登录表单修改为发送用户的邮箱而非用户名。

## 临时解决方法

目前，您可以使用邮箱直接登录，而不是用户名:

```
用户名: yulong
邮箱: <EMAIL>  <!-- 使用此项登录 -->
密码: a123456789
```


#!/usr/bin/env python
'''
登录辅助脚本 - 自动填入邮箱而非用户名
'''
import asyncio
import httpx
import json
import sys
from pprint import pprint

# 服务URL
API_URL = "http://localhost:8000"

# 用户映射 (用户名 -> 邮箱)
USER_EMAIL_MAP = {
    'yulong': '<EMAIL>',
    # 可以添加更多用户
}

async def login(username, password):
    """登录并获取令牌"""
    print(f"登录用户: {username}")
    
    # 查找邮箱
    email = USER_EMAIL_MAP.get(username)
    if not email:
        print(f"错误: 未找到用户{username}的邮箱")
        return None
    
    print(f"使用邮箱登录: {email}")
    
    async with httpx.AsyncClient() as client:
        login_data = {
            "username": email,  # 使用邮箱而非用户名
            "password": password,
        }
        
        response = await client.post(
            f"{API_URL}/api/v1/auth/jwt/login",
            data=login_data,
            headers={
                "Content-Type": "application/x-www-form-urlencoded"
            }
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("登录成功!")
            print("访问令牌:", data.get("access_token"))
            print("令牌类型:", data.get("token_type"))
            return data.get("access_token")
        else:
            print("登录失败:")
            try:
                pprint(response.json())
            except:
                print(response.text)
            return None

async def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("使用方法: python login.py <用户名> <密码>")
        return
    
    username = sys.argv[1]
    password = sys.argv[2]
    
    token = await login(username, password)
    if token:
        print("\n可以使用以下令牌访问API:")
        print(f"Authorization: Bearer {token}")

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python
"""
认证和权限性能测试脚本
测试不同情况下的权限验证性能，包括缓存和非缓存模式
"""

import sys
import os
import asyncio
import time
from typing import List, Optional
import statistics
import json

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
sys.path.append(project_root)

from sqlalchemy.ext.asyncio import AsyncSession
from db.session import get_db
from auth.dependencies import get_user_permissions, get_user_permissions_cached
from auth.users import User
from utils.permission_cache import get_permission_cache

# 测试配置
TEST_ITERATIONS = 20  # 每个测试执行的次数
TEST_USER_IDS = [1, 5]  # 要测试的用户ID

class PerformanceTest:
    """性能测试类"""
    
    def __init__(self):
        """初始化性能测试"""
        self.results = []
        self.user_cache = {}
    
    async def get_user(self, db: AsyncSession, user_id: int) -> Optional[User]:
        """获取用户对象（带缓存）"""
        if user_id in self.user_cache:
            return self.user_cache[user_id]
        
        from sqlalchemy import select
        from auth.users import User
        
        stmt = select(User).where(User.id == user_id)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if user:
            self.user_cache[user_id] = user
        
        return user
    
    async def test_get_permissions(self, db: AsyncSession, user_id: int) -> float:
        """测试权限获取函数的性能（非缓存版本）"""
        user = await self.get_user(db, user_id)
        if not user:
            print(f"用户 ID={user_id} 不存在")
            return 0
        
        start_time = time.time()
        perms = await get_user_permissions(user, db)
        end_time = time.time()
        
        duration = end_time - start_time
        return duration
    
    async def test_get_permissions_cached(self, db: AsyncSession, user_id: int) -> float:
        """测试权限获取函数的性能（缓存版本）"""
        user = await self.get_user(db, user_id)
        if not user:
            print(f"用户 ID={user_id} 不存在")
            return 0
        
        start_time = time.time()
        perms = await get_user_permissions_cached(user, db)
        end_time = time.time()
        
        duration = end_time - start_time
        return duration
    
    async def run_test_suite(self):
        """运行完整测试套件"""
        print("=== 认证和权限性能测试 ===")
        
        # 获取数据库会话
        db_gen = get_db()
        db = await anext(db_gen)
        
        # 清除缓存，确保测试公平
        try:
            cache = await get_permission_cache()
            await cache.invalidate_all_permissions()
            print("已清除权限缓存")
        except Exception as e:
            print(f"清除缓存失败: {e}")
        
        # 测试每个用户
        for user_id in TEST_USER_IDS:
            print(f"\n测试用户ID={user_id}的权限获取性能:")
            
            # 测试标准权限获取
            standard_times = []
            print(f"1. 测试标准权限获取函数 ({TEST_ITERATIONS}次)")
            for i in range(TEST_ITERATIONS):
                duration = await self.test_get_permissions(db, user_id)
                if duration > 0:
                    standard_times.append(duration)
                    print(f"  迭代 {i+1}: {duration:.6f}秒")
            
            # 测试缓存权限获取
            cached_times = []
            print(f"\n2. 测试缓存权限获取函数 ({TEST_ITERATIONS}次)")
            for i in range(TEST_ITERATIONS):
                duration = await self.test_get_permissions_cached(db, user_id)
                if duration > 0:
                    cached_times.append(duration)
                    print(f"  迭代 {i+1}: {duration:.6f}秒")
            
            # 计算结果
            if standard_times:
                std_avg = statistics.mean(standard_times)
                std_min = min(standard_times)
                std_max = max(standard_times)
                
                print(f"\n标准权限获取统计:")
                print(f"  平均时间: {std_avg:.6f}秒")
                print(f"  最小时间: {std_min:.6f}秒")
                print(f"  最大时间: {std_max:.6f}秒")
            
            if cached_times:
                cache_avg = statistics.mean(cached_times)
                cache_min = min(cached_times)
                cache_max = max(cached_times)
                
                print(f"\n缓存权限获取统计:")
                print(f"  平均时间: {cache_avg:.6f}秒")
                print(f"  最小时间: {cache_min:.6f}秒")
                print(f"  最大时间: {cache_max:.6f}秒")
            
            # 如果两者都有数据，计算对比
            if standard_times and cached_times:
                speedup = std_avg / cache_avg if cache_avg > 0 else 0
                print(f"\n性能提升: {speedup:.2f}x")
                
                # 保存结果
                self.results.append({
                    "user_id": user_id,
                    "standard": {
                        "avg": std_avg,
                        "min": std_min,
                        "max": std_max,
                    },
                    "cached": {
                        "avg": cache_avg,
                        "min": cache_min,
                        "max": cache_max,
                    },
                    "speedup": speedup
                })
        
        # 保存结果到文件
        with open(os.path.join(project_root, "perf_test_results.json"), "w") as f:
            json.dump(self.results, f, indent=2)
        
        print("\n测试完成，结果已保存到 perf_test_results.json")

async def main():
    """主函数"""
    tester = PerformanceTest()
    await tester.run_test_suite()

if __name__ == "__main__":
    asyncio.run(main()) 
#!/usr/bin/env python
"""
手动创建用户脚本
"""
import asyncio
import os
import sys
from typing import List, Optional

# 添加当前目录到Python路径，确保可以导入项目模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.session import AsyncSessionLocal
from models.user import User
from models.role import Role, user_role
from core.security import get_password_hash
from sqlalchemy import select, insert
from sqlalchemy.ext.asyncio import AsyncSession

async def create_user(
    db: AsyncSession, 
    username: str, 
    email: str, 
    password: str, 
    is_active: bool = True, 
    is_superuser: bool = False,
    roles: Optional[List[str]] = None
):
    """
    创建一个新用户
    
    Args:
        db: 数据库会话
        username: 用户名
        email: 电子邮箱
        password: 密码（明文，将会被哈希）
        is_active: 是否激活
        is_superuser: 是否超级管理员
        roles: 角色列表
    
    Returns:
        创建的用户对象
    """
    # 检查用户是否已存在
    query = select(User).where(User.username == username)
    result = await db.execute(query)
    user = result.scalar_first()
    
    if user:
        print(f"用户 {username} 已存在")
        return user
    
    # 创建用户
    hashed_password = get_password_hash(password)
    
    # 使用insert语句创建用户
    stmt = insert(User).values(
        username=username,
        email=email,
        hashed_password=hashed_password,
        is_active=is_active,
        is_superuser=is_superuser,
        is_verified=True
    )
    
    result = await db.execute(stmt)
    await db.commit()
    
    # 查询刚刚创建的用户
    query = select(User).where(User.username == username)
    result = await db.execute(query)
    user = result.scalar_one()
    
    # 如果指定了角色，添加角色
    if roles and user:
        # 查询角色ID
        for role_name in roles:
            role_query = select(Role).where(Role.name == role_name)
            role_result = await db.execute(role_query)
            role = role_result.scalar_first()
            
            if role:
                # 添加用户-角色关联
                role_user_stmt = insert(user_role).values(
                    user_id=user.id, 
                    role_id=role.id
                )
                await db.execute(role_user_stmt)
                print(f"为用户 {username} 添加角色 {role_name}")
            else:
                print(f"角色 {role_name} 不存在")
        
        await db.commit()
    
    print(f"用户 {username} 创建成功")
    return user

async def main():
    """
    主函数
    """
    print("开始创建测试用户...")
    
    # 创建用户
    async with AsyncSessionLocal() as db:
        # 创建测试用户yulong
        await create_user(
            db=db,
            username="yulong",
            email="<EMAIL>",
            password="a123456789",
            is_active=True,
            is_superuser=False,
            roles=["user"]  # 分配user角色
        )
        
        # 打印提示
        print(
            "\n测试用户创建完成! 可以使用以下凭据登录:\n"
            "用户名: yulong\n"
            "密码: a123456789\n"
        )
    
    print("脚本执行完成!")

if __name__ == "__main__":
    asyncio.run(main()) 
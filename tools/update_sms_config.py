#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
脚本：动态更新短信配置
该脚本用于在运行时更新数据库中的短信配置，而不依赖环境变量
"""
import asyncio
import logging
import sys
import os
import argparse

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from db.session import AsyncSessionLocal
from models.sms_config import SMSConfig
from sqlalchemy import select

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def list_configs():
    """列出所有短信配置"""
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(SMSConfig).order_by(SMSConfig.is_active.desc(), SMSConfig.config_name))
        configs = result.scalars().all()
        
        if not configs:
            logger.info("数据库中没有短信配置")
            return
        
        print("\n当前短信配置列表:")
        print("="*80)
        print(f"{'ID':<4} {'配置名称':<15} {'提供商':<10} {'签名':<15} {'模板代码':<20} {'状态':<5}")
        print("-"*80)
        
        for config in configs:
            status = "激活" if config.is_active else "禁用"
            print(f"{config.id:<4} {config.config_name:<15} {config.provider:<10} {config.sign_name:<15} {config.template_code:<20} {status:<5}")
        
        print("="*80)

async def activate_config(config_id):
    """激活指定配置并禁用其他配置"""
    async with AsyncSessionLocal() as db:
        # 先将所有配置设为禁用
        await db.execute(
            f"UPDATE sms_configs SET is_active = false"
        )
        
        # 激活指定配置
        config = await db.execute(select(SMSConfig).where(SMSConfig.id == config_id))
        config = config.scalar_one_or_none()
        
        if not config:
            logger.error(f"找不到ID为{config_id}的短信配置")
            return False
        
        config.is_active = True
        db.add(config)
        await db.commit()
        
        logger.info(f"已激活配置 '{config.config_name}'")
        return True

async def update_config(config_id, **kwargs):
    """更新指定配置"""
    async with AsyncSessionLocal() as db:
        config = await db.execute(select(SMSConfig).where(SMSConfig.id == config_id))
        config = config.scalar_one_or_none()
        
        if not config:
            logger.error(f"找不到ID为{config_id}的短信配置")
            return False
        
        # 更新配置参数
        updated = False
        for key, value in kwargs.items():
            if value is not None and hasattr(config, key):
                setattr(config, key, value)
                updated = True
                logger.info(f"已更新 {key} = {value}")
        
        if updated:
            db.add(config)
            await db.commit()
            logger.info(f"配置 '{config.config_name}' 已更新")
        else:
            logger.info("没有参数需要更新")
        
        return True

async def create_config(config_name, provider, access_key, secret_key, sign_name, template_code, 
                       auto_create_user=True, code_expire_minutes=10, code_length=6, cooldown_seconds=60,
                       is_active=False, description=None):
    """创建新配置"""
    async with AsyncSessionLocal() as db:
        # 检查配置名是否已存在
        exists = await db.execute(select(SMSConfig).where(SMSConfig.config_name == config_name))
        if exists.scalar_one_or_none():
            logger.error(f"配置名 '{config_name}' 已存在")
            return False
        
        # 创建新配置
        new_config = SMSConfig(
            config_name=config_name,
            provider=provider,
            access_key=access_key,
            secret_key=secret_key,
            sign_name=sign_name,
            template_code=template_code,
            auto_create_user=auto_create_user,
            code_expire_minutes=code_expire_minutes,
            code_length=code_length,
            cooldown_seconds=cooldown_seconds,
            is_active=is_active,
            description=description or f"{provider}短信服务配置"
        )
        
        db.add(new_config)
        
        # 如果设置为激活状态，则禁用其他配置
        if is_active:
            await db.execute(
                f"UPDATE sms_configs SET is_active = false WHERE config_name != '{config_name}'"
            )
        
        await db.commit()
        logger.info(f"已创建新配置 '{config_name}'")
        return True

async def delete_config(config_id):
    """删除指定配置"""
    async with AsyncSessionLocal() as db:
        config = await db.execute(select(SMSConfig).where(SMSConfig.id == config_id))
        config = config.scalar_one_or_none()
        
        if not config:
            logger.error(f"找不到ID为{config_id}的短信配置")
            return False
        
        # 不允许删除激活的配置
        if config.is_active:
            logger.error(f"无法删除激活状态的配置，请先激活其他配置")
            return False
            
        await db.delete(config)
        await db.commit()
        
        logger.info(f"已删除配置 '{config.config_name}'")
        return True

def main():
    parser = argparse.ArgumentParser(description="短信配置管理工具")
    subparsers = parser.add_subparsers(dest="command", help="子命令")
    
    # 列出配置
    list_parser = subparsers.add_parser("list", help="列出所有短信配置")
    
    # 激活配置
    activate_parser = subparsers.add_parser("activate", help="激活指定配置")
    activate_parser.add_argument("id", type=int, help="配置ID")
    
    # 更新配置
    update_parser = subparsers.add_parser("update", help="更新指定配置")
    update_parser.add_argument("id", type=int, help="配置ID")
    update_parser.add_argument("--provider", help="提供商")
    update_parser.add_argument("--access-key", help="访问密钥ID")
    update_parser.add_argument("--secret-key", help="访问密钥Secret")
    update_parser.add_argument("--sign-name", help="短信签名")
    update_parser.add_argument("--template-code", help="短信模板代码")
    update_parser.add_argument("--auto-create-user", type=bool, help="是否自动创建用户")
    update_parser.add_argument("--code-expire-minutes", type=int, help="验证码有效期(分钟)")
    update_parser.add_argument("--code-length", type=int, help="验证码长度")
    update_parser.add_argument("--cooldown-seconds", type=int, help="冷却时间(秒)")
    update_parser.add_argument("--description", help="配置描述")
    
    # 创建配置
    create_parser = subparsers.add_parser("create", help="创建新配置")
    create_parser.add_argument("--name", required=True, help="配置名称")
    create_parser.add_argument("--provider", required=True, help="提供商")
    create_parser.add_argument("--access-key", required=True, help="访问密钥ID")
    create_parser.add_argument("--secret-key", required=True, help="访问密钥Secret")
    create_parser.add_argument("--sign-name", required=True, help="短信签名")
    create_parser.add_argument("--template-code", required=True, help="短信模板代码")
    create_parser.add_argument("--auto-create-user", type=bool, default=True, help="是否自动创建用户")
    create_parser.add_argument("--code-expire-minutes", type=int, default=10, help="验证码有效期(分钟)")
    create_parser.add_argument("--code-length", type=int, default=6, help="验证码长度")
    create_parser.add_argument("--cooldown-seconds", type=int, default=60, help="冷却时间(秒)")
    create_parser.add_argument("--active", action="store_true", help="是否激活该配置")
    create_parser.add_argument("--description", help="配置描述")
    
    # 删除配置
    delete_parser = subparsers.add_parser("delete", help="删除指定配置")
    delete_parser.add_argument("id", type=int, help="配置ID")
    
    args = parser.parse_args()
    
    if args.command == "list":
        asyncio.run(list_configs())
    elif args.command == "activate":
        asyncio.run(activate_config(args.id))
    elif args.command == "update":
        kwargs = {
            "provider": args.provider,
            "access_key": args.access_key,
            "secret_key": args.secret_key,
            "sign_name": args.sign_name,
            "template_code": args.template_code,
            "auto_create_user": args.auto_create_user,
            "code_expire_minutes": args.code_expire_minutes,
            "code_length": args.code_length,
            "cooldown_seconds": args.cooldown_seconds,
            "description": args.description
        }
        # 移除None值
        kwargs = {k: v for k, v in kwargs.items() if v is not None}
        asyncio.run(update_config(args.id, **kwargs))
    elif args.command == "create":
        asyncio.run(create_config(
            args.name, args.provider, args.access_key, args.secret_key, 
            args.sign_name, args.template_code, args.auto_create_user,
            args.code_expire_minutes, args.code_length, args.cooldown_seconds,
            args.active, args.description
        ))
    elif args.command == "delete":
        asyncio.run(delete_config(args.id))
    else:
        parser.print_help()

if __name__ == "__main__":
    main() 
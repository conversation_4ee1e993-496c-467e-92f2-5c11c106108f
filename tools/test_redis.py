#!/usr/bin/env python3
"""
Redis连接测试工具
用于测试Redis连接并验证我们的修复是否有效
"""
import asyncio
import logging
import sys
import os

# 添加项目根目录到模块搜索路径
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("redis_test")

async def test_redis_connection():
    """测试Redis连接"""
    from services.redis_service import RedisService
    
    # 测试获取Redis客户端
    logger.info("正在测试Redis客户端连接...")
    client = await RedisService.get_client()
    
    if client:
        logger.info("✅ Redis客户端连接成功!")
        try:
            # 测试基本操作
            test_key = "test:connection"
            await client.set(test_key, "连接测试成功")
            value = await client.get(test_key)
            logger.info(f"Redis操作测试: 写入并读取键值 - 结果: {value}")
            
            # 清理测试键
            await client.delete(test_key)
            
            # 安全关闭客户端
            await RedisService.close_client(client)
        except Exception as e:
            logger.error(f"Redis操作失败: {str(e)}")
    else:
        logger.error("❌ Redis客户端连接失败!")
        
    # 测试Redis缓存工具
    logger.info("正在测试RedisCache工具...")
    cache = await RedisService.get_cache()
    
    if cache:
        logger.info("✅ Redis缓存工具创建成功!")
        try:
            # 测试缓存操作
            test_data = {"test": True, "message": "缓存测试成功"}
            await cache.set("test:cache", test_data, 60)
            cached_data = await cache.get("test:cache")
            logger.info(f"缓存操作测试: 写入并读取JSON数据 - 结果: {cached_data}")
            
            # 清理测试键
            await cache.delete("test:cache")
            
            # 测试安全关闭（RedisCache包含Redis客户端）
            if hasattr(cache, 'redis') and cache.redis:
                await RedisService.close_client(cache.redis)
        except Exception as e:
            logger.error(f"缓存操作失败: {str(e)}")
    else:
        logger.error("❌ Redis缓存工具创建失败!")

    # 测试安全执行命令
    logger.info("测试安全执行Redis命令...")
    try:
        result = await RedisService.execute_command("ping")
        logger.info(f"PING命令结果: {result}")
    except Exception as e:
        logger.error(f"执行PING命令失败: {str(e)}")

async def main():
    """主函数"""
    logger.info("=== Redis连接测试工具 ===")
    
    await test_redis_connection()
    
    logger.info("测试完成!")

if __name__ == "__main__":
    asyncio.run(main()) 
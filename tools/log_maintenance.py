#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志维护工具

提供以下功能：
1. 日志轮转 - 根据大小或时间自动轮转日志文件
2. 日志清理 - 删除超过保留期限的旧日志文件
3. 日志归档 - 将旧日志压缩归档以节省空间
4. 日志分析 - 分析日志大小和增长趋势
"""

import os
import re
import shutil
import gzip
import logging
import argparse
from datetime import datetime, timedelta
import time
import glob
from pathlib import Path
import json
from typing import Dict, List, Tuple, Optional

from core.config import settings
from utils.logger import init_logging, app_logger

# 设置日志
def setup_logging():
    """设置日志"""
    init_logging()
    logger = app_logger
    logger.info("日志维护工具已启动")
    return logger

# 获取所有日志文件
def get_log_files(log_dir: Optional[str] = None) -> List[str]:
    """
    获取所有日志文件
    
    Args:
        log_dir: 日志目录，默认使用配置中的LOG_DIR
        
    Returns:
        日志文件路径列表
    """
    log_dir = log_dir or settings.LOG_DIR
    if not os.path.exists(log_dir):
        return []
        
    # 查找所有日志文件
    log_files = []
    for pattern in ["*.log", "*.log.*", "*.gz"]:
        log_files.extend(glob.glob(os.path.join(log_dir, pattern)))
    
    return log_files

# 分析日志大小
def analyze_log_sizes(log_dir: Optional[str] = None) -> Dict:
    """
    分析日志大小
    
    Args:
        log_dir: 日志目录，默认使用配置中的LOG_DIR
        
    Returns:
        包含日志统计信息的字典
    """
    log_dir = log_dir or settings.LOG_DIR
    if not os.path.exists(log_dir):
        return {"file_count": 0, "total_size_mb": 0}
    
    log_files = get_log_files(log_dir)
    
    # 统计信息
    stats = {
        "file_count": len(log_files),
        "total_size_bytes": 0,
        "largest_file": None,
        "largest_file_size": 0,
        "files_by_type": {},
        "files_by_age": {
            "0-1_days": 0,
            "1-7_days": 0,
            "7-30_days": 0,
            "30+_days": 0
        }
    }
    
    # 当前时间
    now = datetime.now()
    
    # 分析每个文件
    for file_path in log_files:
        # 文件大小
        file_size = os.path.getsize(file_path)
        stats["total_size_bytes"] += file_size
        
        # 最大文件
        if file_size > stats["largest_file_size"]:
            stats["largest_file"] = file_path
            stats["largest_file_size"] = file_size
            
        # 按文件类型分类
        _, ext = os.path.splitext(file_path)
        ext = ext if ext else "无扩展名"
        stats["files_by_type"][ext] = stats["files_by_type"].get(ext, 0) + 1
        
        # 按文件年龄分类
        try:
            mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
            days_old = (now - mtime).days
            
            if days_old <= 1:
                stats["files_by_age"]["0-1_days"] += 1
            elif days_old <= 7:
                stats["files_by_age"]["1-7_days"] += 1
            elif days_old <= 30:
                stats["files_by_age"]["7-30_days"] += 1
            else:
                stats["files_by_age"]["30+_days"] += 1
        except Exception:
            pass
    
    # 转换总大小为MB
    stats["total_size_mb"] = stats["total_size_bytes"] / (1024 * 1024)
    
    return stats

# 清理旧日志
def clean_old_logs(days: int, dry_run: bool = False, log_dir: Optional[str] = None) -> Tuple[int, int]:
    """
    清理旧日志文件
    
    Args:
        days: 保留天数，超过此天数的日志将被删除
        dry_run: 是否只模拟执行而不实际删除
        log_dir: 日志目录，默认使用配置中的LOG_DIR
        
    Returns:
        删除的文件数和释放的空间(字节)
    """
    log_dir = log_dir or settings.LOG_DIR
    if not os.path.exists(log_dir):
        return 0, 0
    
    # 获取截止时间
    cutoff_time = datetime.now() - timedelta(days=days)
    
    # 统计信息
    deleted_count = 0
    freed_space = 0
    
    log_files = get_log_files(log_dir)
    
    for file_path in log_files:
        try:
            # 获取文件修改时间
            mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
            
            # 如果文件修改时间早于截止时间，删除它
            if mtime < cutoff_time:
                file_size = os.path.getsize(file_path)
                
                if dry_run:
                    app_logger.info(f"将删除: {file_path} ({file_size / 1024:.1f} KB)")
                else:
                    os.remove(file_path)
                    app_logger.info(f"已删除: {file_path} ({file_size / 1024:.1f} KB)")
                    
                deleted_count += 1
                freed_space += file_size
        except Exception as e:
            app_logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
    
    return deleted_count, freed_space

# 归档旧日志
def archive_logs(days: int, dry_run: bool = False, log_dir: Optional[str] = None) -> int:
    """
    归档旧日志文件
    
    Args:
        days: 归档天数，超过此天数且未压缩的日志将被归档
        dry_run: 是否只模拟执行而不实际归档
        log_dir: 日志目录，默认使用配置中的LOG_DIR
        
    Returns:
        归档的文件数
    """
    log_dir = log_dir or settings.LOG_DIR
    if not os.path.exists(log_dir):
        return 0
    
    # 创建归档目录
    archive_dir = os.path.join(log_dir, "archive")
    if not os.path.exists(archive_dir) and not dry_run:
        os.makedirs(archive_dir)
    
    # 获取截止时间
    cutoff_time = datetime.now() - timedelta(days=days)
    
    # 统计信息
    archived_count = 0
    
    # 查找所有需要归档的日志文件（未压缩且超过天数）
    for file_path in glob.glob(os.path.join(log_dir, "*.log*")):
        # 跳过已压缩的文件
        if file_path.endswith(".gz"):
            continue
            
        try:
            # 获取文件修改时间
            mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
            
            # 如果文件修改时间早于截止时间，归档它
            if mtime < cutoff_time:
                # 生成归档文件路径
                base_name = os.path.basename(file_path)
                archive_path = os.path.join(archive_dir, f"{base_name}_{mtime.strftime('%Y%m%d')}.gz")
                
                if dry_run:
                    app_logger.info(f"将归档: {file_path} -> {archive_path}")
                else:
                    # 压缩文件
                    with open(file_path, 'rb') as f_in, gzip.open(archive_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                    
                    # 验证压缩文件
                    if os.path.exists(archive_path) and os.path.getsize(archive_path) > 0:
                        # 删除原文件
                        os.remove(file_path)
                        app_logger.info(f"已归档: {file_path} -> {archive_path}")
                    else:
                        app_logger.error(f"归档失败: {file_path}")
                        continue
                    
                archived_count += 1
        except Exception as e:
            app_logger.error(f"归档文件 {file_path} 时出错: {str(e)}")
    
    return archived_count

# 手动轮转日志
def log_rotate_manually(log_dir: Optional[str] = None):
    """
    手动轮转当前日志文件
    
    Args:
        log_dir: 日志目录，默认使用配置中的LOG_DIR
    """
    log_dir = log_dir or settings.LOG_DIR
    if not os.path.exists(log_dir):
        app_logger.error(f"日志目录不存在: {log_dir}")
        return
    
    # 定义需要轮转的日志文件
    log_files = [
        os.path.join(log_dir, "app.log"),
        os.path.join(log_dir, "access.log"),
        os.path.join(log_dir, "error.log")
    ]
    
    for log_file in log_files:
        if not os.path.exists(log_file):
            continue
            
        # 检查文件大小，如果太小（小于10KB）则不轮转
        if os.path.getsize(log_file) < 10 * 1024:
            app_logger.info(f"跳过轮转，文件太小: {log_file}")
            continue
            
        # 生成轮转后的文件名
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        rotated_file = f"{log_file}.{timestamp}"
        
        try:
            # 重命名当前日志文件
            shutil.copy2(log_file, rotated_file)
            
            # 清空原文件
            with open(log_file, 'w') as f:
                f.write(f"# 日志已轮转到 {os.path.basename(rotated_file)} - {datetime.now()}\n")
                
            app_logger.info(f"已轮转: {log_file} -> {rotated_file}")
            
            # 压缩轮转后的文件
            try:
                with open(rotated_file, 'rb') as f_in, gzip.open(f"{rotated_file}.gz", 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
                    
                # 如果压缩成功，删除未压缩的轮转文件
                if os.path.exists(f"{rotated_file}.gz"):
                    os.remove(rotated_file)
                    app_logger.info(f"已压缩: {rotated_file} -> {rotated_file}.gz")
            except Exception as e:
                app_logger.error(f"压缩轮转文件时出错: {str(e)}")
        except Exception as e:
            app_logger.error(f"轮转日志 {log_file} 时出错: {str(e)}")

# 生成日志报告
def generate_log_report(log_dir: Optional[str] = None) -> Dict:
    """
    生成日志报告
    
    Args:
        log_dir: 日志目录，默认使用配置中的LOG_DIR
        
    Returns:
        包含报告信息的字典
    """
    log_dir = log_dir or settings.LOG_DIR
    if not os.path.exists(log_dir):
        return {"error": f"日志目录不存在: {log_dir}"}
    
    # 分析日志大小
    size_stats = analyze_log_sizes(log_dir)
    
    # 日志文件列表
    log_files = []
    for file_path in get_log_files(log_dir):
        try:
            file_info = {
                "path": file_path,
                "size_bytes": os.path.getsize(file_path),
                "size_mb": os.path.getsize(file_path) / (1024 * 1024),
                "mtime": datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat(),
                "days_old": (datetime.now() - datetime.fromtimestamp(os.path.getmtime(file_path))).days
            }
            log_files.append(file_info)
        except Exception:
            continue
    
    # 按大小排序
    log_files.sort(key=lambda x: x["size_bytes"], reverse=True)
    
    # 生成报告
    report = {
        "generated_at": datetime.now().isoformat(),
        "log_dir": log_dir,
        "overview": {
            "file_count": size_stats["file_count"],
            "total_size_mb": size_stats["total_size_mb"],
            "largest_file": os.path.basename(size_stats["largest_file"]) if size_stats.get("largest_file") else None,
            "largest_file_size_mb": size_stats["largest_file_size"] / (1024 * 1024) if size_stats.get("largest_file_size") else 0,
        },
        "statistics": {
            "by_type": size_stats.get("files_by_type", {}),
            "by_age": size_stats.get("files_by_age", {})
        },
        "largest_files": log_files[:10],  # 最大的10个文件
        "config": {
            "log_max_size_mb": settings.LOG_MAX_SIZE_MB,
            "log_backup_count": settings.LOG_BACKUP_COUNT
        },
        "recommendations": []
    }
    
    # 生成建议
    if size_stats["total_size_mb"] > 1000:  # 如果日志总大小超过1GB
        report["recommendations"].append("日志总大小较大，建议增加定期清理任务频率")
        
    if size_stats["files_by_age"].get("30+_days", 0) > 0:
        report["recommendations"].append("存在超过30天的旧日志文件，建议清理")
        
    if size_stats["file_count"] > 100:
        report["recommendations"].append("日志文件数量较多，建议归档或清理旧文件")
        
    return report

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="日志维护工具")
    parser.add_argument(
        "--clean", 
        action="store_true", 
        help="清理过期日志文件"
    )
    parser.add_argument(
        "--archive", 
        action="store_true", 
        help="归档旧日志文件"
    )
    parser.add_argument(
        "--rotate", 
        action="store_true", 
        help="手动轮转当前日志文件"
    )
    parser.add_argument(
        "--analyze", 
        action="store_true", 
        help="分析日志大小"
    )
    parser.add_argument(
        "--report",
        action="store_true",
        help="生成详细日志报告"
    )
    parser.add_argument(
        "--days", 
        type=int, 
        default=settings.LOG_BACKUP_COUNT,
        help=f"保留天数，默认为{settings.LOG_BACKUP_COUNT}天"
    )
    parser.add_argument(
        "--dry-run", 
        action="store_true", 
        help="模拟执行，不实际删除文件"
    )
    parser.add_argument(
        "--log-dir",
        type=str,
        default=None,
        help="日志目录，默认使用配置中的LOG_DIR"
    )
    parser.add_argument(
        "--all", 
        action="store_true", 
        help="执行所有维护操作"
    )
    
    args = parser.parse_args()
    
    # 如果没有指定任何操作，显示帮助信息
    if not (args.clean or args.archive or args.rotate or args.analyze or args.report or args.all):
        parser.print_help()
        return
    
    # 设置日志
    logger = setup_logging()
    logger.info(f"=== 日志维护任务开始: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===")
    
    # 分析日志
    if args.analyze or args.all:
        logger.info("分析日志大小...")
        stats = analyze_log_sizes(args.log_dir)
        logger.info(f"当前日志文件数: {stats['file_count']}")
        logger.info(f"日志总大小: {stats['total_size_mb']:.2f} MB")
        if stats.get('largest_file'):
            logger.info(f"最大日志文件: {os.path.basename(stats['largest_file'])} ({stats['largest_file_size'] / (1024*1024):.2f} MB)")
    
    # 生成报告
    if args.report or args.all:
        logger.info("生成日志报告...")
        report = generate_log_report(args.log_dir)
        
        # 保存报告到文件
        report_file = os.path.join(args.log_dir or settings.LOG_DIR, f"log_report_{datetime.now().strftime('%Y%m%d')}.json")
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
            
        logger.info(f"已生成日志报告: {report_file}")
        
        # 输出部分报告信息
        logger.info(f"报告摘要:")
        logger.info(f"  - 日志文件总数: {report['overview']['file_count']}")
        logger.info(f"  - 日志总大小: {report['overview']['total_size_mb']:.2f} MB")
        
        if report['recommendations']:
            logger.info("建议:")
            for rec in report['recommendations']:
                logger.info(f"  - {rec}")
    
    # 手动轮转
    if args.rotate or args.all:
        logger.info("执行手动日志轮转...")
        log_rotate_manually(args.log_dir)
    
    # 归档旧日志
    if args.archive or args.all:
        logger.info(f"归档超过 {args.days} 天的旧日志文件...")
        if args.dry_run:
            logger.info("模拟模式: 不会实际执行归档操作")
            
        archived_count = archive_logs(args.days, args.dry_run, args.log_dir)
        logger.info(f"已归档 {archived_count} 个日志文件")
    
    # 清理过期日志
    if args.clean or args.all:
        logger.info(f"清理超过 {args.days} 天的旧日志文件...")
        if args.dry_run:
            logger.info("模拟模式: 不会实际删除文件")
            
        deleted_count, freed_space = clean_old_logs(args.days, args.dry_run, args.log_dir)
        logger.info(f"已删除 {deleted_count} 个日志文件, 释放了 {freed_space / (1024*1024):.2f} MB 空间")
    
    logger.info(f"=== 日志维护任务完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===")

if __name__ == "__main__":
    main()
# 文件上传与访问方案说明

## 问题描述

在测试系统的文件上传和下载功能时，我们发现以下问题：

1. **匿名上传功能正常工作**，可以通过 `/api/v1/files/public-upload` 端点成功上传文件
2. **API下载端点需要认证**，未登录用户不能直接通过 `/api/v1/files/{id}/download` 访问文件
3. **文件URL路径不匹配**，上传后返回的URL格式为 `/storage/哈希前两位/哈希后两位/文件ID_文件名`
4. **直接访问返回的URL路径失败**，返回404错误

## 根本原因

通过分析代码和实验，发现原因是：

1. 文件实际存储在服务器的 `storage/files` 目录下
2. 服务器的FastAPI应用将 `storage` 目录挂载为静态文件目录
3. 文件上传后返回的URL路径缺少了 `files` 目录部分

核心代码位于 `main.py` 中：

```python
# 文件存储服务
if os.path.exists(storage_service.base_path):
    app.mount("/storage", StaticFiles(directory=storage_service.base_path), name="storage")
```

而 `storage_service.base_path` 设置为 `storage`，内部文件储存在 `storage/files` 目录中。

## 解决方案

要正确访问上传的文件，应使用以下路径模式：

```
http://服务器地址/storage/files/哈希前两位/哈希后两位/文件ID_文件名
```

例如，如果上传后获得的URL是：
```
/storage/4b/c3/09ebe768-57c8-4217-bec8-c00830883e44_public_test.bin
```

正确的完整访问URL应为：
```
http://localhost:8000/storage/files/4b/c3/09ebe768-57c8-4217-bec8-c00830883e44_public_test.bin
```

## 建议修复

有以下几种修复方案可选：

### 方案1：修改文件存储服务返回的URL

修改 `services/file_storage.py` 中的 `save_file` 方法，确保返回的 `file_url` 包含正确的路径：

```python
# 创建文件访问URL（修改前）
file_url = f"/storage/{relative_path}"

# 修改后
file_url = f"/storage/files/{relative_path}"
```

### 方案2：修改静态文件挂载配置

修改 `main.py` 中的静态文件挂载，直接挂载到文件实际所在的目录：

```python
# 修改前
app.mount("/storage", StaticFiles(directory=storage_service.base_path), name="storage")

# 修改后
files_path = os.path.join(storage_service.base_path, "files")
if os.path.exists(files_path):
    app.mount("/storage", StaticFiles(directory=files_path), name="storage")
```

## 临时解决方案

对于客户端应用，可以在从API获取到文件URL后，自动添加 `/files` 部分：

```javascript
// 假设从API获取的文件URL是：/storage/4b/c3/09ebe768-57c8-4217-bec8-c00830883e44_public_test.bin
const apiFileUrl = file.file_url;
const parts = apiFileUrl.split('/');
const correctUrl = `/storage/files/${parts.slice(2).join('/')}`;
```

## 测试验证

我们编写了 `public_file_test.py` 脚本来测试匿名上传和访问功能。测试结果显示，使用正确路径可以成功访问上传的文件。

---

此问题不影响文件内容的正确存储，只是影响客户端如何访问这些文件。按照上述建议修复后，客户端将能够直接使用API返回的URL访问文件，无需额外处理。 
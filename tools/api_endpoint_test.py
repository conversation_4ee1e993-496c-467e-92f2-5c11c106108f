#!/usr/bin/env python3
"""
API端点测试工具 - 用于测试API接口的基本功能
"""
import requests
import json
import argparse
import sys
import os
from datetime import datetime

# 默认配置
DEFAULT_BASE_URL = "http://localhost:8000"
DEFAULT_API_PATH = "/api/v1"
DEFAULT_USERNAME = "admin"
DEFAULT_PASSWORD = "admin123"

def login(base_url, api_path, username, password):
    """尝试登录并获取令牌"""
    print("===== 登录测试 =====")
    
    # 尝试不同的登录端点
    endpoints = [
        "/auth-settings/jwt/login",
        "/auth-settings/flexible-login",
        "/auth/jwt/login",
        "/auth/flexible-login"
    ]
    
    for endpoint in endpoints:
        login_url = f"{base_url}{api_path}{endpoint}"
        print(f"\n尝试登录端点: {login_url}")
        
        # 确定内容类型
        is_json = "flexible" in endpoint
        headers = {
            "Content-Type": "application/json" if is_json else "application/x-www-form-urlencoded"
        }
        
        # 准备请求数据
        data = json.dumps({"username": username, "password": password}) if is_json else {
            "username": username,
            "password": password
        }
        
        try:
            response = requests.post(login_url, data=data, headers=headers)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"响应: {json.dumps(result, indent=2)}")
                    
                    # 提取令牌
                    if "access_token" in result:
                        token = result["access_token"]
                        print(f"找到令牌: {token[:15]}...")
                        return token
                    elif "data" in result and "access_token" in result["data"]:
                        token = result["data"]["access_token"]
                        print(f"找到令牌(在data中): {token[:15]}...")
                        return token
                except:
                    print(f"响应不是JSON格式: {response.text}")
            else:
                print(f"登录失败: {response.text}")
        except Exception as e:
            print(f"请求异常: {str(e)}")
    
    print("\n所有登录尝试失败")
    return None

def test_endpoint(base_url, api_path, endpoint, token, method="GET", data=None, files=None):
    """测试指定的API端点"""
    url = f"{base_url}{api_path}{endpoint}"
    print(f"\n===== 测试端点: {url} =====")
    print(f"方法: {method}")
    
    # 准备认证头
    if token:
        headers = {"Authorization": f"Bearer {token}"}
        print(f"使用令牌: {token[:15]}...")
    else:
        headers = {}
        print("未使用认证令牌")
    
    # 执行请求
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data, files=files)
        elif method.upper() == "PUT":
            response = requests.put(url, headers=headers, json=data)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            print(f"不支持的HTTP方法: {method}")
            return
        
        # 输出结果
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        try:
            result = response.json()
            print(f"响应体: {json.dumps(result, indent=2)}")
        except:
            print(f"响应体: {response.text[:200]}...")
            
        return response
    except Exception as e:
        print(f"请求异常: {str(e)}")
        return None

def test_auth_endpoints(base_url, api_path, token):
    """测试常见的认证相关端点"""
    print("\n===== 测试认证相关端点 =====")
    
    endpoints = [
        "/users/me",  # 当前用户信息
        "/health",    # 健康检查
        "/system/info"  # 系统信息
    ]
    
    for endpoint in endpoints:
        test_endpoint(base_url, api_path, endpoint, token)

def test_file_endpoints(base_url, api_path, token):
    """测试文件相关端点"""
    print("\n===== 测试文件相关端点 =====")
    
    # 测试文件列表
    print("\n1. 测试文件列表接口")
    test_endpoint(base_url, api_path, "/files/", token)
    
    # 测试文件上传
    print("\n2. 测试文件上传接口")
    test_file = "test_api_endpoint.txt"
    with open(test_file, "w") as f:
        f.write(f"API端点测试文件 - {datetime.now().isoformat()}")
    
    try:
        with open(test_file, "rb") as f:
            files = {"file": (test_file, f)}
            data = {"is_public": "true", "category": "测试"}
            
            response = test_endpoint(
                base_url, api_path, "/files/upload", 
                token, method="POST", 
                data=None,  # data参数会被转换为JSON，这不是我们想要的
                files=files
            )
            
            # 如果上传失败，尝试使用curl
            if not response or response.status_code not in (200, 201):
                print("\n上传失败。尝试生成curl命令进行测试:")
                curl_cmd = (
                    f'curl -v -X POST "{base_url}{api_path}/files/upload" '
                    f'-H "Authorization: Bearer {token}" '
                    f'-F "file=@{test_file}" -F "is_public=true" -F "category=测试"'
                )
                print(curl_cmd)
                
                # 在某些平台上可以直接执行命令
                try_execute = input("\n是否尝试直接执行curl命令? (y/n): ")
                if try_execute.lower() == 'y':
                    print("\n执行curl命令:")
                    os.system(curl_cmd)
    finally:
        if os.path.exists(test_file):
            os.remove(test_file)

def check_api_status(base_url):
    """检查API服务器状态"""
    print("===== 检查API服务器状态 =====")
    
    try:
        # 尝试访问健康检查端点
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print(f"服务器状态: 在线 (HTTP 200)")
            try:
                result = response.json()
                print(f"健康检查响应: {json.dumps(result, indent=2)}")
                return True
            except:
                print(f"健康检查响应: {response.text}")
                return True
        else:
            print(f"服务器状态: 异常 (HTTP {response.status_code})")
            print(f"响应: {response.text}")
            return False
    except Exception as e:
        print(f"服务器状态: 离线或无法连接 ({str(e)})")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="API端点测试工具")
    parser.add_argument("--url", default=DEFAULT_BASE_URL, help="API服务器基础URL")
    parser.add_argument("--path", default=DEFAULT_API_PATH, help="API路径前缀")
    parser.add_argument("--username", default=DEFAULT_USERNAME, help="登录用户名")
    parser.add_argument("--password", default=DEFAULT_PASSWORD, help="登录密码")
    parser.add_argument("--endpoint", help="要测试的特定端点")
    parser.add_argument("--method", default="GET", help="HTTP方法(GET, POST, PUT, DELETE)")
    parser.add_argument("--all", action="store_true", help="测试所有常用端点")
    parser.add_argument("--file", action="store_true", help="测试文件相关端点")
    parser.add_argument("--auth", action="store_true", help="测试认证相关端点")
    parser.add_argument("--no-login", action="store_true", help="不执行登录测试")
    
    args = parser.parse_args()
    
    print(f"API测试工具 - {datetime.now().isoformat()}")
    print(f"基础URL: {args.url}")
    print(f"API路径: {args.path}")
    
    # 检查API服务器状态
    if not check_api_status(args.url):
        print("\n服务器可能离线，确认是否继续测试?")
        cont = input("继续? (y/n): ")
        if cont.lower() != 'y':
            sys.exit(1)
    
    # 获取令牌
    token = None
    if not args.no_login:
        token = login(args.url, args.path, args.username, args.password)
        
        if not token and not args.no_login:
            print("\n未能获取认证令牌，确认是否继续测试?")
            cont = input("继续? (y/n): ")
            if cont.lower() != 'y':
                sys.exit(1)
    
    # 测试特定端点
    if args.endpoint:
        test_endpoint(args.url, args.path, args.endpoint, token, method=args.method)
    
    # 测试认证相关端点
    if args.auth or args.all:
        test_auth_endpoints(args.url, args.path, token)
    
    # 测试文件相关端点
    if args.file or args.all:
        test_file_endpoints(args.url, args.path, token)
    
    print("\n===== 测试完成 =====")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(0) 
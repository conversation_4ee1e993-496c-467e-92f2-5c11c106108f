#!/usr/bin/env python3
"""
文件上传下载测试工具

测试文件服务的上传和下载功能
"""

import requests
import tempfile
import os
import json
from datetime import datetime


def test_file_upload_api():
    """测试文件上传API"""
    print("=== 测试文件上传API ===")
    
    base_url = "http://localhost:8000"
    
    # 创建测试文件
    test_content = f"Test file content - {datetime.now().isoformat()}"
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(test_content)
        test_file_path = f.name
    
    try:
        print(f"📄 创建测试文件: {test_file_path}")
        
        # 测试匿名上传（不需要认证）
        upload_url = f"{base_url}/api/v1/files/public-upload"
        
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test.txt', f, 'text/plain')}
            data = {
                'is_public': 'true',
                'description': '测试文件上传',
                'storage_type': 'minio'  # 测试MinIO存储
            }
            
            print(f"🚀 上传文件到: {upload_url}")
            response = requests.post(upload_url, files=files, data=data, timeout=30)
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📊 响应内容: {response.text}")
            
            if response.status_code == 201:
                result = response.json()
                print("✅ 文件上传成功!")
                print(f"📄 文件ID: {result.get('id')}")
                print(f"📄 文件名: {result.get('filename')}")
                print(f"📄 文件URL: {result.get('file_url')}")
                print(f"📄 文件大小: {result.get('size')} 字节")
                
                # 测试文件下载
                file_id = result.get('id')
                if file_id:
                    test_file_download(base_url, file_id, test_content)
                
                return True
            else:
                print(f"❌ 文件上传失败: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ 文件上传测试异常: {str(e)}")
        return False
    finally:
        # 清理测试文件
        try:
            os.unlink(test_file_path)
        except:
            pass


def test_file_download(base_url, file_id, expected_content):
    """测试文件下载"""
    print(f"\n=== 测试文件下载 ===")
    
    try:
        download_url = f"{base_url}/api/v1/files/{file_id}/download"
        print(f"🚀 下载文件: {download_url}")
        
        response = requests.get(download_url, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            print(f"📄 下载内容长度: {len(content)} 字符")
            
            if expected_content in content:
                print("✅ 文件下载成功，内容验证通过!")
                return True
            else:
                print("❌ 文件下载内容验证失败")
                print(f"期望内容: {expected_content}")
                print(f"实际内容: {content}")
                return False
        else:
            print(f"❌ 文件下载失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 文件下载测试异常: {str(e)}")
        return False


def test_file_list_api():
    """测试文件列表API"""
    print(f"\n=== 测试文件列表API ===")
    
    base_url = "http://localhost:8000"
    list_url = f"{base_url}/api/v1/files/"
    
    try:
        print(f"🚀 获取文件列表: {list_url}")
        response = requests.get(list_url, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文件列表获取成功!")
            print(f"📄 文件总数: {result.get('total', 0)}")
            
            files = result.get('items', [])
            if files:
                print("📄 最近的文件:")
                for i, file_info in enumerate(files[:3]):  # 显示前3个文件
                    print(f"   {i+1}. {file_info.get('filename')} ({file_info.get('size')} 字节)")
            
            return True
        elif response.status_code == 401:
            print("⚠️  文件列表需要认证，这是正常的")
            return True
        else:
            print(f"❌ 文件列表获取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 文件列表测试异常: {str(e)}")
        return False


def test_file_service_status():
    """测试文件服务状态API"""
    print(f"\n=== 测试文件服务状态API ===")
    
    base_url = "http://localhost:8000"
    status_url = f"{base_url}/api/v1/files/status"
    
    try:
        print(f"🚀 获取服务状态: {status_url}")
        response = requests.get(status_url, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 服务状态获取成功!")
            print(f"📊 服务状态: {result.get('status')}")
            print(f"📊 数据库状态: {result.get('database', {}).get('status')}")
            
            storage_info = result.get('storage', {})
            if 'default' in storage_info:
                default_storage = storage_info['default']
                print(f"📊 默认存储: {default_storage.get('type')} ({default_storage.get('status')})")
            
            if 'minio' in storage_info:
                minio_info = storage_info['minio']
                print(f"📊 MinIO状态: {minio_info.get('status')}")
                print(f"📊 MinIO端点: {minio_info.get('endpoint')}")
                print(f"📊 MinIO存储桶: {minio_info.get('bucket')}")
            
            errors = result.get('errors', [])
            if errors:
                print("⚠️  发现错误:")
                for error in errors:
                    print(f"   - {error}")
            
            return True
        elif response.status_code == 401:
            print("⚠️  服务状态需要认证，这是正常的")
            return True
        else:
            print(f"❌ 服务状态获取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 服务状态测试异常: {str(e)}")
        return False


def test_health_endpoint():
    """测试健康检查端点"""
    print(f"\n=== 测试健康检查端点 ===")
    
    base_url = "http://localhost:8000"
    health_url = f"{base_url}/health"
    
    try:
        print(f"🚀 健康检查: {health_url}")
        response = requests.get(health_url, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 健康检查成功!")
            print(f"📊 系统状态: {result.get('status')}")
            print(f"📊 系统版本: {result.get('version')}")
            
            db_info = result.get('db', {})
            print(f"📊 数据库状态: {db_info.get('status')}")
            
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
        return False


def main():
    """主函数"""
    print("🧪 文件服务API测试工具")
    print("=" * 50)
    
    # 测试各个功能
    results = {}
    
    results['health'] = test_health_endpoint()
    results['file_status'] = test_file_service_status()
    results['file_upload'] = test_file_upload_api()
    results['file_list'] = test_file_list_api()
    
    # 生成测试报告
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    print(f"🎯 测试通过: {passed}/{total}")
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if passed == total:
        print("\n🎉 所有测试通过！文件服务运行正常。")
    elif passed > 0:
        print(f"\n⚠️  部分测试通过，文件服务可能存在问题。")
    else:
        print(f"\n❌ 所有测试失败，文件服务可能无法正常工作。")
    
    # 保存测试报告
    report = {
        "timestamp": datetime.now().isoformat(),
        "test_results": results,
        "summary": {
            "passed": passed,
            "total": total,
            "success_rate": passed / total if total > 0 else 0
        }
    }
    
    report_file = "file_api_test_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 测试报告已保存到: {report_file}")


if __name__ == "__main__":
    main()

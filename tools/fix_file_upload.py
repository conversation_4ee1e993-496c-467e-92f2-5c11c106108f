#!/usr/bin/env python3
"""
文件上传修复脚本
修复FastAPI的UploadFile对象没有tell方法的问题
"""
import os
import sys
import shutil

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath("."))

def search_for_file_services():
    """查找可能包含文件上传处理代码的服务文件"""
    print("===== 搜索文件服务 =====")
    
    potential_files = []
    base_dirs = ["services", "utils", "api/api_v1/endpoints"]
    
    for base_dir in base_dirs:
        if not os.path.exists(base_dir):
            continue
            
        for root, _, files in os.walk(base_dir):
            for file in files:
                if file.endswith(".py") and ("file" in file.lower() or "storage" in file.lower() or "upload" in file.lower()):
                    file_path = os.path.join(root, file)
                    potential_files.append(file_path)
    
    if potential_files:
        print(f"找到 {len(potential_files)} 个可能相关的文件:")
        for idx, file_path in enumerate(potential_files, 1):
            print(f"{idx}. {file_path}")
    else:
        print("未找到可能相关的文件")
    
    return potential_files

def check_file_for_tell(file_path):
    """检查文件中是否使用了tell方法"""
    try:
        with open(file_path, "r") as f:
            content = f.read()
            
        if ".tell(" in content or "file.tell" in content or "file_content.tell" in content:
            print(f"在文件 {file_path} 中发现可能的tell方法调用")
            return True
        return False
    except Exception as e:
        print(f"检查文件 {file_path} 时出错: {str(e)}")
        return False

def fix_file_upload_service(file_path):
    """修复文件服务中的tell方法问题"""
    try:
        # 创建备份
        backup_file = f"{file_path}.bak"
        if not os.path.exists(backup_file):
            shutil.copy2(file_path, backup_file)
            print(f"已创建备份: {backup_file}")
        
        with open(file_path, "r") as f:
            content = f.read()
        
        # 修改内容
        modified = False
        
        # 方法1: 添加工具函数
        if "async def save_file" in content and ("file_content.tell" in content or ".tell(" in content):
            # 添加一个安全的tell方法处理FastAPI的UploadFile对象
            safe_tell_func = """
    async def _safe_tell(self, file_obj):
        \"\"\"安全获取文件位置，支持FastAPI的UploadFile对象\"\"\"
        try:
            # 对于内置文件对象，直接调用tell方法
            if hasattr(file_obj, 'tell') and callable(file_obj.tell):
                return file_obj.tell()
                
            # 对于FastAPI的UploadFile对象
            if hasattr(file_obj, 'file') and hasattr(file_obj.file, 'tell'):
                return file_obj.file.tell()
                
            # 无法获取位置时返回0
            return 0
        except Exception as e:
            print(f"获取文件位置失败: {str(e)}")
            return 0
"""
            
            # 查找类定义，添加工具函数
            import re
            class_match = re.search(r"class \w+(?:\(.*?\))?:", content)
            if class_match:
                insert_pos = class_match.end()
                content = content[:insert_pos] + safe_tell_func + content[insert_pos:]
                modified = True
            
            # 替换直接调用tell方法的代码
            content = content.replace("file_content.tell()", "await self._safe_tell(file_content)")
            content = content.replace(".tell()", "await self._safe_tell(file_content)")
            modified = True
        
        # 方法2: 更简洁的解决方式 - 如果是特定模式的代码
        if "await file_content.seek(0)" in content and "file_content.tell()" in content:
            # FastAPI的UploadFile.file是一个SpooledTemporaryFile对象，支持seek和read
            content = content.replace(
                "file_content.tell()",
                "(0 if not hasattr(file_content, 'tell') else file_content.tell())"
            )
            modified = True
            
        if "file_content.tell" in content and not modified:
            # 通用修复 - 添加一个条件检查
            content = content.replace(
                "file_content.tell()",
                "(0 if not hasattr(file_content, 'tell') else file_content.tell())"
            )
            modified = True
            
        # 保存修改
        if modified:
            with open(file_path, "w") as f:
                f.write(content)
            print(f"成功修复文件 {file_path}")
            return True
        else:
            print(f"未在文件 {file_path} 中找到需要修复的内容")
            return False
            
    except Exception as e:
        print(f"修复文件 {file_path} 时出错: {str(e)}")
        return False

def fix_file_service():
    """检查并修复所有文件服务"""
    print("===== 修复文件上传服务 =====")
    
    # 1. 先查找LocalFileStorageService类
    local_storage_file = "services/file_storage.py"
    if os.path.exists(local_storage_file):
        print(f"找到本地文件存储服务: {local_storage_file}")
        fix_file_upload_service(local_storage_file)
    
    # 2. 查找MinioFileStorageService类
    minio_storage_file = "services/minio_file_storage.py"
    if os.path.exists(minio_storage_file):
        print(f"找到MinIO文件存储服务: {minio_storage_file}")
        fix_file_upload_service(minio_storage_file)
    
    # 3. 查找其他可能的文件
    potential_files = search_for_file_services()
    
    # 检查并修复所有可能的文件
    fixed_count = 0
    for file_path in potential_files:
        if file_path not in [local_storage_file, minio_storage_file]:
            if check_file_for_tell(file_path):
                if fix_file_upload_service(file_path):
                    fixed_count += 1
    
    if fixed_count > 0:
        print(f"成功修复 {fixed_count} 个文件服务")
    else:
        print("未找到需要修复的文件服务")
    
    return True

def create_upload_file_adapter():
    """创建UploadFile适配器帮助类"""
    adapter_file = "utils/upload_file_adapter.py"
    
    print(f"创建上传文件适配器: {adapter_file}")
    
    with open(adapter_file, "w") as f:
        f.write("""
\"\"\"
上传文件适配器
提供FastAPI的UploadFile类的扩展功能
\"\"\"
from fastapi import UploadFile
import os
import tempfile
import shutil
from typing import BinaryIO, Optional, Union

class UploadFileAdapter:
    \"\"\"
    对FastAPI的UploadFile对象进行扩展，提供更多文件操作方法
    \"\"\"
    
    def __init__(self, upload_file: UploadFile):
        \"\"\"初始化适配器\"\"\"
        self.upload_file = upload_file
        self.temp_file: Optional[BinaryIO] = None
        self.temp_file_path: Optional[str] = None
        self._size: Optional[int] = None
    
    async def to_tempfile(self) -> str:
        \"\"\"
        将上传文件转换为临时文件，返回临时文件路径
        \"\"\"
        if self.temp_file_path:
            return self.temp_file_path
            
        # 创建临时文件
        self.temp_file = tempfile.NamedTemporaryFile(delete=False)
        self.temp_file_path = self.temp_file.name
        
        # 将上传文件内容写入临时文件
        await self.upload_file.seek(0)
        content = await self.upload_file.read()
        self.temp_file.write(content)
        self.temp_file.close()
        
        return self.temp_file_path
    
    async def tell(self) -> int:
        \"\"\"
        获取当前文件位置
        \"\"\"
        if hasattr(self.upload_file, 'tell') and callable(self.upload_file.tell):
            return await self.upload_file.tell()
            
        if hasattr(self.upload_file, 'file') and hasattr(self.upload_file.file, 'tell'):
            return self.upload_file.file.tell()
            
        return 0
    
    async def seek(self, offset: int) -> None:
        \"\"\"
        设置文件指针位置
        \"\"\"
        await self.upload_file.seek(offset)
    
    async def read(self, size: int = -1) -> bytes:
        \"\"\"
        读取文件内容
        \"\"\"
        return await self.upload_file.read(size)
    
    async def get_size(self) -> int:
        \"\"\"
        获取文件大小
        \"\"\"
        if self._size is not None:
            return self._size
            
        # 保存当前位置
        try:
            if hasattr(self.upload_file, 'file') and hasattr(self.upload_file.file, 'tell'):
                current_pos = self.upload_file.file.tell()
            else:
                current_pos = 0
        except:
            current_pos = 0
        
        # 移动到文件末尾
        await self.upload_file.seek(0, 2)  # 2表示从文件末尾计算偏移量
        
        # 获取文件大小
        try:
            if hasattr(self.upload_file, 'file') and hasattr(self.upload_file.file, 'tell'):
                self._size = self.upload_file.file.tell()
            elif self.temp_file_path and os.path.exists(self.temp_file_path):
                self._size = os.path.getsize(self.temp_file_path)
            else:
                # 无法直接获取大小，读取整个文件计算大小
                await self.upload_file.seek(0)
                content = await self.upload_file.read()
                self._size = len(content)
        except:
            self._size = 0
        
        # 恢复原位置
        await self.upload_file.seek(current_pos)
        
        return self._size
    
    def __del__(self):
        \"\"\"清理临时文件\"\"\"
        try:
            if self.temp_file_path and os.path.exists(self.temp_file_path):
                os.unlink(self.temp_file_path)
        except:
            pass

async def adapt_upload_file(upload_file: UploadFile) -> UploadFileAdapter:
    \"\"\"
    创建UploadFile适配器
    \"\"\"
    return UploadFileAdapter(upload_file)
""")
    
    return True

def main():
    """主函数"""
    print("===== 文件上传修复工具 =====")
    
    # 检查并修复文件服务
    fix_file_service()
    
    # 创建UploadFile适配器
    create_upload_file_adapter()
    
    print("\n===== 修复完成 =====")
    print("请重启服务器以应用修复。")

if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
"""
匿名文件上传和访问测试脚本
专门测试匿名上传的文件如何被访问，不依赖认证
"""
import os
import sys
import requests
import json
import logging
import hashlib
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("public-file-test")

# 测试配置
TEST_API_URL = "http://localhost:8000"

def upload_public_file():
    """匿名上传公开访问的文件"""
    # 创建测试内容
    test_content = os.urandom(1024)  # 1KB随机内容
    content_hash = hashlib.sha256(test_content).hexdigest()
    logger.info(f"创建了1KB的随机测试内容，哈希值: {content_hash}")
    
    # 上传文件
    files = {"file": ("public_test.bin", test_content, "application/octet-stream")}
    data = {
        "is_public": "true",
        "description": "测试公开访问的文件"
    }
    
    try:
        logger.info("正在上传公开文件...")
        response = requests.post(
            f"{TEST_API_URL}/api/v1/files/public-upload",
            files=files,
            data=data
        )
        
        if response.status_code != 201:
            logger.error(f"上传失败，状态码: {response.status_code}")
            logger.error(f"错误信息: {response.text}")
            return None, None, None
            
        # 提取上传的文件信息
        file_data = response.json()
        logger.info(f"文件上传成功: {json.dumps(file_data, indent=2)}")
        
        return file_data, content_hash, test_content
    except Exception as e:
        logger.error(f"上传过程中出现异常: {str(e)}")
        return None, None, None

def try_different_access_methods(file_data, original_hash, original_content):
    """尝试不同的方法访问文件"""
    file_id = file_data.get("id")
    file_url = file_data.get("file_url")
    file_name = file_data.get("filename")
    
    logger.info(f"将尝试多种方式获取文件 {file_id}")
    logger.info(f"文件URL: {file_url}")
    
    # 解析文件URL
    # 通常是 /storage/HASH1/HASH2/UUID_filename
    url_parts = file_url.strip('/').split('/')
    hash_part1 = url_parts[1] if len(url_parts) > 1 else ""
    hash_part2 = url_parts[2] if len(url_parts) > 2 else ""
    
    access_methods = [
        # 1. 直接使用API下载端点 (需认证)
        {
            "name": "API下载端点",
            "url": f"{TEST_API_URL}/api/v1/files/{file_id}/download"
        },
        # 2. 使用文件返回的URL (修正为正确路径)
        {
            "name": "文件URL",
            "url": f"{TEST_API_URL}{file_url}" if file_url.startswith("/") else f"{TEST_API_URL}/{file_url}"
        },
        # 3. 修正路径 - 添加files目录
        {
            "name": "修正存储路径",
            "url": f"{TEST_API_URL}/storage/files/{hash_part1}/{hash_part2}/{file_id}_{file_name}"
        },
        # 4. 直接存储路径
        {
            "name": "直接存储路径",
            "url": f"{TEST_API_URL}/storage/files/{file_url.split('/', 1)[1]}" if "/" in file_url else f"{TEST_API_URL}/storage/files/{file_id}_{file_name}"
        }
    ]
    
    success = False
    
    for method in access_methods:
        try:
            logger.info(f"尝试方法: {method['name']} - {method['url']}")
            response = requests.get(method["url"])
            
            if response.status_code == 200:
                content = response.content
                download_hash = hashlib.sha256(content).hexdigest()
                
                logger.info(f"✅ 成功! 状态码: {response.status_code}, 大小: {len(content)} 字节")
                logger.info(f"哈希值: {download_hash}")
                
                # 验证哈希
                if download_hash == original_hash:
                    logger.info("✅ 哈希验证成功: 内容一致")
                    
                    # 直接比较内容
                    if content == original_content:
                        logger.info("✅ 二进制内容完全匹配")
                    else:
                        logger.warning("⚠️ 二进制内容不完全匹配，但哈希相同")
                        
                    success = True
                else:
                    logger.warning("⚠️ 哈希验证失败: 内容不一致")
                    
                # 显示内容类型
                content_type = response.headers.get("Content-Type", "未知")
                logger.info(f"内容类型: {content_type}")
            else:
                logger.error(f"❌ 失败! 状态码: {response.status_code}")
                logger.error(f"错误内容: {response.text[:200]}...")  # 只显示错误内容的前200个字符
        except Exception as e:
            logger.error(f"❌ 请求异常: {str(e)}")
    
    return success

def main():
    """主函数"""
    logger.info("=== 匿名文件上传和访问测试 ===")
    
    # 上传公开文件
    file_data, content_hash, original_content = upload_public_file()
    
    if not file_data:
        logger.error("测试失败: 无法上传文件")
        return False
    
    # 给服务器一点时间处理
    logger.info("等待2秒，确保文件处理完成...")
    time.sleep(2)
    
    # 尝试不同的访问方法
    success = try_different_access_methods(file_data, content_hash, original_content)
    
    if success:
        logger.info("✅ 文件访问测试成功！至少有一种方法可以正确访问文件")
    else:
        logger.error("❌ 文件访问测试失败！无法通过任何方法正确访问文件")
    
    logger.info("=== 测试完成 ===")
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 
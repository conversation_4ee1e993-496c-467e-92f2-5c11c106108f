#!/usr/bin/env python3
"""
认证问题修复脚本

用于解决文件API的401认证错误问题。
主要检查以下几个方面：
1. 缓存令牌问题
2. API客户端认证中间件
3. 文件API权限检查逻辑
"""

import os
import sys
import argparse
import json
from pprint import pprint
import jwt

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath("."))
from core.config import settings

def decode_jwt(token):
    """解码JWT令牌，显示其内容"""
    try:
        decoded = jwt.decode(token, options={"verify_signature": False})
        return decoded
    except Exception as e:
        return {"error": str(e)}

def check_token(token):
    """检查令牌格式和内容"""
    print(f"\n===== 检查令牌 =====")
    
    if not token:
        print("无效的令牌")
        return False
    
    parts = token.split(".")
    if len(parts) != 3:
        print(f"令牌格式错误: 应该有3部分，实际有{len(parts)}部分")
        return False
    
    try:
        header = json.loads(decode_base64(parts[0]))
        payload = json.loads(decode_base64(parts[1]))
        
        print(f"令牌头部: {json.dumps(header, indent=2)}")
        print(f"令牌载荷: {json.dumps(payload, indent=2)}")
        
        # 检查必要字段
        if "alg" not in header:
            print("令牌头部缺少 'alg' 字段")
            return False
        
        if "sub" not in payload:
            print("令牌载荷缺少 'sub' 字段")
            return False
        
        if "exp" not in payload:
            print("令牌载荷缺少 'exp' 字段")
            return False
        
        return True
    except Exception as e:
        print(f"解析令牌时出错: {str(e)}")
        return False

def decode_base64(data):
    """解码Base64URL编码的JWT部分"""
    import base64
    
    # 补全Base64字符串
    padded = data + "=" * (4 - len(data) % 4)
    
    # 替换URL安全字符
    padded = padded.replace("-", "+").replace("_", "/")
    
    # 解码
    return base64.b64decode(padded).decode("utf-8")

def check_redis_connection():
    """检查Redis连接"""
    print(f"\n===== 检查Redis连接 =====")
    
    try:
        # 尝试导入Redis客户端
        import redis
        
        # 检查Redis配置
        redis_url = settings.REDIS_URL
        if not redis_url:
            print("Redis URL 未配置")
            return False
        
        print(f"Redis URL: {redis_url}")
        
        # 尝试连接Redis
        r = redis.from_url(redis_url)
        r.ping()
        
        print("Redis连接成功")
        return True
    except ImportError:
        print("未安装Redis客户端库")
        return False
    except Exception as e:
        print(f"Redis连接失败: {str(e)}")
        return False

def fix_auth_issues():
    """尝试修复认证问题"""
    print(f"\n===== 应用修复 =====")
    
    # 1. 创建一个支持文件API的认证辅助文件
    auth_helper_file = "utils/file_auth_helper.py"
    
    if not os.path.exists(os.path.dirname(auth_helper_file)):
        os.makedirs(os.path.dirname(auth_helper_file), exist_ok=True)
    
    print(f"创建文件API认证辅助文件: {auth_helper_file}")
    
    with open(auth_helper_file, "w") as f:
        f.write("""
\"\"\"
文件API认证辅助模块
提供文件API特定的认证工具函数
\"\"\"
from fastapi import Depends, HTTPException, status, Request, Header
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
import jwt

from models.user import User
from db.session import get_db
from core.config import settings

async def get_file_api_user(
    authorization: Optional[str] = Header(None, description="Bearer令牌"),
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    \"\"\"
    从Authorization头中获取用户信息，专用于文件API
    
    Args:
        authorization: Authorization头内容
        db: 数据库会话
        
    Returns:
        用户对象或None
        
    Raises:
        HTTPException: 认证失败时
    \"\"\"
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证令牌",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    try:
        # 解析Authorization头
        scheme, token = authorization.split()
        if scheme.lower() != "bearer":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="认证格式错误，应为Bearer令牌",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # 解码令牌
        try:
            payload = jwt.decode(
                token, 
                settings.JWT_SECRET, 
                algorithms=[settings.JWT_ALGORITHM]
            )
            user_id = payload.get("sub")
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的令牌内容",
                    headers={"WWW-Authenticate": "Bearer"}
                )
        except jwt.PyJWTError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"无效的令牌: {str(e)}",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # 查询用户
        from sqlalchemy import select
        query = select(User).where(User.id == int(user_id))
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="找不到用户",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户已被禁用",
                headers={"WWW-Authenticate": "Bearer"}
            )
            
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"认证处理错误: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"}
        )
""")
    
    # 2. 修改文件API端点的依赖项
    file_api_file = "api/api_v1/endpoints/files.py"
    
    if not os.path.exists(file_api_file):
        print(f"找不到文件API文件: {file_api_file}")
    else:
        print(f"修改文件API端点: {file_api_file}")
        
        # 备份原文件
        backup_file = f"{file_api_file}.bak"
        if not os.path.exists(backup_file):
            import shutil
            shutil.copy2(file_api_file, backup_file)
            print(f"已创建备份: {backup_file}")
        
        # 读取文件内容
        with open(file_api_file, "r") as f:
            content = f.read()
        
        # 添加导入
        import_line = "from utils.file_auth_helper import get_file_api_user"
        if import_line not in content:
            content = content.replace(
                "from api.deps import get_current_active_user",
                "from api.deps import get_current_active_user\n" + import_line
            )
        
        # 更新依赖项
        content = content.replace(
            "current_user: User = Depends(get_current_active_user),",
            "current_user: User = Depends(get_file_api_user),"
        )
        
        # 保存修改
        with open(file_api_file, "w") as f:
            f.write(content)

    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="认证问题修复工具")
    parser.add_argument("--token", help="用于测试的JWT令牌")
    parser.add_argument("--fix", action="store_true", help="应用修复")
    
    args = parser.parse_args()
    
    print("===== 认证问题修复工具 =====")
    
    if args.token:
        check_token(args.token)
    
    # 检查Redis连接
    check_redis_connection()
    
    # 应用修复
    if args.fix:
        fix_auth_issues()
        print("\n认证问题修复完成。请重启服务器并重新测试文件API。")
    else:
        print("\n要应用修复，请使用 --fix 参数重新运行此脚本。")
    
    print("\n===== 完成 =====")

if __name__ == "__main__":
    main() 
# 每天午夜执行日志清理和归档
0 0 * * * cd /path/to/admin-server/app && python tools/log_maintenance.py --all >> logs/cron_log.log 2>&1

# 每小时检查日志大小，如果超过一定大小则轮转
0 * * * * cd /path/to/admin-server/app && python tools/log_maintenance.py --rotate >> logs/cron_log.log 2>&1

# 每周一清理归档的日志文件
0 1 * * 1 cd /path/to/admin-server/app && find logs/archives -type f -name "*.zip" -mtime +30 -delete

# 在月初执行全面的日志分析
0 2 1 * * cd /path/to/admin-server/app && python tools/log_maintenance.py --analyze >> logs/monthly_stats.log 2>&1

# 使用方法：
# 1. 修改路径为实际项目路径
# 2. 执行 crontab -e 编辑当前用户的crontab
# 3. 粘贴上述配置，保存并退出
# 4. 执行 crontab -l 确认配置已生效 
"""add_storage_type_to_file_table

Revision ID: 31d39ef9bcba
Revises: 5e474292ff11
Create Date: 2025-05-08 13:39:54.198138

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '31d39ef9bcba'
down_revision: Union[str, None] = '5e474292ff11'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 添加storage_type列，默认值为'local'
    op.add_column('files', sa.Column('storage_type', sa.String(20), nullable=True, server_default='local'))
    
    # 更新现有记录的storage_type为'local'
    op.execute("UPDATE files SET storage_type = 'local' WHERE storage_type IS NULL")
    
    # 将列设为非空
    op.alter_column('files', 'storage_type', nullable=False)
    
    # 添加注释
    op.create_check_constraint(
        'check_storage_type',
        'files',
        sa.text("storage_type IN ('local', 'minio')")
    )
    
    # 添加索引以便于按存储类型过滤
    op.create_index(op.f('ix_files_storage_type'), 'files', ['storage_type'], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    # 删除索引
    op.drop_index(op.f('ix_files_storage_type'), table_name='files')
    
    # 删除约束
    op.drop_constraint('check_storage_type', 'files', type_='check')
    
    # 删除列
    op.drop_column('files', 'storage_type')

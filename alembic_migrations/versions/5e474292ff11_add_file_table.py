"""add_file_table

Revision ID: 5e474292ff11
Revises: 7723ca92d046
Create Date: 2025-05-08 12:12:07.777531

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '5e474292ff11'
down_revision: Union[str, None] = '7723ca92d046'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 创建文件表
    op.create_table(
        'files',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('filename', sa.String(255), nullable=False),
        sa.Column('original_filename', sa.String(255), nullable=False),
        sa.Column('storage_path', sa.String(512), nullable=False),
        sa.Column('file_url', sa.String(512), nullable=False),
        sa.Column('content_type', sa.String(128), nullable=True),
        sa.Column('size', sa.Integer, nullable=False, default=0),
        sa.Column('file_hash', sa.String(64), nullable=True),
        
        # 文件分类和组织
        sa.Column('category', sa.String(64), nullable=True),
        sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        
        # 访问控制
        sa.Column('is_public', sa.Boolean, default=False),
        sa.Column('access_key', sa.String(64), nullable=True),
        
        # 用户关联
        sa.Column('owner_id', sa.Integer, sa.ForeignKey('users.id', ondelete='SET NULL'), nullable=True),
        
        # 状态和扩展数据
        sa.Column('status', sa.String(20), default='active'),
        sa.Column('file_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('description', sa.Text, nullable=True),
        
        # 时间信息
        sa.Column('created_at', sa.DateTime, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('expires_at', sa.DateTime, nullable=True),
    )
    
    # 添加索引
    op.create_index(op.f('ix_files_id'), 'files', ['id'], unique=False)
    op.create_index(op.f('ix_files_file_hash'), 'files', ['file_hash'], unique=False)
    op.create_index(op.f('ix_files_category'), 'files', ['category'], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    # 删除索引
    op.drop_index(op.f('ix_files_category'), table_name='files')
    op.drop_index(op.f('ix_files_file_hash'), table_name='files')
    op.drop_index(op.f('ix_files_id'), table_name='files')
    
    # 删除表
    op.drop_table('files')

"""add missing storage_type column to files table

Revision ID: 247202058317
Revises: 3a7c1305ec77
Create Date: 2025-05-08 16:54:37.889821

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '247202058317'
down_revision: Union[str, None] = '3a7c1305ec77'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 检查列是否存在，不存在则添加
    conn = op.get_bind()
    insp = sa.inspect(conn)
    columns = [c['name'] for c in insp.get_columns('files')]
    
    # 如果不存在storage_type列，则添加
    if 'storage_type' not in columns:
        op.add_column('files', 
                      sa.Column('storage_type', 
                                sa.String(20), 
                                nullable=True, 
                                server_default='local', 
                                comment='存储类型: local, minio'))
        # 创建索引
        op.create_index('ix_files_storage_type', 'files', ['storage_type'])


def downgrade() -> None:
    """Downgrade schema."""
    # 删除列（如果需要回滚）
    op.drop_index('ix_files_storage_type', table_name='files')
    op.drop_column('files', 'storage_type')

"""add storage_type column to files table

Revision ID: 3a7c1305ec77
Revises: 31d39ef9bcba
Create Date: 2025-05-08 16:54:18.709968

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '3a7c1305ec77'
down_revision: Union[str, None] = '31d39ef9bcba'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('audit_logs', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_constraint('fk_audit_log_user_id', 'audit_logs', type_='foreignkey')
    op.create_foreign_key(None, 'audit_logs', 'users', ['user_id'], ['id'])
    op.alter_column('files', 'filename',
               existing_type=sa.VARCHAR(length=255),
               comment='文件名',
               existing_nullable=False)
    op.alter_column('files', 'original_filename',
               existing_type=sa.VARCHAR(length=255),
               comment='原始文件名',
               existing_nullable=False)
    op.alter_column('files', 'storage_path',
               existing_type=sa.VARCHAR(length=512),
               comment='存储路径',
               existing_nullable=False)
    op.alter_column('files', 'file_url',
               existing_type=sa.VARCHAR(length=512),
               comment='访问URL',
               existing_nullable=False)
    op.alter_column('files', 'content_type',
               existing_type=sa.VARCHAR(length=128),
               comment='内容类型',
               existing_nullable=True)
    op.alter_column('files', 'size',
               existing_type=sa.INTEGER(),
               comment='文件大小(字节)',
               existing_nullable=False)
    op.alter_column('files', 'file_hash',
               existing_type=sa.VARCHAR(length=64),
               comment='文件哈希值',
               existing_nullable=True)
    op.alter_column('files', 'category',
               existing_type=sa.VARCHAR(length=64),
               comment='文件分类',
               existing_nullable=True)
    op.alter_column('files', 'tags',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='标签列表',
               existing_nullable=True)
    op.alter_column('files', 'is_public',
               existing_type=sa.BOOLEAN(),
               comment='是否公开访问',
               existing_nullable=True)
    op.alter_column('files', 'access_key',
               existing_type=sa.VARCHAR(length=64),
               comment='访问密钥(用于非公开文件)',
               existing_nullable=True)
    op.alter_column('files', 'owner_id',
               existing_type=sa.INTEGER(),
               comment='文件所有者',
               existing_nullable=True)
    op.alter_column('files', 'storage_type',
               existing_type=sa.VARCHAR(length=20),
               nullable=True,
               comment='存储类型: local, minio',
               existing_server_default=sa.text("'local'::character varying"))
    op.alter_column('files', 'status',
               existing_type=sa.VARCHAR(length=20),
               comment='文件状态: active, deleted, archived',
               existing_nullable=True)
    op.alter_column('files', 'file_metadata',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment='额外元数据',
               existing_nullable=True)
    op.alter_column('files', 'description',
               existing_type=sa.TEXT(),
               comment='文件描述',
               existing_nullable=True)
    op.alter_column('files', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='创建时间',
               existing_nullable=True)
    op.alter_column('files', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='更新时间',
               existing_nullable=True)
    op.alter_column('files', 'expires_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='过期时间',
               existing_nullable=True)
    op.drop_index('ix_files_storage_type', table_name='files')
    op.drop_constraint('files_owner_id_fkey', 'files', type_='foreignkey')
    op.create_foreign_key(None, 'files', 'users', ['owner_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'files', type_='foreignkey')
    op.create_foreign_key('files_owner_id_fkey', 'files', 'users', ['owner_id'], ['id'], ondelete='SET NULL')
    op.create_index('ix_files_storage_type', 'files', ['storage_type'], unique=False)
    op.alter_column('files', 'expires_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='过期时间',
               existing_nullable=True)
    op.alter_column('files', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=True)
    op.alter_column('files', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=True)
    op.alter_column('files', 'description',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='文件描述',
               existing_nullable=True)
    op.alter_column('files', 'file_metadata',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='额外元数据',
               existing_nullable=True)
    op.alter_column('files', 'status',
               existing_type=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='文件状态: active, deleted, archived',
               existing_nullable=True)
    op.alter_column('files', 'storage_type',
               existing_type=sa.VARCHAR(length=20),
               nullable=False,
               comment=None,
               existing_comment='存储类型: local, minio',
               existing_server_default=sa.text("'local'::character varying"))
    op.alter_column('files', 'owner_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='文件所有者',
               existing_nullable=True)
    op.alter_column('files', 'access_key',
               existing_type=sa.VARCHAR(length=64),
               comment=None,
               existing_comment='访问密钥(用于非公开文件)',
               existing_nullable=True)
    op.alter_column('files', 'is_public',
               existing_type=sa.BOOLEAN(),
               comment=None,
               existing_comment='是否公开访问',
               existing_nullable=True)
    op.alter_column('files', 'tags',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               comment=None,
               existing_comment='标签列表',
               existing_nullable=True)
    op.alter_column('files', 'category',
               existing_type=sa.VARCHAR(length=64),
               comment=None,
               existing_comment='文件分类',
               existing_nullable=True)
    op.alter_column('files', 'file_hash',
               existing_type=sa.VARCHAR(length=64),
               comment=None,
               existing_comment='文件哈希值',
               existing_nullable=True)
    op.alter_column('files', 'size',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='文件大小(字节)',
               existing_nullable=False)
    op.alter_column('files', 'content_type',
               existing_type=sa.VARCHAR(length=128),
               comment=None,
               existing_comment='内容类型',
               existing_nullable=True)
    op.alter_column('files', 'file_url',
               existing_type=sa.VARCHAR(length=512),
               comment=None,
               existing_comment='访问URL',
               existing_nullable=False)
    op.alter_column('files', 'storage_path',
               existing_type=sa.VARCHAR(length=512),
               comment=None,
               existing_comment='存储路径',
               existing_nullable=False)
    op.alter_column('files', 'original_filename',
               existing_type=sa.VARCHAR(length=255),
               comment=None,
               existing_comment='原始文件名',
               existing_nullable=False)
    op.alter_column('files', 'filename',
               existing_type=sa.VARCHAR(length=255),
               comment=None,
               existing_comment='文件名',
               existing_nullable=False)
    op.drop_constraint(None, 'audit_logs', type_='foreignkey')
    op.create_foreign_key('fk_audit_log_user_id', 'audit_logs', 'users', ['user_id'], ['id'], ondelete='SET NULL')
    op.alter_column('audit_logs', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('now()'))
    # ### end Alembic commands ###

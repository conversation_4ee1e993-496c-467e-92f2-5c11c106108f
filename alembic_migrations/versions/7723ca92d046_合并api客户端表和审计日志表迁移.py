"""合并API客户端表和审计日志表迁移

Revision ID: 7723ca92d046
Revises: a24f5c7e1d8a, 3a5fb81e9762
Create Date: 2025-05-08 11:14:23.873646

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7723ca92d046'
down_revision: Union[str, None] = ('a24f5c7e1d8a', '3a5fb81e9762')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass

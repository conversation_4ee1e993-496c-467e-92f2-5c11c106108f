"""add_oauth_cas_sms_config_models

Revision ID: c53b7d00b6cb
Revises: 
Create Date: 2025-05-07 17:50:05.763775

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'c53b7d00b6cb'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('cas_configs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('config_name', sa.String(length=50), nullable=False, comment='配置名称，默认为default_cas'),
    sa.Column('server_url', sa.String(length=512), nullable=False, comment='CAS服务器URL'),
    sa.Column('service_url', sa.String(length=512), nullable=False, comment='应用回调服务URL'),
    sa.Column('version', sa.Integer(), nullable=False, comment='CAS协议版本 (2或3)'),
    sa.Column('auto_create_user', sa.Boolean(), nullable=True, comment='是否自动创建不存在的用户'),
    sa.Column('default_role', sa.String(length=50), nullable=True, comment='自动创建用户时分配的角色'),
    sa.Column('validate_cert', sa.Boolean(), nullable=True, comment='是否验证CAS服务器的SSL证书'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活此CAS配置'),
    sa.Column('description', sa.String(length=255), nullable=True, comment='描述'),
    sa.Column('created_at', sa.String(length=255), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.String(length=255), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_cas_configs_config_name'), 'cas_configs', ['config_name'], unique=True)
    op.create_index(op.f('ix_cas_configs_id'), 'cas_configs', ['id'], unique=False)
    op.create_table('oauth_provider_configs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('provider_name', sa.String(length=50), nullable=False, comment='提供商名称 (e.g., github, google)'),
    sa.Column('client_id', sa.String(length=255), nullable=False, comment='Client ID'),
    sa.Column('client_secret', sa.String(length=255), nullable=False, comment='Client Secret'),
    sa.Column('authorize_url', sa.String(length=512), nullable=True, comment='授权URL'),
    sa.Column('token_url', sa.String(length=512), nullable=True, comment='获取令牌URL'),
    sa.Column('user_info_url', sa.String(length=512), nullable=True, comment='获取用户信息URL'),
    sa.Column('jwks_uri', sa.String(length=512), nullable=True, comment='JWKS URI (用于OIDC)'),
    sa.Column('scopes', sa.Text(), nullable=True, comment='授权范围 (逗号分隔或JSON列表)'),
    sa.Column('icon_url', sa.String(length=512), nullable=True, comment='提供商图标URL'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('description', sa.Text(), nullable=True, comment='描述'),
    sa.Column('created_at', sa.String(length=255), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.String(length=255), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_oauth_provider_configs_id'), 'oauth_provider_configs', ['id'], unique=False)
    op.create_index(op.f('ix_oauth_provider_configs_provider_name'), 'oauth_provider_configs', ['provider_name'], unique=True)
    op.create_table('sms_configs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('config_name', sa.String(length=50), nullable=False, comment='配置名称，默认为default_sms'),
    sa.Column('provider', sa.String(length=50), nullable=False, comment='短信服务提供商 (e.g., aliyun, tencent)'),
    sa.Column('access_key', sa.String(length=255), nullable=False, comment='Access Key ID'),
    sa.Column('secret_key', sa.String(length=255), nullable=False, comment='Secret Key'),
    sa.Column('sign_name', sa.String(length=100), nullable=True, comment='短信签名'),
    sa.Column('template_code', sa.String(length=100), nullable=True, comment='短信模板代码'),
    sa.Column('app_id', sa.String(length=100), nullable=True, comment='应用ID (例如腾讯云需要)'),
    sa.Column('auto_create_user', sa.Boolean(), nullable=True, comment='是否自动创建不存在的用户'),
    sa.Column('code_expire_minutes', sa.Integer(), nullable=True, comment='验证码有效期（分钟）'),
    sa.Column('code_length', sa.Integer(), nullable=True, comment='验证码长度'),
    sa.Column('cooldown_seconds', sa.Integer(), nullable=True, comment='验证码发送冷却时间（秒）'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活此短信配置'),
    sa.Column('description', sa.String(length=255), nullable=True, comment='描述'),
    sa.Column('created_at', sa.String(length=255), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.String(length=255), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sms_configs_config_name'), 'sms_configs', ['config_name'], unique=True)
    op.create_index(op.f('ix_sms_configs_id'), 'sms_configs', ['id'], unique=False)
    op.drop_index('ix_login_history_id', table_name='login_history')
    op.drop_table('login_history')
    op.drop_index('ix_verification_codes_email', table_name='verification_codes')
    op.drop_index('ix_verification_codes_id', table_name='verification_codes')
    op.drop_index('ix_verification_codes_phone', table_name='verification_codes')
    op.drop_table('verification_codes')
    op.create_index(op.f('ix_oauth_accounts_user_id'), 'oauth_accounts', ['user_id'], unique=False)
    op.alter_column('users', 'email',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=320),
               existing_nullable=False)
    op.alter_column('users', 'hashed_password',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.String(length=1024),
               existing_nullable=False)
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('users', 'is_superuser',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('users', 'is_verified',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'is_verified',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('users', 'is_superuser',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.alter_column('users', 'hashed_password',
               existing_type=sa.String(length=1024),
               type_=sa.VARCHAR(length=255),
               existing_nullable=False)
    op.alter_column('users', 'email',
               existing_type=sa.String(length=320),
               type_=sa.VARCHAR(length=100),
               existing_nullable=False)
    op.drop_index(op.f('ix_oauth_accounts_user_id'), table_name='oauth_accounts')
    op.create_table('verification_codes',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('phone', sa.VARCHAR(length=20), autoincrement=False, nullable=True),
    sa.Column('email', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('code', sa.VARCHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('purpose', sa.VARCHAR(length=20), autoincrement=False, nullable=False, comment='用途，如login, register, reset_password等'),
    sa.Column('is_used', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('expires_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='verification_codes_pkey')
    )
    op.create_index('ix_verification_codes_phone', 'verification_codes', ['phone'], unique=False)
    op.create_index('ix_verification_codes_id', 'verification_codes', ['id'], unique=False)
    op.create_index('ix_verification_codes_email', 'verification_codes', ['email'], unique=False)
    op.create_table('login_history',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('login_method', sa.VARCHAR(length=20), autoincrement=False, nullable=False, comment='登录方式，如password, oauth2, cas, sms等'),
    sa.Column('login_ip', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('user_agent', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('status', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='登录是否成功'),
    sa.Column('message', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='登录失败原因'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='login_history_user_id_fkey', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name='login_history_pkey')
    )
    op.create_index('ix_login_history_id', 'login_history', ['id'], unique=False)
    op.drop_index(op.f('ix_sms_configs_id'), table_name='sms_configs')
    op.drop_index(op.f('ix_sms_configs_config_name'), table_name='sms_configs')
    op.drop_table('sms_configs')
    op.drop_index(op.f('ix_oauth_provider_configs_provider_name'), table_name='oauth_provider_configs')
    op.drop_index(op.f('ix_oauth_provider_configs_id'), table_name='oauth_provider_configs')
    op.drop_table('oauth_provider_configs')
    op.drop_index(op.f('ix_cas_configs_id'), table_name='cas_configs')
    op.drop_index(op.f('ix_cas_configs_config_name'), table_name='cas_configs')
    op.drop_table('cas_configs')
    # ### end Alembic commands ###

"""添加API客户端表

Revision ID: a24f5c7e1d8a
Revises: your_previous_revision_id_here
Create Date: 2023-05-08 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy import inspect

# revision identifiers, used by Alembic.
revision = 'a24f5c7e1d8a'
down_revision = None  # 请替换为实际的上一个版本ID
branch_labels = None
depends_on = None


def table_exists(table_name):
    """检查表是否存在"""
    bind = op.get_bind()
    inspector = inspect(bind)
    tables = inspector.get_table_names()
    return table_name in tables


def index_exists(table_name, index_name):
    """检查索引是否存在"""
    bind = op.get_bind()
    inspector = inspect(bind)
    if not table_exists(table_name):
        return False
    indexes = inspector.get_indexes(table_name)
    for idx in indexes:
        if idx.get('name') == index_name:
            return True
    return False


def upgrade():
    # 检查表是否已存在
    if table_exists('api_clients'):
        # 表已存在，跳过创建
        print("表 api_clients 已存在，跳过创建")
    else:
        # 创建api_clients表
        op.create_table('api_clients',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('name', sa.String(length=100), nullable=False),
            sa.Column('description', sa.Text(), nullable=True),
            sa.Column('client_id', sa.String(length=64), nullable=False),
            sa.Column('client_secret', sa.String(length=128), nullable=False),
            sa.Column('allowed_ips', sa.JSON(), nullable=True),
            sa.Column('scopes', sa.JSON(), nullable=True),
            sa.Column('rate_limit', sa.Integer(), nullable=True, server_default='60'),
            sa.Column('is_active', sa.Boolean(), nullable=True, server_default='true'),
            sa.Column('created_at', sa.DateTime(), nullable=True, server_default=sa.text('now()')),
            sa.Column('updated_at', sa.DateTime(), nullable=True, server_default=sa.text('now()')),
            sa.Column('expires_at', sa.DateTime(), nullable=True),
            sa.Column('created_by_id', sa.Integer(), nullable=True),
            sa.Column('request_count', sa.Integer(), nullable=True, server_default='0'),
            sa.Column('last_used_at', sa.DateTime(), nullable=True),
            sa.ForeignKeyConstraint(['created_by_id'], ['users.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
    
    # 创建索引
    indexes = [
        ('ix_api_clients_client_id', 'api_clients', ['client_id'], True),
        ('ix_api_clients_id', 'api_clients', ['id'], False),
        ('ix_api_clients_name', 'api_clients', ['name'], False)
    ]
    
    for idx_name, table, columns, unique in indexes:
        if not index_exists(table, idx_name):
            op.create_index(op.f(idx_name), table, columns, unique=unique)
        else:
            print(f"索引 {idx_name} 已存在，跳过创建")


def downgrade():
    # 删除索引
    op.drop_index(op.f('ix_api_clients_name'), table_name='api_clients')
    op.drop_index(op.f('ix_api_clients_id'), table_name='api_clients')
    op.drop_index(op.f('ix_api_clients_client_id'), table_name='api_clients')
    
    # 删除表
    op.drop_table('api_clients') 
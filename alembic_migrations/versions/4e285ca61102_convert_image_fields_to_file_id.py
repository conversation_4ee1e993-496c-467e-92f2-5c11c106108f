"""convert_image_fields_to_file_id

Revision ID: 4e285ca61102
Revises: ************
Create Date: 2025-05-08 17:39:15.534624

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4e285ca61102'
down_revision: Union[str, None] = '************'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """升级数据库模式，将图片字段改为文件ID。"""
    
    # 修改用户头像字段
    op.alter_column('users', 'avatar',
                    existing_type=sa.String(length=255),
                    type_=sa.String(length=36),
                    existing_nullable=True,
                    comment='头像文件UUID')
    
    # 修改菜单图标字段
    op.alter_column('menus', 'icon',
                    existing_type=sa.String(length=100),
                    type_=sa.String(length=36),
                    existing_nullable=True,
                    comment='图标文件UUID')
    
    # 修改OAuth提供商图标字段
    op.alter_column('oauth_provider_configs', 'icon_url',
                    new_column_name='icon',
                    existing_type=sa.String(length=512),
                    type_=sa.String(length=36),
                    existing_nullable=True,
                    comment='提供商图标文件UUID')
    
    # 为CAS和SMS配置添加图标字段
    op.add_column('cas_configs',
                 sa.Column('icon', sa.String(length=36), nullable=True, comment='服务图标文件UUID'))
    
    op.add_column('sms_configs',
                 sa.Column('icon', sa.String(length=36), nullable=True, comment='服务图标文件UUID'))


def downgrade() -> None:
    """降级数据库模式，恢复图片字段为URL形式。"""
    
    # 删除SMS配置图标字段
    op.drop_column('sms_configs', 'icon')
    
    # 删除CAS配置图标字段
    op.drop_column('cas_configs', 'icon')
    
    # 恢复OAuth提供商图标字段
    op.alter_column('oauth_provider_configs', 'icon',
                    new_column_name='icon_url',
                    existing_type=sa.String(length=36),
                    type_=sa.String(length=512),
                    existing_nullable=True,
                    comment='提供商图标URL')
    
    # 恢复菜单图标字段
    op.alter_column('menus', 'icon',
                    existing_type=sa.String(length=36),
                    type_=sa.String(length=100),
                    existing_nullable=True)
    
    # 恢复用户头像字段
    op.alter_column('users', 'avatar',
                    existing_type=sa.String(length=36),
                    type_=sa.String(length=255),
                    existing_nullable=True)

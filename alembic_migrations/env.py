import os
import sys
from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

# This line assumes your models are accessible via the `app` module (e.g., app.models)
# Adjust if your Base is defined elsewhere relative to the `app` directory.
# Since env.py is in app/alembic_migrations, we need to add app's parent to sys.path
# so that modules like `app.db.session` or `app.models` can be imported.
# CORRECTED PATH ADJUSTMENT:
# Assuming your project structure is /Users/<USER>/projects/admin-server/app
# and env.py is in /Users/<USER>/projects/admin-server/app/alembic_migrations
# To import 'app.db.session', 'app' itself needs to be in sys.path or its parent.
# If alembic commands are run from app/, then imports like `from db.session import Base` should work.
# If alembic commands are run from admin-server/, then `from app.db.session import Base` is needed.

# For autogenerate support, import your Base and all models here.
# This is a common pattern if Base is defined in one place and models in others.
# Example for your project structure:
# Assuming this env.py is inside app/alembic_migrations
# Add the 'app' directory to sys.path to allow importing modules from it directly
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
APP_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))

# 确保 app 目录优先级更高（先被搜索）
sys.path.insert(0, APP_DIR)     # 先添加 app 目录，用于 from core.config import settings
sys.path.insert(0, PROJECT_ROOT) # 然后添加项目根目录，用于 from app.db.session import Base

# Now you can import from your app's modules
from db.session import Base  # 假设这不直接依赖于 settings
# Import all your models here so Alembic's autogenerate can see them
from models.user import User # Example, add all your models
from models.role import Role, Permission # Example
from models.menu import Menu # Example
from models.config import Config # Example
from models.oauth_provider import OAuthProviderConfig # Added model
from models.cas_config import CASConfig # Added model
from models.sms_config import SMSConfig # Added model
# Add any other models you have...

# This is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata # Use your Base's metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:c
# my_important_option = config.get_main_option("my_important_option")
# ... etc.

# 从环境变量直接读取数据库配置，避免使用 settings
DB_SERVER = os.getenv("POSTGRES_SERVER", "192.168.2.201")
DB_USER = os.getenv("POSTGRES_USER", "postgres")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "xfPCP8FzG4jT7d5Z")
DB_NAME = os.getenv("POSTGRES_DB", "postgres")
DB_PORT = os.getenv("POSTGRES_PORT", "35432")

# 构建数据库 URL
# 注意：这里使用 psycopg2 引擎，与 alembic.ini 中的配置保持一致
DB_URL = f"postgresql+psycopg2://{DB_USER}:{DB_PASSWORD}@{DB_SERVER}:{DB_PORT}/{DB_NAME}"

# 设置 sqlalchemy.url
config.set_main_option('sqlalchemy.url', DB_URL)

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    hereabouts.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()

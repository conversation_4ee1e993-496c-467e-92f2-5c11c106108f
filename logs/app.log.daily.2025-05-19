2025-05-19 09:50:32,437 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-19 09:50:32,438 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-19 09:50:32,439 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-19 09:50:32,810 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 09:50:32,823 - INFO - app - [main.py:457] - 动态配置加载完成
2025-05-19 09:50:32,955 - INFO - app - [main.py:473] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 09:50:33,825 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-19 09:50:34,466 - INFO - app - [main.py:495] - 数据库连接状态: healthy
2025-05-19 09:50:34,467 - INFO - app - [main.py:499] - 应用启动完成: 管理后台服务
2025-05-19 09:52:32,762 - INFO - app - [main.py:507] - 应用关闭，执行清理操作...
2025-05-19 09:52:32,763 - INFO - app - [main.py:513] - 已停止认证配置刷新任务
2025-05-19 09:52:32,763 - INFO - app - [main.py:528] - 应用已完全关闭
2025-05-19 09:52:36,799 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 09:52:36,800 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 09:52:36,800 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 09:52:37,217 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 09:52:37,226 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 09:52:37,305 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 09:52:38,263 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 09:52:38,915 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 09:52:38,917 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 09:52:47,919 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 09:52:47,935 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 09:52:47,935 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 09:52:52,698 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 09:52:52,698 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 09:52:52,699 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 09:52:53,031 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 09:52:53,034 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 09:52:53,150 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 09:52:54,032 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 09:52:54,689 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 09:52:54,690 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 09:52:56,507 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 09:52:56,507 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 09:52:56,508 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 09:53:00,516 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 09:53:00,516 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 09:53:00,517 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 09:53:01,015 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 09:53:01,020 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 09:53:01,142 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 09:53:02,126 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 09:53:02,768 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 09:53:02,769 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:00:22,277 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 10:00:22,278 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 10:00:22,279 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 10:00:26,501 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:00:26,501 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:00:26,502 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:00:26,925 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:00:26,945 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:00:26,954 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:00:28,036 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:00:28,705 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:00:28,706 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:01:15,191 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 10:01:15,192 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 10:01:15,193 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 10:01:19,499 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:01:19,499 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:01:19,500 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:01:19,855 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:01:19,858 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:01:19,940 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:01:20,965 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:01:21,625 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:01:21,626 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:03:48,748 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 10:03:48,749 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 10:03:48,749 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 10:04:53,141 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:04:53,141 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:04:53,142 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:04:53,671 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:04:53,704 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:04:53,715 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:04:54,795 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:04:55,466 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:04:55,466 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:19:18,741 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 10:19:18,743 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 10:19:18,744 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 10:19:22,964 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:19:22,964 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:19:22,965 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:19:23,261 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:19:23,264 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:19:23,337 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:19:24,163 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:19:24,798 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:19:24,798 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:22:30,627 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 10:22:30,630 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 10:22:30,631 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 10:22:35,179 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:22:35,180 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:22:35,181 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:22:35,524 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:22:35,539 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:22:35,543 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:22:36,375 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:22:37,028 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:22:37,028 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:22:44,222 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 10:22:44,224 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 10:22:44,224 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 10:22:48,201 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:22:48,201 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:22:48,202 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:22:48,492 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:22:48,497 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:22:48,542 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:22:49,513 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:22:50,162 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:22:50,162 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:26:45,429 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 10:26:45,430 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 10:26:45,431 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 10:26:49,553 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:26:49,554 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:26:49,554 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:26:49,900 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:26:49,908 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:26:50,019 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:26:50,928 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:26:51,580 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:26:51,581 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:27:20,723 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 10:27:20,724 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 10:27:20,725 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 10:27:25,156 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:27:25,156 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:27:25,157 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:27:25,510 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:27:25,518 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:27:25,566 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:27:26,617 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:27:27,269 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:27:27,270 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:28:38,227 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 10:28:38,230 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 10:28:38,231 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 10:28:42,475 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:28:42,475 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:28:42,476 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:28:42,781 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:28:42,793 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:28:42,802 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:28:43,557 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:28:44,198 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:28:44,198 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:29:34,267 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 10:29:34,269 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 10:29:34,270 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 10:29:38,346 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:29:38,346 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:29:38,347 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:29:38,705 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:29:38,733 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:29:38,790 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:29:39,775 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:29:40,428 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:29:40,429 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:33:58,458 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:33:58,460 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:33:58,460 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:33:58,764 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:33:58,772 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:33:58,821 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:33:59,654 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:34:00,303 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:34:00,303 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:41:02,963 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:41:02,963 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:41:02,964 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:41:03,319 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:41:03,366 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:41:03,370 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:41:04,149 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:41:04,823 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:41:04,826 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:43:37,509 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:43:37,510 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:43:37,511 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:43:37,866 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:43:37,878 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:43:37,885 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:43:38,924 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:43:39,571 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:43:39,571 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:49:12,680 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 10:49:12,681 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 10:49:12,682 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 10:49:16,761 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:49:16,761 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:49:16,762 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:49:17,081 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:49:17,099 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:49:17,291 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:49:19,137 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:49:19,796 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:49:19,797 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:49:30,210 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 10:49:30,211 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 10:49:30,212 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 10:49:34,097 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:49:34,097 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:49:34,098 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:49:34,433 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:49:34,436 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:49:34,581 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:49:36,081 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:49:36,747 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:49:36,748 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 10:53:08,480 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 10:53:08,481 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 10:53:08,482 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 10:53:08,788 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 10:53:08,795 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 10:53:08,898 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 10:53:10,095 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 10:53:10,741 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 10:53:10,741 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 14:02:32,595 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 14:02:32,596 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 14:02:32,597 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 14:02:36,902 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 14:02:36,902 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 14:02:36,903 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 14:02:37,250 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 14:02:37,256 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 14:02:37,294 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 14:02:37,975 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 14:02:38,622 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 14:02:38,622 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 14:02:55,460 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 14:02:55,460 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 14:02:55,461 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 14:02:59,446 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 14:02:59,447 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 14:02:59,447 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 14:02:59,757 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 14:02:59,762 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 14:02:59,791 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 14:03:00,541 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 14:03:01,189 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 14:03:01,189 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 14:21:53,245 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 14:21:53,247 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 14:21:53,247 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 14:21:57,539 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 14:21:57,540 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 14:21:57,540 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 14:21:57,924 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 14:21:57,932 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 14:21:57,973 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 14:21:58,860 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 14:21:59,519 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 14:21:59,520 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 16:04:58,679 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 16:04:58,680 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 16:04:58,680 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 16:04:59,108 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 16:04:59,129 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 16:04:59,142 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 16:05:00,597 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 16:05:01,302 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 16:05:01,303 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 16:09:39,333 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 16:09:39,334 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 16:09:39,334 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 16:09:39,653 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 16:09:39,660 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 16:09:39,709 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 16:09:40,513 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 16:09:41,171 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 16:09:41,171 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 16:11:24,587 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:11:28,663 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:11:44,536 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:12:00,778 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:12:01,150 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:12:01,917 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:12:09,115 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:12:09,632 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:13:02,175 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 16:13:02,177 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 16:13:02,177 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 16:13:06,470 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 16:13:06,470 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 16:13:06,471 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 16:13:07,504 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 16:13:07,508 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 16:13:07,549 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 16:13:08,350 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 16:13:08,990 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 16:13:08,991 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 16:13:14,169 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 16:13:14,170 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 16:13:14,170 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 16:13:18,337 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 16:13:18,337 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 16:13:18,338 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 16:13:18,683 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 16:13:18,688 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 16:13:18,739 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 16:13:19,680 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 16:13:20,323 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 16:13:20,324 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 16:13:32,671 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 16:13:32,672 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 16:13:32,672 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 16:13:36,727 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 16:13:36,727 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 16:13:36,728 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 16:13:37,026 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 16:13:37,030 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 16:13:37,059 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 16:13:37,992 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 16:13:38,637 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 16:13:38,637 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 16:13:38,674 - ERROR - app - [main.py:309] - 请求处理异常: invalid literal for int() with base 10: "{'sub': '2'}"
2025-05-19 16:14:27,639 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 16:14:27,642 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 16:14:27,642 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 16:14:33,257 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 16:14:33,257 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 16:14:33,258 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 16:14:33,660 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 16:14:33,669 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 16:14:33,703 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 16:14:34,647 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 16:14:35,295 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 16:14:35,295 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-19 16:14:40,359 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-19 16:14:40,360 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-19 16:14:40,361 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-19 16:14:45,357 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-19 16:14:45,357 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-19 16:14:45,358 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-19 16:14:45,886 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-19 16:14:45,897 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-19 16:14:46,196 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-19 16:14:47,785 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-19 16:14:48,493 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-19 16:14:48,493 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务

2025-05-08 11:22:05,099 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:05,099 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:05,100 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:05,100 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:05,100 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:05,101 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:05,443 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:05,458 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:05,465 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:05,466 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:05,470 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:05,499 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:05,510 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:05,554 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:05,558 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:06,485 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:06,485 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:06,485 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:06,485 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:06,687 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:06,689 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:07,502 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:07,503 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:07,504 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:07,505 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:07,587 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:07,676 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:07,694 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:08,208 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:08,325 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:08,784 - INFO - app - [main.py:360] - 数据库初始化完成
2025-05-08 11:22:08,784 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:08,813 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:08,824 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:09,075 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:09,116 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:10,148 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:10,150 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:10,204 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:10,204 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:10,205 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:10,205 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:10,630 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:10,693 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:10,703 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:11,245 - INFO - app - [main.py:360] - 数据库初始化完成
2025-05-08 11:22:11,245 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:12,316 - INFO - app - [main.py:360] - 数据库初始化完成
2025-05-08 11:22:12,316 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:23,866 - INFO - app - [main.py:428] - 应用关闭，执行清理操作...
2025-05-08 11:22:23,867 - INFO - app - [main.py:434] - 已停止认证配置刷新任务
2025-05-08 11:22:23,867 - INFO - app - [main.py:447] - 应用已完全关闭
2025-05-08 11:22:23,948 - INFO - app - [main.py:428] - 应用关闭，执行清理操作...
2025-05-08 11:22:23,949 - INFO - app - [main.py:434] - 已停止认证配置刷新任务
2025-05-08 11:22:23,950 - INFO - app - [main.py:447] - 应用已完全关闭
2025-05-08 11:22:23,966 - INFO - app - [main.py:428] - 应用关闭，执行清理操作...
2025-05-08 11:22:23,967 - INFO - app - [main.py:434] - 已停止认证配置刷新任务
2025-05-08 11:22:23,967 - INFO - app - [main.py:447] - 应用已完全关闭
2025-05-08 11:22:23,967 - INFO - app - [main.py:428] - 应用关闭，执行清理操作...
2025-05-08 11:22:23,968 - INFO - app - [main.py:434] - 已停止认证配置刷新任务
2025-05-08 11:22:23,968 - INFO - app - [main.py:447] - 应用已完全关闭
2025-05-08 11:22:25,355 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:25,357 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:25,358 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:25,360 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:25,368 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:25,369 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:25,713 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:25,806 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:25,812 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:25,967 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:25,976 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:25,995 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:26,023 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:26,024 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:26,032 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:26,826 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:26,828 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:27,158 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:27,158 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:27,158 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:27,158 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:27,233 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:27,236 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:27,273 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:27,654 - INFO - app - [main.py:360] - 数据库初始化完成
2025-05-08 11:22:27,654 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:28,120 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:28,121 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:28,122 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:28,123 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:28,335 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:28,340 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:28,376 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:28,427 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:28,436 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:28,441 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:28,612 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:28,613 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:28,682 - INFO - app - [main.py:360] - 数据库初始化完成
2025-05-08 11:22:28,683 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:28,942 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:28,987 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:28,993 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:29,179 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:29,179 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:29,850 - INFO - app - [main.py:360] - 数据库初始化完成
2025-05-08 11:22:29,850 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:30,378 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:30,379 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:30,449 - INFO - app - [main.py:360] - 数据库初始化完成
2025-05-08 11:22:30,449 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:30,655 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:30,660 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:30,694 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:31,061 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:31,063 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:31,213 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:31,215 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:31,337 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:31,385 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:31,395 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:31,447 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:31,448 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:31,536 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:31,621 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:31,676 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:32,006 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:32,038 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:32,046 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:32,169 - INFO - app - [main.py:360] - 数据库初始化完成
2025-05-08 11:22:32,170 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:32,375 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:32,376 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:32,381 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:32,382 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:32,827 - INFO - app - [main.py:360] - 数据库初始化完成
2025-05-08 11:22:32,827 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:34,700 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:34,702 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:35,031 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:35,040 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:35,096 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:36,247 - INFO - app - [main.py:428] - 应用关闭，执行清理操作...
2025-05-08 11:22:36,247 - INFO - app - [main.py:434] - 已停止认证配置刷新任务
2025-05-08 11:22:36,248 - INFO - app - [main.py:447] - 应用已完全关闭
2025-05-08 11:22:36,253 - INFO - app - [main.py:428] - 应用关闭，执行清理操作...
2025-05-08 11:22:36,253 - INFO - app - [main.py:434] - 已停止认证配置刷新任务
2025-05-08 11:22:36,253 - INFO - app - [main.py:447] - 应用已完全关闭
2025-05-08 11:22:36,290 - INFO - app - [main.py:428] - 应用关闭，执行清理操作...
2025-05-08 11:22:36,290 - INFO - app - [main.py:434] - 已停止认证配置刷新任务
2025-05-08 11:22:36,290 - INFO - app - [main.py:447] - 应用已完全关闭
2025-05-08 11:22:36,358 - INFO - app - [main.py:360] - 数据库初始化完成
2025-05-08 11:22:36,358 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:37,430 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:37,432 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:37,450 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:37,452 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:37,452 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:37,453 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:37,723 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:37,737 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:37,751 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:37,755 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:37,762 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:37,793 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:37,793 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:37,796 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:37,798 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:38,363 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:38,363 - ERROR - app - [main.py:362] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:38,365 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:38,365 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:38,899 - INFO - app - [main.py:360] - 数据库初始化完成
2025-05-08 11:22:38,899 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:39,077 - INFO - app - [main.py:351] - 开始应用启动初始化...
2025-05-08 11:22:39,079 - INFO - app - [main.py:368] - Redis连接池初始化完成
2025-05-08 11:22:39,381 - INFO - app - [main.py:404] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:39,402 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:39,410 - INFO - app - [main.py:388] - 动态配置加载完成
2025-05-08 11:22:39,810 - INFO - app - [main.py:428] - 应用关闭，执行清理操作...
2025-05-08 11:22:39,810 - INFO - app - [main.py:434] - 已停止认证配置刷新任务
2025-05-08 11:22:39,810 - INFO - app - [main.py:447] - 应用已完全关闭
2025-05-08 11:22:39,886 - INFO - app - [main.py:428] - 应用关闭，执行清理操作...
2025-05-08 11:22:39,886 - INFO - app - [main.py:428] - 应用关闭，执行清理操作...
2025-05-08 11:22:39,886 - INFO - app - [main.py:434] - 已停止认证配置刷新任务
2025-05-08 11:22:39,886 - INFO - app - [main.py:434] - 已停止认证配置刷新任务
2025-05-08 11:22:39,886 - INFO - app - [main.py:447] - 应用已完全关闭
2025-05-08 11:22:39,886 - INFO - app - [main.py:447] - 应用已完全关闭
2025-05-08 11:22:40,399 - INFO - app - [main.py:360] - 数据库初始化完成
2025-05-08 11:22:40,399 - INFO - app - [main.py:420] - 应用启动完成: 管理后台服务
2025-05-08 11:22:40,805 - INFO - app - [main.py:352] - 开始应用启动初始化...
2025-05-08 11:22:40,806 - INFO - app - [main.py:369] - Redis连接池初始化完成
2025-05-08 11:22:40,859 - INFO - app - [main.py:352] - 开始应用启动初始化...
2025-05-08 11:22:40,859 - INFO - app - [main.py:352] - 开始应用启动初始化...
2025-05-08 11:22:40,860 - INFO - app - [main.py:369] - Redis连接池初始化完成
2025-05-08 11:22:40,861 - INFO - app - [main.py:369] - Redis连接池初始化完成
2025-05-08 11:22:41,021 - INFO - app - [main.py:405] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:41,043 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:41,043 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:41,048 - INFO - app - [main.py:389] - 动态配置加载完成
2025-05-08 11:22:41,048 - INFO - app - [main.py:389] - 动态配置加载完成
2025-05-08 11:22:41,060 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:41,066 - INFO - app - [main.py:389] - 动态配置加载完成
2025-05-08 11:22:41,180 - INFO - app - [main.py:405] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:41,184 - INFO - app - [main.py:405] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:41,895 - ERROR - app - [main.py:363] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:41,896 - INFO - app - [main.py:421] - 应用启动完成: 管理后台服务
2025-05-08 11:22:41,901 - ERROR - app - [main.py:363] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:41,902 - INFO - app - [main.py:421] - 应用启动完成: 管理后台服务
2025-05-08 11:22:42,352 - INFO - app - [main.py:361] - 数据库初始化完成
2025-05-08 11:22:42,353 - INFO - app - [main.py:421] - 应用启动完成: 管理后台服务
2025-05-08 11:22:42,963 - INFO - app - [main.py:352] - 开始应用启动初始化...
2025-05-08 11:22:42,966 - INFO - app - [main.py:369] - Redis连接池初始化完成
2025-05-08 11:22:43,007 - INFO - app - [main.py:352] - 开始应用启动初始化...
2025-05-08 11:22:43,008 - INFO - app - [main.py:369] - Redis连接池初始化完成
2025-05-08 11:22:43,011 - INFO - app - [main.py:352] - 开始应用启动初始化...
2025-05-08 11:22:43,012 - INFO - app - [main.py:369] - Redis连接池初始化完成
2025-05-08 11:22:43,224 - INFO - app - [main.py:405] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:43,249 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:43,265 - INFO - app - [main.py:389] - 动态配置加载完成
2025-05-08 11:22:43,269 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:43,277 - INFO - app - [main.py:389] - 动态配置加载完成
2025-05-08 11:22:43,291 - INFO - app - [main.py:405] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:43,307 - INFO - app - [main.py:405] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:43,308 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:43,315 - INFO - app - [main.py:389] - 动态配置加载完成
2025-05-08 11:22:43,353 - INFO - app - [main.py:352] - 开始应用启动初始化...
2025-05-08 11:22:43,354 - INFO - app - [main.py:369] - Redis连接池初始化完成
2025-05-08 11:22:43,628 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:43,631 - INFO - app - [main.py:389] - 动态配置加载完成
2025-05-08 11:22:43,676 - INFO - app - [main.py:405] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:43,930 - ERROR - app - [main.py:363] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:43,930 - ERROR - app - [main.py:363] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:43,930 - INFO - app - [main.py:421] - 应用启动完成: 管理后台服务
2025-05-08 11:22:43,930 - INFO - app - [main.py:421] - 应用启动完成: 管理后台服务
2025-05-08 11:22:44,364 - INFO - app - [main.py:361] - 数据库初始化完成
2025-05-08 11:22:44,364 - INFO - app - [main.py:421] - 应用启动完成: 管理后台服务
2025-05-08 11:22:44,652 - INFO - app - [main.py:361] - 数据库初始化完成
2025-05-08 11:22:44,652 - INFO - app - [main.py:421] - 应用启动完成: 管理后台服务
2025-05-08 11:22:46,384 - INFO - app - [main.py:352] - 开始应用启动初始化...
2025-05-08 11:22:46,386 - INFO - app - [main.py:369] - Redis连接池初始化完成
2025-05-08 11:22:46,762 - INFO - app - [main.py:405] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:46,796 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:46,802 - INFO - app - [main.py:389] - 动态配置加载完成
2025-05-08 11:22:48,001 - INFO - app - [main.py:361] - 数据库初始化完成
2025-05-08 11:22:48,002 - INFO - app - [main.py:421] - 应用启动完成: 管理后台服务
2025-05-08 11:22:51,338 - INFO - app - [main.py:429] - 应用关闭，执行清理操作...
2025-05-08 11:22:51,338 - INFO - app - [main.py:435] - 已停止认证配置刷新任务
2025-05-08 11:22:51,338 - INFO - app - [main.py:448] - 应用已完全关闭
2025-05-08 11:22:51,338 - INFO - app - [main.py:429] - 应用关闭，执行清理操作...
2025-05-08 11:22:51,338 - INFO - app - [main.py:429] - 应用关闭，执行清理操作...
2025-05-08 11:22:51,338 - INFO - app - [main.py:435] - 已停止认证配置刷新任务
2025-05-08 11:22:51,338 - INFO - app - [main.py:448] - 应用已完全关闭
2025-05-08 11:22:51,338 - INFO - app - [main.py:435] - 已停止认证配置刷新任务
2025-05-08 11:22:51,339 - INFO - app - [main.py:448] - 应用已完全关闭
2025-05-08 11:22:51,342 - INFO - app - [main.py:429] - 应用关闭，执行清理操作...
2025-05-08 11:22:51,342 - INFO - app - [main.py:435] - 已停止认证配置刷新任务
2025-05-08 11:22:51,342 - INFO - app - [main.py:448] - 应用已完全关闭
2025-05-08 11:22:52,454 - INFO - app - [main.py:352] - 开始应用启动初始化...
2025-05-08 11:22:52,457 - INFO - app - [main.py:369] - Redis连接池初始化完成
2025-05-08 11:22:52,460 - INFO - app - [main.py:352] - 开始应用启动初始化...
2025-05-08 11:22:52,461 - INFO - app - [main.py:369] - Redis连接池初始化完成
2025-05-08 11:22:52,464 - INFO - app - [main.py:352] - 开始应用启动初始化...
2025-05-08 11:22:52,465 - INFO - app - [main.py:369] - Redis连接池初始化完成
2025-05-08 11:22:52,814 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:52,814 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:52,818 - INFO - app - [main.py:389] - 动态配置加载完成
2025-05-08 11:22:52,818 - INFO - app - [main.py:389] - 动态配置加载完成
2025-05-08 11:22:52,835 - INFO - app - [main.py:405] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:52,848 - INFO - app - [main.py:405] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:52,880 - INFO - app - [main.py:405] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:52,896 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:52,903 - INFO - app - [main.py:389] - 动态配置加载完成
2025-05-08 11:22:53,568 - ERROR - app - [main.py:363] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:53,568 - ERROR - app - [main.py:363] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:53,568 - INFO - app - [main.py:421] - 应用启动完成: 管理后台服务
2025-05-08 11:22:53,568 - INFO - app - [main.py:421] - 应用启动完成: 管理后台服务
2025-05-08 11:22:53,871 - INFO - app - [main.py:429] - 应用关闭，执行清理操作...
2025-05-08 11:22:53,871 - INFO - app - [main.py:435] - 已停止认证配置刷新任务
2025-05-08 11:22:53,871 - INFO - app - [main.py:448] - 应用已完全关闭
2025-05-08 11:22:53,871 - INFO - app - [main.py:429] - 应用关闭，执行清理操作...
2025-05-08 11:22:53,871 - INFO - app - [main.py:435] - 已停止认证配置刷新任务
2025-05-08 11:22:53,871 - INFO - app - [main.py:448] - 应用已完全关闭
2025-05-08 11:22:53,904 - INFO - app - [main.py:361] - 数据库初始化完成
2025-05-08 11:22:53,904 - INFO - app - [main.py:421] - 应用启动完成: 管理后台服务
2025-05-08 11:22:54,067 - INFO - app - [main.py:352] - 开始应用启动初始化...
2025-05-08 11:22:54,069 - INFO - app - [main.py:369] - Redis连接池初始化完成
2025-05-08 11:22:54,342 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:54,360 - INFO - app - [main.py:389] - 动态配置加载完成
2025-05-08 11:22:54,386 - INFO - app - [main.py:405] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:54,905 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:22:54,906 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:22:54,912 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:22:54,913 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:22:54,920 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:22:54,921 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:22:55,114 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:55,119 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:55,145 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:55,148 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:55,152 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:22:55,153 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:22:55,158 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:55,165 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:22:55,204 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:55,418 - INFO - app - [main.py:361] - 数据库初始化完成
2025-05-08 11:22:55,419 - INFO - app - [main.py:421] - 应用启动完成: 管理后台服务
2025-05-08 11:22:55,724 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:55,724 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:55,725 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:22:55,725 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:22:56,123 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:22:56,124 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:22:56,818 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:22:56,819 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:22:56,819 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:22:56,819 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:22:57,000 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:57,000 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:57,027 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:57,035 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:22:57,058 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:57,063 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:22:57,696 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:22:57,696 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:22:57,960 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:22:57,962 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:22:58,048 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:22:58,048 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:22:58,239 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:22:58,245 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:22:58,282 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:22:59,306 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:22:59,307 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:01,740 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:01,742 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:02,104 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:02,185 - INFO - app.config - [config.py:240] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:02,210 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:03,485 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:23:03,485 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:15,250 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:23:15,250 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:23:15,250 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:23:15,279 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:23:15,279 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:23:15,279 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:23:15,283 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:23:15,283 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:23:15,283 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:23:15,326 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:23:15,326 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:23:15,327 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:23:16,517 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:16,518 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:16,520 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:16,521 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:16,521 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:16,522 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:16,824 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:16,844 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:16,845 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:16,858 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:16,858 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:16,915 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:16,938 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:16,967 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:16,972 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:17,830 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:17,832 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:17,848 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:17,849 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:17,850 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:17,850 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:18,143 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:18,166 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:18,173 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:18,359 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:23:18,359 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:19,401 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:23:19,401 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:27,576 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:23:27,577 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:23:27,577 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:23:27,583 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:23:27,583 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:23:27,583 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:23:27,583 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:23:27,583 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:23:27,583 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:23:27,592 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:23:27,592 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:23:27,592 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:23:28,882 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:28,883 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:28,885 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:28,886 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:28,889 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:28,890 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:29,143 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:29,158 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:29,186 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:29,193 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:29,194 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:29,346 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:29,385 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:29,385 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:29,386 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:30,343 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:30,343 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:30,344 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:30,345 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:30,543 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:30,545 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:30,860 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:30,896 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:23:30,896 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:30,900 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:30,909 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:32,709 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:23:32,711 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:37,091 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:23:37,091 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:23:37,091 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:23:37,093 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:23:37,093 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:23:37,093 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:23:37,139 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:23:37,140 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:23:37,140 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:23:37,140 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:23:37,140 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:23:37,140 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:23:38,071 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:38,072 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:38,101 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:38,102 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:38,103 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:38,104 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:38,285 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:38,288 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:38,290 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:38,314 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:38,321 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:38,321 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:38,322 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:38,330 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:38,344 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:39,045 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:39,045 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:39,045 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:39,045 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:39,569 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:23:39,570 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:39,632 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:39,634 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:39,920 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:39,930 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:40,015 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:40,122 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:40,123 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:40,126 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:40,127 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:40,354 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:40,359 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:40,568 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:40,576 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:40,651 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:40,687 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:40,758 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:23:40,759 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:23:40,949 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:23:41,014 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:23:41,025 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:23:41,281 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:23:41,281 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:41,478 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:23:41,479 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:41,910 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:23:41,910 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:23:42,259 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:23:42,259 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:24:06,161 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:24:06,163 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:24:06,459 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:24:06,474 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:24:06,529 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:24:07,923 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:24:07,923 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:24:58,660 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:24:58,661 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:24:58,661 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:24:58,668 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:24:58,669 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:24:58,669 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:24:58,671 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:24:58,671 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:24:58,671 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:24:58,686 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:24:58,686 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:24:58,686 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:24:58,695 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:24:58,695 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:24:58,696 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:25:00,089 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:25:00,090 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:25:00,090 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:25:00,091 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:25:00,093 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:25:00,094 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:25:00,094 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:25:00,095 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:25:00,313 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:25:00,318 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:25:00,336 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:25:00,336 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:25:00,339 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:25:00,342 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:25:00,347 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:25:00,362 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:25:00,363 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:25:00,368 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:25:00,377 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:25:00,377 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:25:00,932 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:25:00,932 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:25:00,932 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:25:00,933 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:25:00,933 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:25:00,933 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:25:01,344 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:25:01,346 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:25:01,969 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:25:01,971 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:25:01,977 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:25:02,005 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:25:02,005 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:25:02,839 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:25:02,839 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:07,968 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:07,969 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:07,994 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:08,023 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:08,023 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:08,024 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:08,024 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:08,024 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:08,024 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:08,070 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:08,071 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:08,071 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:08,172 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:08,172 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:08,173 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:09,704 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:09,705 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:09,705 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:09,706 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:09,706 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:09,707 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:09,716 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:09,717 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:09,956 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:09,956 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:09,961 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:09,961 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:09,995 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:10,002 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:10,016 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:10,032 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:10,032 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:10,033 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:10,042 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:10,046 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:10,967 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:10,967 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:10,967 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:10,968 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:10,968 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:10,968 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:11,115 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:11,117 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:11,406 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:11,425 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:11,434 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:11,452 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:26:11,452 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:12,621 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:26:12,622 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:23,902 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:23,901 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:23,904 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:23,904 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:23,904 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:23,905 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:23,905 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:23,905 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:23,905 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:23,908 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:23,909 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:23,909 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:23,930 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:23,930 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:23,930 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:25,434 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:25,435 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:25,436 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:25,437 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:25,437 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:25,437 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:25,438 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:25,438 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:25,839 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:25,839 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:25,840 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:25,845 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:25,845 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:25,845 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:25,883 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:25,889 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:25,923 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:25,953 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:25,953 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:25,978 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:26,593 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:26,595 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:27,133 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:27,147 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:27,155 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:27,155 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:27,155 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:27,155 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:27,176 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:27,176 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:27,221 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:28,373 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:28,373 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:28,373 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:28,374 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:28,374 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:28,374 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:29,743 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:26:29,744 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:30,013 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:30,032 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:30,144 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:30,151 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:30,152 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:30,198 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:30,200 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:30,204 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:30,208 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:31,108 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:31,108 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:31,108 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:31,109 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:31,116 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:31,118 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:33,249 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:26:33,249 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:33,500 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:33,562 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:33,569 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:34,226 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:26:34,227 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:35,609 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:35,611 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:35,897 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:35,983 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:35,993 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:36,223 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:26:36,223 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:39,528 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:26:39,529 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:45,800 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:45,801 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:45,801 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:45,825 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:45,825 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:45,826 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:45,826 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:45,826 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:45,825 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:45,827 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:45,827 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:45,827 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:45,827 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:45,828 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:45,828 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:47,516 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:47,519 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:47,520 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:47,522 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:47,524 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:47,524 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:47,525 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:47,526 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:47,746 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:47,754 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:47,756 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:47,779 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:47,779 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:47,785 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:47,786 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:47,792 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:47,792 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:47,801 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:47,815 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:47,817 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:48,366 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:48,367 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:48,367 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:48,367 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:48,367 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:48,367 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:48,716 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:48,717 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:48,757 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:26:48,757 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:49,002 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:49,025 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:49,037 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:50,520 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:26:50,520 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:56,387 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:56,389 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:56,389 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:56,406 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:56,406 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:56,406 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:56,411 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:56,411 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:56,411 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:56,459 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:56,459 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:56,459 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:56,475 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:26:56,475 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:26:56,475 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:26:57,775 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:57,776 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:57,777 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:57,778 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:57,781 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:57,782 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:57,790 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:57,791 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:57,990 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:57,993 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:58,021 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:58,040 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:58,046 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:58,052 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:58,064 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:58,074 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:58,075 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:58,083 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:58,083 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:58,090 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:58,937 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:58,937 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:58,937 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:26:58,937 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:58,937 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:58,937 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:59,163 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:59,165 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:59,455 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:26:59,462 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:26:59,462 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:26:59,465 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:26:59,494 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:26:59,960 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:59,961 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:59,966 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:59,967 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:26:59,969 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:26:59,970 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:27:00,204 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:27:00,207 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:27:00,240 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:27:00,245 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:27:00,245 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:27:00,245 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:27:00,268 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:27:00,276 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:27:00,278 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:27:00,659 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:27:00,660 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:27:00,803 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:27:00,804 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:27:00,863 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:27:00,868 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:27:00,970 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:27:01,024 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:27:01,024 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:27:01,025 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:27:01,025 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:27:01,439 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:27:01,439 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:27:02,047 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:27:02,048 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:27:03,348 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:27:03,349 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:27:03,349 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:27:03,365 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:27:03,365 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:27:03,365 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:27:03,368 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:27:03,368 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:27:03,368 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:27:03,444 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:27:03,444 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:27:03,444 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:27:03,451 - INFO - app - [main.py:432] - 应用关闭，执行清理操作...
2025-05-08 11:27:03,452 - INFO - app - [main.py:438] - 已停止认证配置刷新任务
2025-05-08 11:27:03,452 - INFO - app - [main.py:451] - 应用已完全关闭
2025-05-08 11:27:04,357 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:27:04,357 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:27:04,358 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:27:04,359 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:27:04,368 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:27:04,369 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:27:04,447 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:27:04,447 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:27:04,646 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:27:04,874 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:27:04,881 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:27:04,891 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:27:04,909 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:27:04,914 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:27:04,927 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:27:04,950 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:27:04,960 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:27:04,960 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:27:04,972 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:27:04,972 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:27:05,552 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:27:05,552 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:27:05,552 - ERROR - app - [main.py:366] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "role_permission_pkey"
DETAIL:  Key (role_id, permission_id)=(1, 1) already exists.
[SQL: INSERT INTO role_permission (role_id, permission_id) VALUES ($1, $2)]
[parameters: (1, 1)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-05-08 11:27:05,552 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:27:05,552 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:27:05,553 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:27:05,763 - INFO - app - [main.py:355] - 开始应用启动初始化...
2025-05-08 11:27:05,765 - INFO - app - [main.py:372] - Redis连接池初始化完成
2025-05-08 11:27:06,072 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:27:06,072 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 11:27:06,107 - INFO - app - [main.py:408] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 11:27:06,199 - INFO - app.config - [config.py:255] - 成功从数据库加载系统配置，共10项
2025-05-08 11:27:06,208 - INFO - app - [main.py:392] - 动态配置加载完成
2025-05-08 11:27:07,856 - INFO - app - [main.py:364] - 数据库初始化完成
2025-05-08 11:27:07,857 - INFO - app - [main.py:424] - 应用启动完成: 管理后台服务
2025-05-08 13:14:12,303 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:14:12,305 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:14:12,656 - INFO - app.config - [config.py:264] - 成功从数据库加载系统配置，共10项
2025-05-08 13:14:12,665 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:14:12,698 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:14:13,540 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:14:14,187 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:14:14,188 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:14:16,852 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:14:16,854 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:14:17,176 - INFO - app.config - [config.py:264] - 成功从数据库加载系统配置，共10项
2025-05-08 13:14:17,180 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:14:17,231 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:14:18,063 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:14:18,706 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:14:18,707 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:14:20,728 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:14:20,728 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:14:20,729 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:14:23,094 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:14:23,095 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:14:23,633 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:14:23,666 - INFO - app.config - [config.py:264] - 成功从数据库加载系统配置，共10项
2025-05-08 13:14:23,671 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:14:24,580 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:14:25,235 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:14:25,235 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:14:27,772 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:14:27,774 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:14:28,087 - INFO - app.config - [config.py:264] - 成功从数据库加载系统配置，共10项
2025-05-08 13:14:28,091 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:14:28,155 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:14:29,095 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:14:29,737 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:14:29,738 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:14:33,665 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:14:33,666 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:14:33,666 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:14:36,272 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:14:36,274 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:14:36,629 - INFO - app.config - [config.py:264] - 成功从数据库加载系统配置，共10项
2025-05-08 13:14:36,638 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:14:36,707 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:14:37,577 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:14:38,213 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:14:38,213 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:14:40,743 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:14:40,745 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:14:41,073 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:14:41,134 - INFO - app.config - [config.py:264] - 成功从数据库加载系统配置，共10项
2025-05-08 13:14:41,141 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:14:42,127 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:14:42,770 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:14:42,770 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:14:45,398 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:14:45,400 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:14:45,950 - INFO - app.config - [config.py:264] - 成功从数据库加载系统配置，共10项
2025-05-08 13:14:45,954 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:14:46,024 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:14:46,912 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:14:47,618 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:14:47,618 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:14:48,652 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:14:48,653 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:14:48,822 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:14:48,842 - INFO - app.config - [config.py:264] - 成功从数据库加载系统配置，共10项
2025-05-08 13:14:48,846 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:14:49,587 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:14:50,214 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:14:50,214 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:14:50,215 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:14:50,215 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:14:50,215 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:15:10,680 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:15:10,682 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:15:10,853 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:15:10,877 - INFO - app.config - [config.py:264] - 成功从数据库加载系统配置，共10项
2025-05-08 13:15:10,884 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:15:11,814 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:15:12,449 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:15:12,449 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:15:12,450 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:15:12,450 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:15:12,450 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:16:02,652 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:16:02,653 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:16:02,654 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:18:47,843 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:18:47,844 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:18:48,032 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:18:48,055 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:18:48,059 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:18:48,897 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:18:49,535 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:18:49,535 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:19:13,548 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:19:13,548 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:19:13,726 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:19:13,804 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:19:13,809 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:19:14,769 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:19:15,424 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:19:15,424 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:20:18,279 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:20:18,280 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:20:19,129 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:20:19,145 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:20:19,313 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:20:21,839 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:20:22,540 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:20:22,541 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:22:27,429 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:22:27,430 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:22:27,431 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:22:30,416 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:22:30,417 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:22:30,714 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:22:30,719 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:22:30,736 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:22:31,463 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:22:32,116 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:22:32,117 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:22:34,765 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:22:34,767 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:22:35,165 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:22:35,188 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:22:35,192 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:22:35,930 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:22:36,564 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:22:36,565 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:23:30,443 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:23:30,445 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:23:30,447 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:23:54,655 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:23:54,656 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:23:54,842 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:23:54,853 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:23:54,888 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:23:55,589 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:23:56,224 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:23:56,224 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:26:50,520 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:26:50,521 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:26:50,521 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:26:51,988 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:26:51,989 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:26:52,172 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:26:52,184 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:26:52,244 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:26:53,140 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:26:53,797 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:26:53,797 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:26:54,863 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:26:54,864 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:26:55,066 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:26:55,070 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:26:55,092 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:26:55,866 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:26:56,493 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:26:56,493 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:27:10,054 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:27:10,055 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:27:10,055 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:27:11,376 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:27:11,377 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:27:11,616 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:27:11,625 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:27:11,674 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:27:12,586 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:27:13,227 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:27:13,228 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:27:16,447 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:27:16,447 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:27:16,447 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:27:17,706 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:27:17,707 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:27:18,051 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:27:18,057 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:27:18,073 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:27:20,434 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:27:21,079 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:27:21,079 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:27:22,095 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:27:22,096 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:27:22,328 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:27:22,342 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:27:22,346 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:27:24,163 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:27:24,835 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:27:24,835 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:27:41,262 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:27:41,263 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:27:41,263 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:27:42,577 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:27:42,577 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:27:43,054 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:27:43,060 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:27:43,082 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:27:43,892 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:27:44,530 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:27:44,530 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:27:45,554 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:27:45,554 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:27:45,741 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:27:45,759 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:27:45,765 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:27:46,527 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:27:47,173 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:27:47,173 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:27:48,189 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:27:48,189 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:27:48,383 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:27:48,389 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:27:48,433 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:27:49,100 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:27:49,752 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:27:49,752 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:28:08,252 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:28:08,254 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:28:08,254 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:28:09,557 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:28:09,557 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:28:09,768 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:28:09,771 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:28:10,001 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:28:10,848 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:28:11,481 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:28:11,481 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:28:12,515 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:28:12,516 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:28:12,739 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:28:12,757 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:28:12,761 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:28:13,593 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:28:14,233 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:28:14,234 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:28:15,247 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:28:15,247 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:28:15,457 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:28:15,463 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:28:15,513 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:28:16,210 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:28:16,861 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:28:16,861 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:28:54,089 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:28:54,090 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:28:54,091 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:28:55,483 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:28:55,484 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:28:55,731 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:28:55,737 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:28:55,760 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:28:56,547 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:28:57,179 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:28:57,180 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:28:58,206 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:28:58,207 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:28:58,370 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:28:58,374 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:28:58,442 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:28:59,354 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:29:00,085 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:29:00,086 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:29:01,119 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:29:01,120 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:29:01,324 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:29:01,332 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:29:01,349 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:29:02,188 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:29:02,868 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:29:02,869 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:30:56,469 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:30:56,470 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:30:56,470 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:30:57,843 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:30:57,844 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:30:58,089 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:30:58,093 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:30:58,120 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:30:58,987 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:30:59,616 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:30:59,617 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:31:00,613 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:31:00,614 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:31:00,841 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:31:00,845 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:31:00,865 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:31:01,617 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:31:02,256 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:31:02,256 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:31:25,740 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:31:25,740 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:31:25,740 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:31:26,957 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:31:26,957 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:31:27,201 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:31:27,223 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:31:27,230 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:31:28,129 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:31:28,763 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:31:28,763 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:31:29,844 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:31:29,846 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:31:30,054 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:31:30,058 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:31:30,102 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:31:31,260 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:31:31,913 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:31:31,913 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:31:45,785 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:31:45,786 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:31:45,786 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:32:07,723 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:32:07,724 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:32:08,033 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:32:08,046 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:32:08,125 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:32:09,469 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:32:10,137 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:32:10,138 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:38:43,120 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:38:43,121 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:38:43,122 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:38:44,680 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:38:44,680 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:38:45,034 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:38:45,058 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:38:45,062 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:38:45,867 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:38:46,501 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:38:46,501 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:38:47,106 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:38:47,106 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:38:47,106 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:38:48,119 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:38:48,120 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:38:48,300 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:38:48,303 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:38:48,390 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:38:49,140 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:38:49,776 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:38:49,776 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:38:51,076 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:38:51,076 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:38:51,275 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:38:51,279 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:38:51,301 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:38:55,247 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:38:55,881 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:38:55,881 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:39:09,420 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:39:09,421 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:39:09,421 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:39:10,809 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:39:10,810 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:39:11,096 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:39:11,144 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:39:11,148 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:39:11,907 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:39:12,541 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:39:12,541 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:39:12,743 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:39:12,744 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:39:12,744 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:39:13,936 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:39:13,937 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:39:14,112 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:39:14,167 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:39:14,171 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:39:15,061 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:39:15,696 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:39:15,696 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:39:16,743 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:39:16,744 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:39:17,001 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:39:17,006 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:39:17,055 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:39:18,056 - INFO - app - [main.py:418] - 数据库初始化完成
2025-05-08 13:39:18,699 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:39:18,699 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:39:42,238 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:39:42,239 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:39:42,239 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:39:43,515 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:39:43,515 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:39:43,716 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:39:43,756 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:39:43,759 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:39:44,397 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:39:45,030 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:39:45,030 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:39:46,074 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:39:46,075 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:39:46,280 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:39:46,316 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:39:46,320 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:39:47,277 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:39:47,913 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:39:47,913 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:39:54,486 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:39:54,486 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:39:54,486 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:39:55,583 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:39:55,583 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:39:56,153 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:39:56,171 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:39:56,314 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:39:57,277 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:39:57,913 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:39:57,913 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:40:22,857 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:40:22,858 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:40:22,858 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:40:24,155 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:40:24,156 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:40:24,503 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:40:24,528 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:40:24,533 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:40:25,096 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:40:25,728 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:40:25,728 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:40:26,797 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:40:26,798 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:40:27,013 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:40:27,022 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:40:27,067 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:40:27,743 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:40:28,379 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:40:28,380 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:41:03,702 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:41:03,702 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:41:03,702 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:41:05,008 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:41:05,009 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:41:05,207 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:41:05,211 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:41:05,233 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:41:05,807 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:41:06,457 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:41:06,458 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:41:07,485 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:41:07,486 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:41:07,723 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:41:07,749 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:41:07,754 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:41:08,329 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:41:08,951 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:41:08,951 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:41:40,141 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:41:40,142 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:41:40,143 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:41:41,418 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:41:41,419 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:41:41,588 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:41:41,609 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:41:41,614 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:41:42,310 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:41:42,935 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:41:42,935 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:41:43,960 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:41:43,960 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:41:44,119 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:41:44,144 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:41:44,147 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:41:44,747 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:41:45,390 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:41:45,390 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:42:40,217 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:42:40,232 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:42:40,236 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:42:43,950 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:42:43,951 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:42:44,177 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:42:44,182 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:42:44,207 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:42:44,822 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:42:45,450 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:42:45,450 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:42:46,899 - INFO - app - [main.py:409] - 开始应用启动初始化...
2025-05-08 13:42:46,900 - INFO - app - [main.py:426] - Redis连接池初始化完成
2025-05-08 13:42:47,096 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:42:47,103 - INFO - app - [main.py:446] - 动态配置加载完成
2025-05-08 13:42:47,142 - INFO - app - [main.py:462] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:42:48,178 - ERROR - app - [main.py:420] - 数据库初始化失败: (sqlalchemy.dialects.postgresql.asyncpg.ProgrammingError) <class 'asyncpg.exceptions.UndefinedColumnError'>: column files.storage_type does not exist
[SQL: SELECT files.owner_id AS files_owner_id, files.id AS files_id, files.filename AS files_filename, files.original_filename AS files_original_filename, files.storage_path AS files_storage_path, files.file_url AS files_file_url, files.content_type AS files_content_type, files.size AS files_size, files.file_hash AS files_file_hash, files.category AS files_category, files.tags AS files_tags, files.is_public AS files_is_public, files.access_key AS files_access_key, files.storage_type AS files_storage_type, files.status AS files_status, files.file_metadata AS files_file_metadata, files.description AS files_description, files.created_at AS files_created_at, files.updated_at AS files_updated_at, files.expires_at AS files_expires_at 
FROM files 
WHERE files.owner_id IN ($1::INTEGER)]
[parameters: (1,)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-05-08 13:42:48,814 - INFO - app - [main.py:484] - 数据库连接状态: healthy
2025-05-08 13:42:48,814 - INFO - app - [main.py:488] - 应用启动完成: 管理后台服务
2025-05-08 13:43:12,340 - INFO - app - [main.py:496] - 应用关闭，执行清理操作...
2025-05-08 13:43:12,343 - INFO - app - [main.py:502] - 已停止认证配置刷新任务
2025-05-08 13:43:12,343 - INFO - app - [main.py:518] - 应用已完全关闭
2025-05-08 13:43:13,707 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:43:13,707 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:43:13,707 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:43:14,913 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:43:14,913 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:43:14,913 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:47:54,619 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:47:54,620 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:47:54,620 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:48:53,052 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:48:53,053 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:48:53,053 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:48:53,279 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:48:53,283 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 13:48:53,358 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:48:54,100 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 13:48:54,726 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 13:48:54,726 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 13:49:32,402 - ERROR - app - [main.py:310] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-05-08 13:50:04,837 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 13:50:04,838 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 13:50:04,838 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 13:50:30,846 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:50:30,846 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:50:30,846 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:50:31,027 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:50:31,071 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:50:31,076 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 13:50:31,981 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 13:50:32,614 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 13:50:32,615 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 13:53:27,303 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 13:53:27,304 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 13:53:27,304 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 13:53:28,951 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:53:28,951 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:53:28,952 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:53:29,203 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:53:29,208 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 13:53:29,310 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:53:30,506 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 13:53:31,137 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 13:53:31,137 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 13:53:32,267 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:53:32,267 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:53:32,267 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:53:32,479 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:53:32,485 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 13:53:32,509 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:53:33,211 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 13:53:34,054 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 13:53:34,055 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 13:53:35,271 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:53:35,271 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:53:35,271 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:53:35,493 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:53:35,530 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:53:35,540 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 13:53:36,232 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 13:53:36,852 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 13:53:36,852 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 13:53:37,970 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:53:37,971 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:53:37,971 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:53:38,135 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:53:38,176 - INFO - app.config - [config.py:268] - 成功从数据库加载系统配置，共10项
2025-05-08 13:53:38,183 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 13:53:38,809 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 13:53:39,436 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 13:53:39,437 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 13:54:58,356 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 13:54:58,358 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 13:54:58,359 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 13:54:59,775 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:54:59,775 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:54:59,775 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:55:00,005 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 13:55:00,012 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 13:55:00,032 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:55:00,798 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 13:55:01,448 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 13:55:01,448 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 13:55:02,817 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:55:02,818 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:55:02,818 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:55:03,041 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:55:03,068 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 13:55:03,077 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 13:55:04,083 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 13:55:04,775 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 13:55:04,775 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 13:55:05,883 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:55:05,883 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:55:05,883 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:55:06,110 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 13:55:06,124 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 13:55:06,144 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:55:06,837 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 13:55:07,461 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 13:55:07,462 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 13:59:24,419 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 13:59:24,420 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 13:59:24,421 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 13:59:26,117 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:59:26,118 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:59:26,118 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:59:26,399 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 13:59:26,404 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 13:59:26,429 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:59:27,290 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 13:59:27,917 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 13:59:27,917 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 13:59:29,007 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:59:29,007 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:59:29,007 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:59:29,257 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 13:59:29,262 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 13:59:29,308 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:59:30,106 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 13:59:30,752 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 13:59:30,752 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 13:59:31,961 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 13:59:31,961 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 13:59:31,961 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 13:59:32,168 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 13:59:32,204 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 13:59:32,208 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 13:59:32,973 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 13:59:33,605 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 13:59:33,606 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:00:16,746 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:00:16,748 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:00:16,748 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:00:18,335 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:00:18,335 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:00:18,335 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:00:18,536 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:00:18,540 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:00:18,650 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:00:19,535 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:00:20,170 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:00:20,170 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:00:21,245 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:00:21,245 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:00:21,245 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:00:21,481 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:00:21,485 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:00:21,512 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:00:22,214 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:00:22,882 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:00:22,883 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:00:23,972 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:00:23,973 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:00:23,973 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:00:24,243 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:00:24,280 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:00:24,285 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:00:25,089 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:00:25,720 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:00:25,720 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:01:02,546 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:01:02,547 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:01:02,547 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:01:04,057 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:01:04,057 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:01:04,057 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:01:04,239 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:01:04,244 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:01:04,284 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:01:05,217 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:01:05,852 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:01:05,852 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:01:06,918 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:01:06,918 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:01:06,918 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:01:07,147 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:01:07,152 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:01:07,178 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:01:07,910 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:01:08,538 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:01:08,538 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:01:09,623 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:01:09,624 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:01:09,624 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:01:09,828 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:01:09,832 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:01:09,854 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:01:10,623 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:01:11,261 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:01:11,261 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:02:19,215 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:02:19,216 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:02:19,216 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:02:20,676 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:02:20,676 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:02:20,676 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:02:20,850 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:02:20,908 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:02:20,916 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:02:21,874 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:02:22,511 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:02:22,511 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:02:23,947 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:02:23,947 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:02:23,947 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:02:25,073 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:02:25,074 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:02:25,074 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:02:25,320 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:02:25,324 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:02:25,348 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:02:27,420 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:02:28,087 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:02:28,087 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:02:29,214 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:02:29,214 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:02:29,214 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:02:29,901 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:02:29,914 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:02:29,996 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:02:31,387 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:02:32,016 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:02:32,016 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:03:13,359 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:03:13,360 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:03:13,360 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:03:15,044 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:03:15,044 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:03:15,044 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:03:19,814 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:03:20,000 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:03:20,018 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:03:21,250 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:03:21,889 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:03:21,890 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:03:23,238 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:03:23,238 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:03:23,238 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:03:23,442 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:03:23,446 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:03:23,470 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:03:24,455 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:03:25,099 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:03:25,099 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:03:26,203 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:03:26,203 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:03:26,203 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:03:26,586 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:03:26,622 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:03:26,630 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:03:27,632 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:03:28,278 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:03:28,278 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:04:40,972 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:04:40,974 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:04:40,975 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:04:43,976 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:04:43,976 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:04:43,977 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:04:44,301 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:04:44,386 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:04:44,390 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:04:45,347 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:04:46,000 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:04:46,001 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:04:48,213 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:04:48,213 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:04:48,214 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:04:48,858 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:04:48,888 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:04:48,915 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:04:49,697 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:04:50,327 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:04:50,328 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:04:51,664 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:04:51,664 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:04:51,664 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:04:51,831 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:04:51,836 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:04:51,884 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:04:52,773 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:04:53,412 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:04:53,412 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:04:59,720 - ERROR - app - [main.py:310] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-05-08 14:12:09,440 - ERROR - app - [main.py:310] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-05-08 14:13:03,405 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:13:03,406 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:13:03,407 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:13:05,085 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:13:05,085 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:13:05,085 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:13:05,342 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:13:05,349 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:13:05,366 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:13:06,039 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:13:06,802 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:13:06,807 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:13:07,617 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:13:07,617 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:13:07,617 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:13:08,708 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:13:08,708 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:13:08,708 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:13:09,153 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:13:09,492 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:13:09,496 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:13:10,093 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:13:10,732 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:13:10,732 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:13:11,880 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:13:11,880 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:13:11,881 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:13:12,078 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:13:12,083 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:13:12,127 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:13:12,803 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:13:13,434 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:13:13,434 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:14:29,287 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:14:29,288 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:14:29,289 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:14:30,946 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:14:30,947 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:14:30,947 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:14:31,202 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:14:31,206 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:14:31,262 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:14:32,086 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:14:32,719 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:14:32,720 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:14:33,807 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:14:33,807 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:14:33,807 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:14:34,084 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:14:34,088 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:14:34,113 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:14:34,960 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:14:35,596 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:14:35,597 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:14:37,115 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:14:37,115 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:14:37,116 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:14:38,205 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:14:38,205 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:14:38,205 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:14:38,483 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:14:38,503 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:14:38,508 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:14:39,357 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:14:39,993 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:14:39,993 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:14:41,104 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:14:41,104 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:14:41,104 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:14:42,430 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:14:42,430 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:14:42,430 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:14:42,646 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:14:42,707 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:14:42,712 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:14:43,651 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:14:44,281 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:14:44,282 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:14:45,397 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:14:45,397 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:14:45,397 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:14:45,579 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:14:45,582 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:14:45,661 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:14:46,569 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:14:47,213 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:14:47,214 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:14:50,950 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:14:50,951 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:14:50,951 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:14:52,088 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:14:52,089 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:14:52,089 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:14:52,365 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:14:52,370 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:14:52,394 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:14:53,354 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:14:53,991 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:14:53,991 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:14:54,598 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:14:54,598 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:14:54,598 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:14:55,681 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:14:55,681 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:14:55,681 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:14:55,909 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:14:55,915 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:14:55,944 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:14:57,254 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:14:57,892 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:14:57,892 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:15:04,871 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:15:04,872 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:15:04,872 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:15:05,936 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:15:05,936 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:15:05,936 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:15:06,233 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:15:06,262 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:15:06,265 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:15:07,329 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:15:07,976 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:15:07,976 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:16:10,805 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:16:10,806 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:16:10,807 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:16:12,476 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:16:12,478 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:16:12,480 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:16:12,698 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:16:12,712 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:16:12,715 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:16:13,471 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:16:14,104 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:16:14,104 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:16:14,711 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:16:14,711 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:16:14,711 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:16:15,794 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:16:15,795 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:16:15,795 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:16:15,966 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:16:15,970 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:16:16,015 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:16:16,845 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:16:17,541 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:16:17,541 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:16:18,700 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:16:18,700 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:16:18,701 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:16:18,923 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:16:18,942 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:16:18,945 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:16:19,614 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:16:20,248 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:16:20,248 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:21:27,744 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:21:27,745 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:21:27,745 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:21:29,483 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:21:29,483 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:21:29,483 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:21:29,734 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:21:29,760 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:21:29,764 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:21:30,476 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:21:31,113 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:21:31,114 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:21:31,821 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:21:31,821 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:21:31,821 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:21:32,898 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:21:32,899 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:21:32,899 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:21:33,091 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:21:33,112 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:21:33,114 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:21:33,812 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:21:34,451 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:21:34,454 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:21:35,882 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:21:35,883 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:21:35,883 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:21:36,129 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:21:36,175 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:21:36,181 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:21:36,930 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:21:37,555 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:21:37,555 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:22:15,206 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:22:15,213 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:22:15,213 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:22:16,836 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:22:16,837 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:22:16,837 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:22:17,063 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:22:17,067 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:22:17,182 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:22:18,206 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:22:18,846 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:22:18,846 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:22:19,148 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:22:19,149 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:22:19,149 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:22:20,219 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:22:20,219 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:22:20,219 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:22:20,417 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:22:20,423 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:22:20,477 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:22:21,685 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:22:22,319 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:22:22,320 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:22:23,435 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:22:23,435 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:22:23,435 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:22:23,603 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:22:23,609 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:22:23,631 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:22:24,602 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:22:25,238 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:22:25,238 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:22:34,152 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:22:34,153 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:22:34,153 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:22:35,542 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:22:35,542 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:22:35,542 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:22:35,776 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:22:35,791 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:22:35,794 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:22:36,524 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:22:37,160 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:22:37,160 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:22:47,969 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:22:47,972 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:22:47,972 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:22:49,425 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:22:49,425 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:22:49,425 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:22:49,635 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:22:49,716 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:22:49,722 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:22:50,528 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:22:51,165 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:22:51,166 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:30:56,690 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:30:56,691 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:30:56,692 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:30:58,313 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:30:58,313 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:30:58,313 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:30:58,537 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:30:58,559 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:30:58,562 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:30:59,355 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:31:00,007 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:31:00,007 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:31:01,826 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:31:01,826 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:31:01,826 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:31:02,939 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:31:02,940 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:31:02,940 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:31:03,215 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:31:03,221 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:31:03,297 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:31:04,388 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:31:05,072 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:31:05,072 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:31:06,277 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:31:06,277 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:31:06,277 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:31:06,463 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:31:06,473 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:31:06,594 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:31:07,392 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:31:08,069 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:31:08,141 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:31:09,070 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:31:09,070 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:31:09,070 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:33:17,563 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:33:17,564 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:33:17,564 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:33:17,737 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:33:17,764 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:33:17,769 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:33:18,595 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:33:19,223 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:33:19,223 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:33:20,370 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:33:20,370 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:33:20,370 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:33:20,612 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:33:20,625 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:33:20,723 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:33:21,750 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:33:22,403 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:33:22,403 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:34:01,930 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:34:01,931 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:34:01,932 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:34:03,479 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:34:03,479 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:34:03,480 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:34:03,677 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:34:03,696 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:34:03,702 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:34:04,519 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:34:05,152 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:34:05,152 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:34:05,355 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:34:05,355 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:34:05,355 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:34:06,529 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:34:06,529 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:34:06,530 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:34:06,680 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:34:06,684 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:34:06,748 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:34:07,491 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:34:08,116 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:34:08,116 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:35:32,371 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:35:32,372 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:35:32,373 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:35:33,895 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:35:33,895 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:35:33,895 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:35:34,140 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:35:34,146 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:35:34,166 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:35:35,189 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:35:35,816 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:35:35,816 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:35:36,916 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:35:36,916 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:35:36,916 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:35:37,099 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:35:37,162 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:35:37,168 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:35:37,924 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:35:38,557 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:35:38,557 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:36:04,296 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:36:04,296 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:36:04,296 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:36:05,878 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:36:05,880 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:36:05,881 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:36:06,276 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:36:06,356 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:36:06,360 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:36:07,168 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:36:07,830 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:36:07,830 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:39:29,223 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:39:29,224 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:39:29,224 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:45:36,560 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:45:36,560 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:45:36,560 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:45:36,560 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:45:36,560 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:45:36,560 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:45:36,783 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:45:36,791 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:45:36,827 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:45:36,851 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:45:36,864 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:45:36,864 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:45:37,712 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:45:37,740 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:45:38,345 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:45:38,345 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:45:38,379 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:45:38,379 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:45:54,787 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:45:54,790 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:45:54,790 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:45:54,813 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:45:54,814 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:45:54,814 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:45:56,618 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:45:56,618 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:45:56,618 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:45:56,625 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:45:56,625 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:45:56,625 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:45:56,834 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:45:56,841 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:45:56,858 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:45:56,862 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:45:56,886 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:45:56,893 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:45:57,701 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:45:57,704 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:45:58,344 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:45:58,344 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:45:58,345 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:45:58,346 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:46:55,638 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:46:55,672 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:46:55,686 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:46:55,692 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:46:55,717 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:46:55,717 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:46:57,302 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:46:57,302 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:46:57,302 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:46:57,302 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:46:57,302 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:46:57,302 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:46:57,509 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:46:57,514 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:46:57,517 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:46:57,533 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:46:57,540 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:46:57,568 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:46:58,275 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:46:58,329 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:46:58,921 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:46:58,921 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:46:58,976 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:46:58,977 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:47:00,038 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:47:00,038 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:47:00,038 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:47:00,100 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:47:00,100 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:47:00,101 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:47:00,230 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:47:00,293 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:47:00,296 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:47:00,298 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:47:00,299 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:47:00,371 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:47:01,197 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:47:01,308 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:47:01,843 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:47:01,843 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:47:01,943 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:47:01,943 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:15,576 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:48:15,581 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:48:15,581 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:48:15,684 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:48:15,685 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:48:15,685 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:48:17,343 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:48:17,343 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:48:17,343 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:48:17,343 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:48:17,343 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:48:17,343 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:48:17,543 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:48:17,568 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:48:17,574 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:48:17,574 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:48:17,598 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:48:17,603 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:48:18,293 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:48:18,382 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:48:18,939 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:48:18,939 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:19,015 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:48:19,015 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:20,931 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:48:20,931 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:48:20,931 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:48:20,960 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:48:20,960 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:48:20,960 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:48:22,020 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:48:22,020 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:48:22,021 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:48:22,035 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:48:22,035 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:48:22,036 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:48:22,231 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:48:22,231 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:48:22,235 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:48:22,235 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:48:22,269 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:48:22,290 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:48:23,425 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:48:23,473 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:48:24,061 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:48:24,061 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:24,102 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:48:24,102 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:25,205 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:48:25,206 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:48:25,206 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:48:25,212 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:48:25,212 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:48:25,213 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:48:25,460 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:48:25,468 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:48:25,510 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:48:25,512 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:48:25,528 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:48:25,531 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:48:26,531 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:48:26,612 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:48:27,179 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:48:27,179 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:27,249 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:48:27,249 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:31,721 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:48:31,723 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:48:31,723 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:48:31,845 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:48:31,846 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:48:31,846 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:48:33,118 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:48:33,118 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:48:33,118 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:48:33,157 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:48:33,158 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:48:33,158 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:48:33,320 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:48:33,324 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:48:33,354 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:48:33,364 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:48:33,402 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:48:33,408 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:48:34,043 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:48:34,116 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:48:34,671 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:48:34,671 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:34,747 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:48:34,747 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:35,756 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:48:35,756 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:48:35,756 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:48:35,784 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:48:35,784 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:48:35,784 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:48:37,182 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:48:37,183 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:48:37,183 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:48:37,206 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:48:37,206 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:48:37,206 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:48:37,369 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:48:37,373 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:48:37,377 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:48:37,380 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:48:37,399 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:48:37,422 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:48:38,145 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:48:38,170 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:48:38,775 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:48:38,776 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:38,801 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:48:38,801 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:39,927 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:48:39,928 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:48:39,928 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:48:39,965 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:48:39,965 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:48:39,966 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:48:40,133 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:48:40,184 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:48:40,187 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:48:40,188 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:48:40,190 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:48:40,207 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:48:41,018 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:48:41,059 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:48:41,641 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:48:41,641 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:41,698 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:48:41,699 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:48:59,247 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:48:59,248 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:48:59,248 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:48:59,250 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:48:59,250 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:48:59,250 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:49:00,993 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:49:00,993 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:49:00,993 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:49:00,998 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:49:00,998 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:49:00,998 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:49:01,169 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:49:01,181 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:49:01,181 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:49:01,185 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:49:01,206 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:49:01,261 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:49:02,043 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:49:02,061 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:49:02,675 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:49:02,675 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:49:02,692 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:49:02,692 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:49:03,830 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:49:03,830 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:49:03,830 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:49:03,846 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:49:03,846 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:49:03,846 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:49:04,030 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:49:04,036 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:49:04,045 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:49:04,062 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:49:04,075 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:49:04,082 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:49:04,746 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:49:04,847 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:49:05,377 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:49:05,382 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:49:05,597 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:49:05,598 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:49:06,799 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:49:06,800 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:49:06,800 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:49:06,800 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:49:06,800 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:49:06,801 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:49:07,031 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:49:07,034 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:49:07,056 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:49:07,085 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:49:07,091 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:49:07,099 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:49:08,000 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:49:08,039 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:49:08,640 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:49:08,640 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:49:08,672 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:49:08,672 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:50:16,406 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:50:16,407 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:50:16,408 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:50:16,435 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:50:16,435 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:50:16,436 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:50:17,906 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:50:17,906 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:50:17,907 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:50:17,907 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:50:17,907 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:50:17,907 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:50:18,096 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:50:18,100 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:50:18,107 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:50:18,144 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:50:18,144 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:50:18,152 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:50:19,120 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:50:19,127 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:50:19,755 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:50:19,755 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:50:19,781 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:50:19,782 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:50:26,822 - ERROR - app - [main.py:310] - 请求处理异常: 用户名或密码错误
2025-05-08 14:50:46,725 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:50:46,726 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:50:46,727 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:50:46,756 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:50:46,756 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:50:46,756 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:53:54,718 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:53:54,718 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:53:54,718 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:53:54,718 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:53:54,718 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:53:54,718 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:53:54,937 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:53:54,945 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:53:54,945 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:53:55,007 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:53:55,014 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:53:55,014 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:53:56,005 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:53:56,028 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:53:56,657 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:53:56,657 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:53:56,657 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:53:56,657 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:53:57,834 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:53:57,834 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:53:57,834 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:53:57,837 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:53:57,837 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:53:57,837 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:53:58,046 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:53:58,049 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:53:58,126 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:53:58,152 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:53:58,179 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:53:58,183 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:53:59,025 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:53:59,053 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:53:59,652 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:53:59,652 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:53:59,686 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:53:59,687 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:57:30,653 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:57:30,654 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:57:30,655 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:57:30,901 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:57:30,912 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:57:30,968 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:57:31,668 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:57:32,302 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:57:32,303 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:57:35,540 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:57:35,540 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:57:35,540 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:57:38,723 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:57:38,723 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:57:38,724 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:57:39,078 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:57:39,088 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:57:39,110 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:57:39,922 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:57:40,559 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:57:40,559 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:57:43,148 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:57:43,148 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:57:43,148 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:57:43,401 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:57:43,408 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:57:43,455 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:57:44,266 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:57:44,927 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:57:44,927 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:57:47,885 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:57:47,886 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:57:47,886 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:57:48,118 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:57:48,123 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:57:48,224 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:57:49,201 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:57:49,839 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:57:49,839 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:58:02,803 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:58:02,805 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:58:02,805 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:58:05,800 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:58:05,800 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:58:05,801 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:58:06,040 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:58:06,043 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:58:06,119 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:58:07,235 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:58:07,881 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:58:07,881 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:58:10,453 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:58:10,453 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:58:10,454 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:58:11,162 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:58:11,169 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:58:11,187 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:58:12,140 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:58:12,782 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:58:12,783 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:58:15,720 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:58:15,721 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:58:15,721 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:58:16,200 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:58:16,206 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:58:16,300 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:58:17,236 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:58:17,877 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:58:17,878 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:59:16,495 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:59:16,498 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:59:16,498 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:59:16,784 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:59:16,797 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:59:16,801 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:59:17,721 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:59:18,392 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:59:18,392 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:59:33,886 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 14:59:33,888 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 14:59:33,888 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 14:59:38,937 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:59:38,937 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:59:38,937 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:59:39,210 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:59:39,219 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:59:39,280 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:59:40,131 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:59:40,778 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:59:40,778 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:59:43,900 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:59:43,900 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:59:43,901 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:59:44,274 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:59:44,282 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:59:44,351 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:59:45,125 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:59:45,755 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:59:45,756 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 14:59:48,816 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 14:59:48,816 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 14:59:48,817 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 14:59:49,061 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 14:59:49,093 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 14:59:49,100 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 14:59:50,044 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 14:59:50,695 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 14:59:50,695 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:00:02,740 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:00:02,741 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:00:02,742 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:00:05,907 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:00:05,908 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:00:05,908 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:00:06,187 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:00:06,197 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:00:06,215 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:00:06,989 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:00:07,625 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:00:07,626 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:00:10,447 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:00:10,448 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:00:10,448 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:00:10,701 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:00:10,707 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:00:10,747 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:00:11,511 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:00:12,288 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:00:12,289 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:00:12,597 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:00:12,597 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:00:12,597 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:00:44,851 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:00:44,852 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:00:44,852 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:00:45,092 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:00:45,096 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:00:45,158 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:00:46,019 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:00:46,656 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:00:46,656 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:00:54,780 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:00:54,781 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:00:54,781 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:03:00,324 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:03:00,325 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:03:00,325 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:03:00,601 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:03:00,667 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:03:00,673 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:03:01,725 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:03:02,373 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:03:02,373 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:03:42,962 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:03:42,964 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:03:42,986 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:03:46,306 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:03:46,306 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:03:46,307 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:03:46,652 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:03:46,670 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:03:46,720 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:03:47,906 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:03:48,567 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:03:48,567 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:03:51,863 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:03:51,863 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:03:51,864 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:03:52,204 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:03:52,222 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:03:52,226 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:03:53,000 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:03:53,641 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:03:53,642 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:03:56,919 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:03:56,919 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:03:56,920 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:03:57,156 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:03:57,165 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:03:57,247 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:03:57,987 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:03:58,621 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:03:58,621 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:08:16,158 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:08:16,160 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:08:16,161 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:08:19,471 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:08:19,471 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:08:19,472 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:08:20,021 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:08:20,028 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:08:20,059 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:08:21,213 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:08:21,858 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:08:21,858 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:08:24,601 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:08:24,602 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:08:24,602 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:08:25,144 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:08:25,200 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:08:25,203 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:08:26,067 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:08:26,720 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:08:26,720 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:09:43,096 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:09:43,098 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:09:43,099 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:09:46,167 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:09:46,167 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:09:46,168 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:09:46,457 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:09:46,465 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:09:46,516 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:09:47,432 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:09:48,084 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:09:48,084 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:09:51,068 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:09:51,068 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:09:51,069 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:09:51,374 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:09:51,387 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:09:51,406 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:09:52,869 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:09:53,512 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:09:53,513 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:09:53,908 - ERROR - app - [main.py:310] - 请求处理异常: 用户名或密码错误
2025-05-08 15:10:21,328 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:10:21,330 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:10:21,330 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:10:24,786 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:10:24,786 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:10:24,787 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:10:25,080 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:10:25,112 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:10:25,117 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:10:26,079 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:10:26,730 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:10:26,731 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:10:29,407 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:10:29,408 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:10:29,408 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:10:29,716 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:10:29,764 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:10:29,767 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:10:31,031 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:10:31,686 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:10:31,686 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:10:34,588 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:10:34,588 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:10:34,589 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:10:34,916 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:10:34,943 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:10:35,036 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:10:35,912 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:10:36,550 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:10:36,550 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:11:49,378 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:11:49,380 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:11:49,380 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:11:52,373 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:11:52,373 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:11:52,373 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:11:52,662 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:11:52,673 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:11:52,691 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:11:53,525 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:11:54,203 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:11:54,203 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:11:56,852 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:11:56,852 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:11:56,853 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:11:57,098 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:11:57,101 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:11:57,128 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:11:57,912 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:11:58,547 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:11:58,547 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:12:01,554 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:12:01,554 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:12:01,554 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:12:01,785 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:12:01,789 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:12:01,894 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:12:02,786 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:12:03,423 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:12:03,423 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:17:32,801 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:17:32,804 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:17:32,804 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:17:36,483 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:17:36,484 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:17:36,484 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:17:36,730 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:17:36,776 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:17:36,786 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:17:37,613 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:17:38,246 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:17:38,246 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:17:41,247 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:17:41,248 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:17:41,248 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:17:41,674 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:17:41,706 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:17:41,727 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:17:42,684 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:17:43,330 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:17:43,331 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:17:46,214 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:17:46,215 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:17:46,215 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:17:46,495 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:17:46,510 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:17:46,513 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:17:47,268 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:17:47,906 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:17:47,906 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:25:05,813 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:25:05,814 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:25:05,815 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:25:09,260 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:25:09,260 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:25:09,261 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:25:09,574 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:25:09,581 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:25:09,604 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:25:10,628 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:25:11,288 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:25:11,288 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:25:14,199 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:25:14,199 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:25:14,200 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:25:14,483 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:25:14,486 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:25:14,571 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:25:15,702 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:25:16,356 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:25:16,357 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:25:18,924 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:25:18,924 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:25:18,924 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:25:19,528 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:25:19,539 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:25:19,583 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:25:20,731 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:25:21,367 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:25:21,368 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:25:24,215 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:25:24,215 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:25:24,216 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:25:24,529 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:25:24,626 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:25:24,637 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:25:25,690 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:25:26,324 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:25:26,324 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:25:29,205 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:25:29,205 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:25:29,205 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:25:29,461 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:25:29,475 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:25:29,482 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:25:30,259 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:25:30,898 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:25:30,899 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:26:34,331 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:26:34,331 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:26:34,332 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:26:37,583 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:26:37,583 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:26:37,584 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:26:37,867 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:26:37,906 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:26:37,910 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:26:38,638 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:26:39,279 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:26:39,280 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:26:41,802 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:26:41,802 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:26:41,803 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:26:42,154 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:26:42,189 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:26:42,195 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:26:43,783 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:26:44,496 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:26:44,496 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:34:52,279 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:34:52,282 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:34:52,283 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:34:55,393 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:34:55,394 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:34:55,394 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:34:56,050 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:34:56,058 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:34:56,122 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:34:56,988 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:34:57,705 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:34:57,705 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:35:00,365 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:35:00,365 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:35:00,366 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:35:00,686 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:35:00,693 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:35:00,722 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:35:01,666 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:35:02,315 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:35:02,316 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:35:39,488 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:35:39,491 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:35:39,492 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:35:42,898 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:35:42,899 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:35:42,899 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:35:43,146 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:35:43,150 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:35:43,236 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:35:44,076 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:35:44,713 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:35:44,713 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:35:47,314 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:35:47,314 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:35:47,315 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:35:47,625 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:35:47,642 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:35:47,650 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:35:48,393 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:35:49,031 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:35:49,032 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:35:51,573 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:35:51,573 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:35:51,574 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:35:51,861 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:35:51,897 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:35:51,904 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:35:52,651 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:35:53,300 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:35:53,300 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:36:12,972 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:36:12,973 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:36:12,974 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:36:16,339 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:36:16,340 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:36:16,340 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:36:16,674 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:36:16,680 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:36:16,699 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:36:17,542 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:36:18,183 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:36:18,184 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:36:21,443 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:36:21,443 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:36:21,443 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:36:21,749 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:36:21,771 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:36:21,777 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:36:22,468 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:36:23,108 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:36:23,109 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:36:26,286 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:36:26,287 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:36:26,287 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:36:26,571 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:36:26,577 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:36:26,594 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:36:27,305 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:36:27,951 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:36:27,951 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:36:43,343 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:36:43,344 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:36:43,344 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:36:47,015 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:36:47,015 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:36:47,016 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:36:47,369 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:36:47,394 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:36:47,400 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:36:48,182 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:36:48,822 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:36:48,822 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:36:51,443 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:36:51,443 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:36:51,443 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:36:51,707 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:36:51,712 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:36:51,761 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:36:52,698 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:36:53,330 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:36:53,330 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:36:56,174 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:36:56,175 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:36:56,175 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:36:56,410 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:36:56,414 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:36:56,484 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:36:57,339 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:36:57,977 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:36:57,978 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:37:06,518 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:37:06,520 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:37:06,520 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:37:09,428 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:37:09,428 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:37:09,428 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:37:09,669 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:37:09,676 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:37:09,768 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:37:10,492 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:37:11,145 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:37:11,145 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:37:12,367 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:37:12,368 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:37:12,368 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:37:15,480 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:37:15,480 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:37:15,481 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:37:15,783 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:37:15,789 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:37:15,835 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:37:16,766 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:37:17,414 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:37:17,415 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:37:20,231 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:37:20,232 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:37:20,232 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:37:20,852 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:37:20,861 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:37:20,885 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:37:21,960 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:37:22,602 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:37:22,602 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:37:25,164 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:37:25,164 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:37:25,165 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:37:25,487 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:37:25,494 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:37:25,515 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:37:26,523 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:37:27,182 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:37:27,182 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:40:47,573 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:40:47,574 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:40:47,575 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:40:51,086 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:40:51,087 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:40:51,087 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:40:51,524 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:40:51,535 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:40:51,572 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:40:52,953 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:40:53,590 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:40:53,590 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:40:56,475 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:40:56,475 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:40:56,476 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:40:56,814 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:40:56,820 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:40:56,842 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:40:58,224 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:40:58,883 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:40:58,883 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:41:01,594 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:41:01,594 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:41:01,595 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:41:02,015 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:41:02,040 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:41:02,051 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:41:03,239 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:41:03,883 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:41:03,883 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:41:19,320 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:41:19,322 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:41:19,322 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:41:22,290 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:41:22,291 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:41:22,291 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:41:23,083 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:41:23,089 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:41:23,255 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:41:24,160 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:41:24,810 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:41:24,810 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:41:27,658 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:41:27,659 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:41:27,659 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:41:28,020 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:41:28,030 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:41:28,109 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:41:29,540 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:41:30,186 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:41:30,187 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:41:42,685 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:41:42,687 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:41:42,687 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:41:45,586 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:41:45,587 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:41:45,587 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:41:45,841 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:41:45,847 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:41:45,871 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:41:46,647 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:41:47,282 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:41:47,282 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:41:50,259 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:41:50,263 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:41:50,263 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:41:50,634 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:41:50,649 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:41:50,683 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:41:51,748 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:41:52,399 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:41:52,399 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:41:55,614 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:41:55,614 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:41:55,615 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:41:55,942 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:41:55,997 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:41:56,001 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:41:56,812 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:41:57,445 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:41:57,446 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:42:10,404 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:42:10,405 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:42:10,405 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:42:13,438 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:42:13,438 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:42:13,439 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:42:13,739 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:42:13,743 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:42:13,759 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:42:14,594 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:42:15,243 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:42:15,243 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:42:17,792 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:42:17,792 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:42:17,793 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:42:18,091 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:42:18,106 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:42:18,109 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:42:19,009 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:42:19,649 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:42:19,649 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:42:22,586 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:42:22,587 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:42:22,588 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:42:22,875 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:42:22,927 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:42:22,932 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:42:24,118 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:42:24,773 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:42:24,773 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:42:35,178 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:42:35,179 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:42:35,179 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:42:38,167 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:42:38,167 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:42:38,168 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:42:38,843 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:42:38,949 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:42:38,974 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:42:40,311 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:42:40,995 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:42:40,995 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:42:43,817 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:42:43,818 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:42:43,818 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:42:44,260 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:42:44,271 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:42:44,352 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:42:45,363 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:42:46,035 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:42:46,035 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:42:49,030 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:42:49,032 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:42:49,032 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:42:49,409 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:42:49,474 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:42:49,479 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:42:50,672 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:42:51,312 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:42:51,312 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:42:54,207 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:42:54,208 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:42:54,208 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:42:54,520 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:42:54,524 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:42:54,581 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:42:55,610 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:42:56,258 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:42:56,258 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:42:58,826 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:42:58,826 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:42:58,826 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:42:59,131 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:42:59,151 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:42:59,248 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:43:00,324 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:43:01,115 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:43:01,115 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:43:03,718 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:43:03,718 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:43:03,718 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:43:04,056 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:43:04,128 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:43:04,135 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:43:05,342 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:43:05,984 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:43:05,984 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:44:58,489 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:44:58,491 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:44:58,491 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:45:02,055 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:45:02,055 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:45:02,056 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:45:02,346 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:45:02,361 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:45:02,370 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:45:03,334 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:45:03,985 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:45:03,985 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:46:39,888 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:46:39,891 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:46:39,891 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:46:43,601 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:46:43,601 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:46:43,602 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:46:43,890 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:46:43,896 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:46:43,914 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:46:44,821 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:46:45,464 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:46:45,465 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:48:28,947 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:48:28,953 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:48:28,955 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:48:31,971 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:48:31,971 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:48:31,972 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:48:32,272 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:48:32,288 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:48:32,299 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:48:33,091 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:48:33,728 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:48:33,728 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:48:36,245 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:48:36,245 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:48:36,246 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:48:36,526 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:48:36,546 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:48:36,552 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:48:37,286 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:48:37,924 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:48:37,924 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:49:28,822 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:49:28,824 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:49:28,825 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:49:31,839 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:49:31,840 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:49:31,840 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:49:32,297 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:49:32,306 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:49:32,371 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:49:33,580 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:49:34,222 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:49:34,222 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:49:36,548 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:49:36,549 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:49:36,550 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:49:39,749 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:49:39,750 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:49:39,750 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:49:40,017 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:49:40,021 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:49:40,102 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:49:40,919 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:49:41,556 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:49:41,556 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:49:43,856 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:49:43,894 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:49:43,899 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:49:46,970 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:49:46,970 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:49:46,970 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:49:47,263 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:49:47,270 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:49:47,291 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:49:48,367 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:49:48,999 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:49:48,999 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:49:49,721 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:49:49,721 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:49:49,722 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:49:52,761 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:49:52,761 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:49:52,762 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:49:53,088 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:49:53,092 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:49:53,218 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:49:54,991 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:49:55,626 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:49:55,627 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:49:59,365 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:49:59,365 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:49:59,366 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:49:59,709 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:49:59,731 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:49:59,778 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:50:01,002 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:50:01,645 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:50:01,645 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:50:05,384 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 15:50:05,385 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 15:50:05,386 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 15:50:08,650 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:50:08,651 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:50:08,651 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:50:09,001 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:50:09,021 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:50:09,024 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:50:10,029 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:50:10,679 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:50:10,679 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 15:50:13,810 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 15:50:13,811 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 15:50:13,811 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 15:50:14,166 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 15:50:14,170 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 15:50:14,199 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 15:50:15,038 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 15:50:15,697 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 15:50:15,697 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:00:52,444 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:00:52,445 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:00:52,445 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:00:52,787 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:00:52,813 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:00:52,819 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:00:54,019 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:00:54,674 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:00:54,674 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:18:03,065 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 16:18:03,067 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 16:18:03,068 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 16:18:06,023 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:18:06,023 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:18:06,023 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:18:06,410 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:18:06,435 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:18:06,441 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:18:07,168 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:18:07,810 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:18:07,810 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:18:10,350 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:18:10,350 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:18:10,350 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:18:10,595 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:18:10,602 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:18:10,636 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:18:11,329 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:18:11,969 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:18:11,969 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:18:14,493 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:18:14,494 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:18:14,494 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:18:14,732 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:18:14,773 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:18:14,778 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:18:15,491 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:18:16,124 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:18:16,124 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:25:06,524 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 16:25:06,525 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 16:25:06,525 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 16:25:10,013 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:25:10,013 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:25:10,014 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:25:10,280 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:25:10,286 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:25:10,313 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:25:11,137 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:25:11,770 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:25:11,771 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:38:11,247 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:38:11,247 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:38:11,247 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:38:11,563 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:38:11,597 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:38:11,601 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:38:12,703 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:38:13,362 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:38:13,363 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:51:02,430 - ERROR - app - [main.py:310] - 请求处理异常: object dict can't be used in 'await' expression
2025-05-08 16:51:52,779 - ERROR - app - [main.py:310] - 请求处理异常: object dict can't be used in 'await' expression
2025-05-08 16:52:05,135 - ERROR - app - [main.py:310] - 请求处理异常: object dict can't be used in 'await' expression
2025-05-08 16:52:31,413 - ERROR - app - [main.py:310] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-05-08 16:54:18,992 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 16:54:18,992 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 16:54:18,993 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 16:54:21,723 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:54:21,723 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:54:21,723 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:54:21,960 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:54:22,001 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:54:22,005 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:54:22,693 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:54:23,347 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:54:23,348 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:54:38,111 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 16:54:38,111 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 16:54:38,112 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 16:54:40,823 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:54:40,823 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:54:40,823 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:54:41,061 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:54:41,072 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:54:41,142 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:54:41,854 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:54:42,509 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:54:42,509 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:54:56,484 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 16:54:56,484 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 16:54:56,484 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 16:54:59,318 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:54:59,318 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:54:59,319 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:54:59,615 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:54:59,636 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:54:59,641 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:55:00,270 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:55:00,910 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:55:00,910 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:55:03,788 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:55:03,789 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:55:03,789 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:55:04,066 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:55:04,080 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:55:04,084 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:55:04,832 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:55:05,464 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:55:05,464 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:55:24,798 - ERROR - app - [main.py:310] - 请求处理异常: object dict can't be used in 'await' expression
2025-05-08 16:59:10,932 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 16:59:10,933 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 16:59:10,935 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 16:59:13,970 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:59:13,971 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:59:13,971 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:59:14,226 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:59:14,230 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:59:14,302 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:59:14,967 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:59:15,601 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:59:15,601 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:59:46,531 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 16:59:46,532 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 16:59:46,532 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 16:59:49,477 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:59:49,478 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:59:49,478 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:59:49,747 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:59:49,767 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:59:49,771 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:59:50,592 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:59:51,226 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:59:51,226 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 16:59:54,070 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 16:59:54,070 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 16:59:54,071 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 16:59:54,321 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 16:59:54,328 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 16:59:54,570 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 16:59:55,446 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 16:59:56,082 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 16:59:56,082 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:00:01,030 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:00:01,031 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:00:01,031 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:00:03,972 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:00:03,973 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:00:03,973 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:00:04,247 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:00:04,267 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:00:04,271 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:00:04,977 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:00:05,635 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:00:05,636 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:00:06,922 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:00:06,922 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:00:06,923 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:00:09,615 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:00:09,616 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:00:09,616 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:00:09,936 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:00:10,126 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:00:10,199 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:00:10,912 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:00:11,581 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:00:11,582 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:00:14,199 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:00:14,199 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:00:14,200 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:00:14,446 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:00:14,451 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:00:14,497 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:00:15,678 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:00:16,316 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:00:16,317 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:00:39,125 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:00:39,125 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:00:39,125 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:00:43,022 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:00:43,023 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:00:43,023 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:00:43,278 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:00:43,297 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:00:43,301 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:00:44,034 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:00:44,666 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:00:44,666 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:00:47,633 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:00:47,634 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:00:47,634 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:00:47,871 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:00:47,875 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:00:47,922 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:00:48,904 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:00:49,537 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:00:49,537 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:00:52,467 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:00:52,467 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:00:52,468 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:00:53,181 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:00:53,195 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:00:53,201 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:00:54,059 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:00:54,702 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:00:54,702 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:06:34,055 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:06:34,056 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:06:34,057 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:06:37,000 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:06:37,000 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:06:37,001 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:06:37,239 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:06:37,292 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:06:37,297 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:06:38,100 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:06:38,737 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:06:38,737 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:06:41,502 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:06:41,503 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:06:41,503 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:06:41,817 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:06:41,872 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:06:41,876 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:06:42,620 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:06:43,251 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:06:43,251 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:06:54,024 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:06:54,024 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:06:54,024 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:06:56,957 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:06:56,957 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:06:56,958 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:06:57,268 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:06:57,274 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:06:57,294 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:06:58,139 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:06:58,773 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:06:58,773 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:07:01,647 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:07:01,647 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:07:01,648 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:07:01,915 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:07:01,922 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:07:01,940 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:07:02,741 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:07:03,376 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:07:03,376 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:07:05,919 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:07:05,919 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:07:05,920 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:07:06,178 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:07:06,272 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:07:06,276 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:07:06,942 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:07:07,574 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:07:07,575 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:12:50,552 - ERROR - app - [main.py:310] - 请求处理异常: object dict can't be used in 'await' expression
2025-05-08 17:13:12,012 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:13:12,013 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:13:12,014 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:13:15,208 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:13:15,208 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:13:15,208 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:13:15,487 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:13:15,495 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:13:15,514 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:13:16,222 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:13:16,857 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:13:16,858 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:13:19,858 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:13:19,859 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:13:19,859 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:13:20,086 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:13:20,160 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:13:20,165 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:13:20,898 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:13:21,536 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:13:21,537 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:13:24,411 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:13:24,411 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:13:24,411 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:13:24,659 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:13:24,665 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:13:24,684 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:13:25,444 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:13:26,078 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:13:26,078 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:14:27,885 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:14:27,886 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:14:27,887 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:14:31,427 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:14:31,428 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:14:31,428 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:14:31,688 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:14:31,691 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:14:31,714 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:14:32,376 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:14:33,010 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:14:33,010 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:15:26,854 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:15:26,855 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:15:26,855 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:15:29,842 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:15:29,842 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:15:29,843 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:15:30,096 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:15:30,102 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:15:30,143 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:15:30,873 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:15:31,511 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:15:31,511 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:15:34,421 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:15:34,421 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:15:34,421 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:15:34,691 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:15:34,696 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:15:34,719 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:15:35,415 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:15:36,061 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:15:36,061 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:15:38,918 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:15:38,919 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:15:38,919 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:15:39,172 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:15:39,177 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:15:39,218 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:15:39,858 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:15:40,490 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:15:40,490 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:28:49,189 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:28:49,191 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:28:49,191 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:28:52,159 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:28:52,159 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:28:52,159 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:28:52,399 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:28:52,432 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:28:52,437 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:28:53,073 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:28:53,921 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:28:53,921 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:28:56,526 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:28:56,526 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:28:56,527 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:28:56,835 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:28:56,855 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:28:56,859 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:28:57,647 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:28:58,278 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:28:58,278 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:30:03,649 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:30:03,651 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:30:03,652 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:30:06,631 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:30:06,631 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:30:06,632 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:30:06,853 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:30:06,872 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:30:06,878 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:30:07,589 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:30:08,292 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:30:08,294 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:30:08,902 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:30:08,902 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:30:08,903 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:30:11,496 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:30:11,496 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:30:11,497 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:30:11,781 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:30:11,809 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:30:11,813 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:30:12,403 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:30:13,036 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:30:13,037 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:30:15,912 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:30:15,912 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:30:15,912 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:30:16,183 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:30:16,190 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:30:16,209 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:30:16,912 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:30:17,547 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:30:17,548 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:30:29,025 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:30:29,026 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:30:29,027 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:30:31,910 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:30:31,910 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:30:31,911 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:30:32,172 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:30:32,177 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:30:32,233 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:30:32,892 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:30:33,524 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:30:33,524 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:30:34,436 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:30:34,436 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:30:34,437 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:30:37,335 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:30:37,336 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:30:37,336 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:30:37,581 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:30:37,595 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:30:37,603 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:30:38,357 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:30:38,992 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:30:38,993 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:30:41,599 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:30:41,599 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:30:41,599 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:30:41,865 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:30:41,868 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:30:41,891 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:30:42,480 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:30:43,120 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:30:43,120 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:30:55,020 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:30:55,020 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:30:55,021 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:30:58,325 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:30:58,326 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:30:58,326 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:30:58,577 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:30:58,581 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:30:58,606 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:30:59,562 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:31:00,198 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:31:00,199 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:31:03,142 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:31:03,143 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:31:03,143 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:31:03,376 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:31:03,380 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:31:03,418 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:31:04,140 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:31:04,777 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:31:04,778 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:31:07,355 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:31:07,356 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:31:07,356 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:31:07,636 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:31:07,676 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:31:07,680 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:31:08,372 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:31:09,006 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:31:09,007 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:33:42,197 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:33:42,197 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:33:42,198 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:33:45,535 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:33:45,535 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:33:45,535 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:33:45,821 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:33:45,862 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:33:45,866 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:33:46,679 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:33:47,311 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:33:47,311 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:37:24,131 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:37:24,133 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:37:24,133 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:37:27,089 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:37:27,089 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:37:27,090 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:37:27,317 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:37:27,600 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:37:27,604 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:37:28,298 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:37:28,932 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:37:28,933 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:37:31,849 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:37:31,849 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:37:31,850 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:37:32,111 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:37:32,117 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:37:32,176 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:37:32,903 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:37:33,538 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:37:33,539 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:37:53,934 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:37:53,935 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:37:53,936 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:37:56,747 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:37:56,747 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:37:56,748 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:37:57,024 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:37:57,038 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:37:57,042 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:37:57,685 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:37:58,320 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:37:58,320 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:38:01,253 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:38:01,253 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:38:01,253 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:38:01,481 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:38:01,542 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:38:01,552 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:38:02,508 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:38:03,168 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:38:03,169 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:38:17,396 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:38:17,398 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:38:17,398 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:38:20,620 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:38:20,621 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:38:20,621 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:38:20,893 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:38:20,917 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:38:20,926 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:38:21,593 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:38:22,226 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:38:22,226 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:38:24,745 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:38:24,746 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:38:24,746 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:38:25,014 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:38:25,017 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:38:25,034 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:38:25,941 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:38:26,584 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:38:26,585 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:38:29,625 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:38:29,625 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:38:29,626 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:38:29,883 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:38:29,886 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:38:29,924 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:38:30,807 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:38:31,453 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:38:31,455 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:38:39,855 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:38:39,857 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:38:39,857 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:38:42,936 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:38:42,936 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:38:42,937 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:38:43,318 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:38:43,329 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:38:43,354 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:38:44,198 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:38:44,835 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:38:44,835 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:38:47,798 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:38:47,798 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:38:47,798 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:38:48,046 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:38:48,053 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:38:48,089 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:38:48,765 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:38:49,407 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:38:49,407 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:38:58,280 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:38:58,280 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:38:58,281 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:39:01,115 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:39:01,116 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:39:01,116 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:39:01,343 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:39:01,385 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:39:01,389 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:39:02,185 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:39:02,821 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:39:02,821 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:39:05,730 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:39:05,731 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:39:05,732 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:39:06,023 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:39:06,043 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:39:06,048 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:39:06,712 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:39:07,351 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:39:07,351 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:39:15,802 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:39:15,803 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:39:15,803 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:39:18,731 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:39:18,732 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:39:18,736 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:39:19,256 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:39:19,261 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:39:19,307 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:39:19,946 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:39:20,580 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:39:20,580 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:39:43,626 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:39:43,627 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:39:43,627 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:39:46,785 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:39:46,785 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:39:46,785 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:39:47,051 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:39:47,101 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:39:47,104 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:39:48,087 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:39:48,734 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:39:48,735 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:39:51,389 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:39:51,390 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:39:51,390 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:39:51,639 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:39:51,645 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:39:51,670 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:39:52,657 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:39:53,293 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:39:53,294 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:40:39,114 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:40:39,114 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:40:39,115 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:40:41,993 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:40:41,993 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:40:41,994 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:40:42,239 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:40:42,258 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:40:42,261 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:40:43,043 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:40:43,675 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:40:43,675 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:40:46,514 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:40:46,515 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:40:46,515 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:40:46,748 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:40:46,754 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:40:46,784 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:40:47,499 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:40:48,137 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:40:48,138 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:41:36,804 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:41:36,805 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:41:36,805 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:41:39,865 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:41:39,866 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:41:39,866 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:41:40,135 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:41:40,140 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:41:40,173 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:41:40,841 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:41:41,492 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:41:41,492 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:41:44,839 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:41:44,839 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:41:44,840 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:41:45,099 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:41:45,124 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:41:45,130 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:41:46,049 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:41:46,688 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:41:46,688 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:42:43,419 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-08 17:42:43,422 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-08 17:42:43,422 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-08 17:42:46,366 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:42:46,366 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:42:46,367 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:42:46,667 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:42:46,672 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:42:46,689 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:42:47,475 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:42:48,113 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:42:48,114 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:42:51,173 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:42:51,174 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:42:51,174 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:42:51,469 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:42:51,475 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:42:51,488 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:42:52,179 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:42:52,838 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:42:52,839 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 17:54:39,961 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 17:54:39,961 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 17:54:39,961 - INFO - app - [main.py:438] - 本地文件存储服务初始化完成
2025-05-08 17:54:40,216 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 17:54:40,233 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-08 17:54:40,237 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 17:54:40,962 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-08 17:54:41,623 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-08 17:54:41,625 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-08 21:04:50,330 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 21:04:50,331 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 21:04:50,333 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-08 21:05:50,334 - ERROR - app - [main.py:421] - 数据库初始化失败: 
2025-05-08 21:05:50,337 - WARNING - app.config - [config.py:284] - 无法加载动态配置: 
2025-05-08 21:05:50,338 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 21:05:50,338 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 21:06:30,024 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-08 21:06:30,024 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-08 21:06:30,027 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-08 21:07:30,029 - ERROR - app - [main.py:421] - 数据库初始化失败: 
2025-05-08 21:07:30,033 - WARNING - app.config - [config.py:284] - 无法加载动态配置: 
2025-05-08 21:07:30,033 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-08 21:07:30,033 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-08 21:08:30,045 - INFO - app - [main.py:496] - 数据库连接状态: unhealthy
2025-05-08 21:08:30,048 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-11 11:58:35,604 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-11 11:58:35,607 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-11 11:58:35,608 - INFO - app - [main.py:530] - 应用已完全关闭

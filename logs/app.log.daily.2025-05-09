2025-05-09 09:19:18,773 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 09:19:18,774 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 09:19:18,777 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 09:19:19,043 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 09:19:19,058 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 09:19:19,065 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 09:19:20,009 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 09:19:20,669 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 09:19:20,671 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:12:10,013 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:12:10,014 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:12:10,015 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:12:13,521 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:12:13,521 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:12:13,524 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:12:13,959 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:12:13,999 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:12:14,030 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:12:14,836 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:12:15,477 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:12:15,477 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:12:24,521 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:12:24,522 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:12:24,522 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:12:27,519 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:12:27,519 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:12:27,522 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:12:27,793 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:12:27,811 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:12:27,818 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:12:28,437 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:12:29,092 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:12:29,092 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:12:37,505 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:12:37,506 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:12:37,506 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:12:40,784 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:12:40,785 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:12:40,789 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:12:41,053 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:12:41,098 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:12:41,101 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:12:41,838 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:12:42,480 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:12:42,481 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:13:23,503 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:13:23,504 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:13:23,504 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:13:26,277 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:13:26,277 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:13:26,280 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:13:26,570 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:13:26,574 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:13:26,599 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:13:27,408 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:13:28,047 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:13:28,047 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:13:30,687 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:13:30,687 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:13:30,689 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:13:30,921 - INFO - app - [main.py:557] - 使用端口: 8001
2025-05-09 10:13:30,985 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:13:31,002 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:13:31,005 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:13:31,693 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:13:32,171 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:13:32,171 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:13:32,171 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:13:32,320 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:13:32,332 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:13:32,332 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:13:32,341 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:13:32,347 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:13:33,104 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:13:33,736 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:13:33,736 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:13:47,435 - ERROR - app - [main.py:310] - 请求处理异常: 用户名或密码错误
2025-05-09 10:18:23,627 - ERROR - app - [main.py:310] - 请求处理异常: 用户名或密码错误
2025-05-09 10:22:30,675 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:22:30,677 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:22:30,677 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:22:30,724 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:22:30,725 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:22:30,725 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:22:32,221 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:22:32,221 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:22:32,222 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:22:32,464 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:22:32,504 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:22:32,510 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:22:33,322 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:22:33,323 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:22:33,326 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:22:33,690 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:22:33,692 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:22:33,701 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:22:33,719 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:22:34,334 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:22:34,334 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:22:34,783 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:22:35,425 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:22:35,426 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:22:41,211 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:22:41,211 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:22:41,211 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:22:41,289 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:22:41,290 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:22:41,290 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:22:42,537 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:22:42,537 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:22:42,538 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:22:42,764 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:22:42,779 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:22:42,824 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:22:43,957 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:22:43,957 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:22:43,959 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:22:44,204 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:22:44,276 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:22:44,280 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:22:44,396 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:22:44,856 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:22:44,857 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:22:45,764 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:22:46,406 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:22:46,407 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:22:59,639 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:22:59,640 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:22:59,640 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:22:59,644 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:22:59,644 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:22:59,644 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:23:02,143 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:23:02,143 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:23:02,145 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:23:02,448 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:23:02,490 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:23:02,497 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:23:02,621 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:23:02,621 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:23:02,623 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:23:02,938 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:23:02,953 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:23:03,037 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:23:03,669 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:23:04,336 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:23:04,336 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:23:04,517 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:23:05,163 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:23:05,163 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:23:07,752 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:23:07,753 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:23:07,754 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:23:08,084 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:23:08,089 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:23:08,257 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:23:09,819 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:23:10,481 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:23:10,481 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:23:11,135 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:23:11,135 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:23:11,135 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:23:12,128 - INFO - app - [main.py:557] - 使用端口: 8001
2025-05-09 10:23:13,326 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:23:13,326 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:23:13,327 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:23:13,579 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:23:13,585 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:23:13,692 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:23:15,264 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:23:15,905 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:23:15,905 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:23:18,201 - ERROR - app - [main.py:310] - 请求处理异常: 用户名或密码错误
2025-05-09 10:23:37,349 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:23:37,351 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:23:37,351 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:23:37,398 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:23:37,399 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:23:37,399 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:23:38,869 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:23:38,869 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:23:38,871 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:23:39,168 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:23:39,180 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:23:39,221 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:23:40,101 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:23:40,102 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:23:40,104 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:23:40,554 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:23:40,561 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:23:40,627 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:23:40,842 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:23:41,516 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:23:41,516 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:23:42,324 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:23:42,989 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:23:42,990 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:23:54,346 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:23:54,346 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:23:54,346 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:23:54,424 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:23:54,424 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:23:54,425 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:23:57,385 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:23:57,385 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:23:57,389 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:23:57,768 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:23:57,794 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:23:57,841 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:23:57,882 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:23:57,882 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:23:57,883 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:23:58,273 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:23:58,313 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:23:58,322 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:23:59,644 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:23:59,869 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:24:00,318 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:24:00,319 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:24:00,520 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:24:00,520 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:24:02,991 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:24:02,991 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:24:02,996 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:24:03,479 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:24:03,489 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:24:03,565 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:24:05,028 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:24:05,678 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:24:05,678 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:24:05,678 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:24:05,699 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:24:05,700 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:24:06,968 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:24:06,968 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:24:06,969 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:24:07,244 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:24:07,366 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:24:07,383 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:24:08,387 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:24:08,387 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:24:08,389 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:24:08,981 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:24:09,032 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:24:09,059 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:24:09,098 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:24:09,757 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:24:09,757 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:24:10,646 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:24:11,301 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:24:11,301 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:24:11,301 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:24:11,301 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:24:11,301 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:24:11,543 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:24:11,548 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:24:11,593 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:24:13,345 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:24:13,945 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:24:13,946 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:24:13,949 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:24:14,021 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:24:14,022 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:24:14,487 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:24:14,514 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:24:14,524 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:24:15,046 - INFO - app - [main.py:557] - 使用端口: 8001
2025-05-09 10:24:16,256 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:24:16,257 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:24:16,257 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:24:16,315 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:24:16,533 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:24:16,599 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:24:16,611 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:24:16,998 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:24:16,998 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:24:18,651 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:24:19,315 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:24:19,315 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:24:19,652 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:24:19,652 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:24:19,654 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:24:20,177 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:24:20,187 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:24:20,229 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:24:22,223 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:24:22,888 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:24:22,889 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:12,118 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:34:12,120 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:34:12,120 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:34:12,141 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:34:12,141 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:34:12,142 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:34:13,686 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:13,686 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:13,688 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:14,135 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:14,171 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:14,183 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:14,880 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:14,881 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:14,882 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:15,473 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:15,504 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:15,535 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:16,088 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:16,735 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:16,735 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:16,765 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:17,439 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:17,439 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:24,610 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:34:24,612 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:34:24,612 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:34:24,645 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:34:24,646 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:34:24,646 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:34:26,379 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:26,379 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:26,380 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:26,724 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:26,736 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:26,900 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:27,351 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:27,351 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:27,353 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:27,722 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:27,758 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:27,767 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:28,260 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:28,903 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:28,904 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:29,225 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:29,908 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:29,909 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:32,600 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:32,600 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:32,602 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:33,021 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:33,026 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:33,229 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:34,621 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:35,286 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:35,286 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:36,883 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:34:36,883 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:34:36,884 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:34:36,904 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:34:36,905 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:34:36,905 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:34:39,143 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:39,143 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:39,144 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:39,401 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:39,415 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:39,418 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:39,650 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:39,650 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:39,652 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:40,187 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:40,251 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:40,306 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:40,941 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:41,594 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:41,594 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:42,078 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:42,734 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:42,734 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:42,815 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:42,815 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:42,816 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:43,096 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:43,168 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:43,178 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:44,605 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:45,266 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:45,266 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:45,507 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:45,507 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:45,509 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:46,014 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:46,060 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:46,073 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:47,579 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:48,235 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:48,235 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:50,841 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:50,841 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:50,843 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:51,156 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:51,159 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:51,200 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:52,177 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:52,826 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:52,826 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:53,837 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:34:53,839 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:34:53,839 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:34:53,893 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 10:34:53,893 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 10:34:53,893 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 10:34:55,152 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:55,152 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:55,153 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:55,337 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:55,410 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:55,414 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:56,156 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:56,518 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:56,519 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:56,521 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:56,813 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:56,813 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:56,859 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:56,862 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:56,939 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:57,814 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:58,022 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:34:58,022 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:34:58,023 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:34:58,187 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:34:58,191 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:34:58,217 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:34:58,449 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:58,449 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:34:59,019 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:34:59,647 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:34:59,647 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:35:00,891 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:35:00,891 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:35:00,892 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:35:01,057 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:35:01,061 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:35:01,102 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:35:01,102 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:35:01,104 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:35:01,115 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:35:01,385 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:35:01,434 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:35:01,438 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:35:01,968 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:35:02,283 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:35:02,598 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:35:02,598 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:35:02,923 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:35:02,923 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 10:35:05,554 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 10:35:05,554 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 10:35:05,559 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 10:35:05,885 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 10:35:05,892 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 10:35:05,931 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 10:35:06,774 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 10:35:07,423 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 10:35:07,423 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:05:51,126 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:05:51,127 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:05:51,127 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:05:51,141 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:05:51,142 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:05:51,142 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:05:52,808 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:05:52,808 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:05:52,810 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:05:53,042 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:05:53,063 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:05:53,067 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:05:53,861 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:05:53,862 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:05:53,864 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:05:53,877 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:05:54,528 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:05:54,532 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:05:55,848 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:05:55,857 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:05:55,858 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:05:57,213 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:05:57,854 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:05:57,854 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:06:14,139 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:06:14,140 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:06:14,141 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:06:14,148 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:06:14,148 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:06:14,148 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:06:16,225 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:06:16,225 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:06:16,227 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:06:16,388 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:06:16,407 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:06:16,410 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:06:17,059 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:06:17,060 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:06:17,062 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:06:17,136 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:06:17,302 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:06:17,365 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:06:17,370 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:06:17,766 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:06:17,766 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:06:18,045 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:06:18,684 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:06:18,684 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:06:21,343 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:06:21,344 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:06:21,346 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:06:21,635 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:06:21,644 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:06:21,664 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:06:22,372 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:06:22,998 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:06:22,998 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:06:42,819 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:06:42,820 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:06:42,820 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:06:42,820 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:06:42,820 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:06:42,820 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:06:45,695 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:06:45,695 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:06:45,698 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:06:45,965 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:06:45,971 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:06:45,989 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:06:46,431 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:06:46,431 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:06:46,431 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:06:46,596 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:06:46,608 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:06:46,644 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:06:46,864 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:06:47,497 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:06:47,497 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:06:47,521 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:06:48,150 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:06:48,150 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:06:50,146 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:06:50,146 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:06:50,149 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:06:50,440 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:06:50,446 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:06:50,469 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:06:51,356 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:06:52,011 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:06:52,011 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:06:54,586 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:06:54,587 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:06:54,590 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:06:54,832 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:06:54,836 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:06:54,915 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:06:55,628 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:06:56,261 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:06:56,262 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:07:05,941 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:07:05,942 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:07:05,942 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:07:05,998 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:07:05,999 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:07:05,999 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:07:08,729 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:07:08,730 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:07:08,733 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:07:08,852 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:07:08,852 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:07:08,853 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:07:09,113 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:07:09,147 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:07:09,151 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:07:09,167 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:07:09,184 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:07:09,189 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:07:09,828 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:07:09,905 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:07:10,455 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:07:10,455 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:07:10,543 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:07:10,543 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:07:13,177 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:07:13,178 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:07:13,182 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:07:13,432 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:07:13,474 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:07:13,478 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:07:14,376 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:07:15,056 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:07:15,056 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:07:17,768 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:07:17,768 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:07:17,772 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:07:18,037 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:07:18,118 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:07:18,121 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:07:19,011 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:07:19,643 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:07:19,643 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:07:22,471 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:07:22,472 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:07:22,472 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:07:22,493 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:07:22,493 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:07:22,493 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:23:18,488 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:23:18,489 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:23:18,491 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:23:18,831 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:23:18,865 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:23:18,872 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:23:19,595 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:23:20,228 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:23:20,228 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:23:20,266 - ERROR - app - [main.py:310] - 请求处理异常: module 'jose.jwt' has no attribute 'PyJWTError'
2025-05-09 11:23:26,399 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:23:26,399 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:23:26,400 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:23:29,210 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:23:29,210 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:23:29,213 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:23:29,472 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:23:29,479 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:23:29,500 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:23:30,330 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:23:31,043 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:23:31,045 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:23:40,644 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:23:40,645 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:23:40,645 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:23:43,514 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:23:43,514 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:23:43,518 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:23:43,836 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:23:43,852 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:23:43,860 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:23:44,735 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:23:45,380 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:23:45,380 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:23:48,165 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:23:48,166 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:23:48,168 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:23:48,456 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:23:48,460 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:23:48,508 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:23:49,354 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:23:49,993 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:23:49,994 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:23:50,601 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:23:50,601 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:23:50,602 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:23:53,456 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:23:53,457 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:23:53,459 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:23:53,771 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:23:53,793 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:23:53,797 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:23:54,682 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:23:55,324 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:23:55,324 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:23:58,868 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:23:58,869 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:23:58,869 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:24:01,693 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:24:01,694 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:24:01,696 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:24:01,957 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:24:01,961 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:24:02,017 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:24:02,822 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:24:03,498 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:24:03,498 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:25:58,760 - INFO - app - [main.py:557] - 使用端口: 8001
2025-05-09 11:25:59,994 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:25:59,994 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:25:59,997 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:26:00,338 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:26:00,355 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:26:00,365 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:26:01,274 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:26:01,908 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:26:01,908 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:26:32,307 - ERROR - app - [main.py:310] - 请求处理异常: module 'jose.jwt' has no attribute 'PyJWTError'
2025-05-09 11:26:51,393 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:27:04,301 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:27:04,302 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:27:04,303 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:27:04,343 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:27:04,343 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:27:04,343 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:27:06,219 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:27:06,219 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:27:06,221 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:27:06,431 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:27:06,453 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:27:06,458 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:27:07,082 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:27:07,082 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:27:07,084 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:27:07,248 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:27:07,446 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:27:07,513 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:27:07,521 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:27:07,881 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:27:07,882 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:27:08,561 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:27:09,209 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:27:09,210 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:27:11,341 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:27:11,341 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:27:11,341 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:27:12,140 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:27:12,141 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:27:12,143 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:27:12,405 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:27:12,410 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:27:12,462 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:27:12,857 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:27:12,857 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:27:12,858 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:27:13,130 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:27:13,137 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:27:13,159 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:27:13,551 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:27:13,993 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:27:14,191 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:27:14,191 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:27:14,630 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:27:14,631 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:27:16,793 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:27:16,793 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:27:16,796 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:27:17,069 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:27:17,072 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:27:17,100 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:27:18,068 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:27:18,711 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:27:18,711 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:27:19,015 - INFO - app - [main.py:557] - 使用端口: 8002
2025-05-09 11:27:20,293 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:27:20,293 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:27:20,294 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:27:20,543 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:27:20,546 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:27:20,564 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:27:21,296 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:27:21,924 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:27:21,924 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:27:26,059 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:27:44,089 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:27:44,090 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:27:44,090 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:27:44,187 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:27:44,187 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:27:44,187 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:27:45,794 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:27:45,794 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:27:45,797 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:27:46,017 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:27:46,021 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:27:46,079 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:27:46,889 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:27:46,889 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:27:46,891 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:27:47,509 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:27:47,732 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:27:47,740 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:27:47,764 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:27:48,188 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:27:48,188 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:27:50,691 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:27:51,398 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:27:51,399 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:28:05,016 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:28:05,017 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:28:05,017 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:28:05,022 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:28:05,023 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:28:05,023 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:28:06,509 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:28:06,509 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:28:06,511 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:28:06,844 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:28:06,874 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:28:06,955 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:28:07,868 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:28:07,868 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:28:07,871 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:28:07,950 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:28:08,170 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:28:08,193 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:28:08,197 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:28:08,585 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:28:08,585 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:28:09,155 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:28:09,795 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:28:09,796 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:28:19,710 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:28:19,711 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:28:19,711 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:28:19,714 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:28:19,714 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:28:19,714 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:28:21,606 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:28:21,606 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:28:21,608 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:28:21,802 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:28:21,805 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:28:21,831 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:28:22,811 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:28:22,954 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:28:22,954 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:28:22,957 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:28:23,238 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:28:23,245 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:28:23,264 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:28:23,452 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:28:23,452 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:28:23,956 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:28:23,956 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:28:23,956 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:28:24,103 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:28:24,742 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:28:24,743 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:28:26,065 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:28:26,065 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:28:26,066 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:28:26,242 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:28:26,252 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:28:26,309 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:28:27,095 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:28:27,772 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:28:27,773 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:28:27,868 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:28:27,869 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:28:27,871 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:28:28,162 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:28:28,210 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:28:28,218 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:28:29,030 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:28:29,671 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:28:29,672 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:28:32,339 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:28:32,339 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:28:32,341 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:28:32,744 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:28:32,754 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:28:32,817 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:28:33,647 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:28:34,281 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:28:34,281 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:28:34,653 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:29:01,428 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:29:01,429 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:29:01,429 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:29:01,429 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:29:01,432 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:29:01,432 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:29:03,334 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:29:03,334 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:29:03,336 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:29:03,537 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:29:03,560 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:29:03,565 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:29:04,448 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:29:04,765 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:29:04,766 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:29:04,768 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:29:05,079 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:29:05,079 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:29:05,102 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:29:05,118 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:29:05,124 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:29:06,247 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:29:06,673 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:29:06,673 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:29:06,674 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:29:06,867 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:29:06,901 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:29:06,902 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:29:06,912 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:29:06,920 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:29:08,032 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:29:08,676 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:29:08,677 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:29:09,730 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:29:09,730 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:29:09,732 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:29:09,965 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:29:09,969 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:29:10,042 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:29:10,696 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:29:10,697 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:29:10,697 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:29:10,849 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:29:11,486 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:29:11,487 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:29:12,073 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:29:12,073 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:29:12,073 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:29:12,303 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:29:12,307 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:29:12,327 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:29:13,118 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:29:13,762 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:29:13,763 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:29:14,611 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:29:14,612 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:29:14,614 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:29:15,099 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:29:15,163 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:29:15,168 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:29:16,237 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:29:16,894 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:29:16,894 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:29:17,295 - INFO - app - [main.py:557] - 使用端口: 8003
2025-05-09 11:29:18,528 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:29:18,529 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:29:18,530 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:29:18,801 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:29:18,815 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:29:18,821 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:29:19,749 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:29:20,389 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:29:20,389 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:29:51,343 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:30:25,669 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:30:25,670 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:30:25,671 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:30:25,712 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:30:25,712 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:30:25,712 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:30:27,324 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:30:27,325 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:30:27,327 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:30:27,510 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:30:27,514 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:30:27,543 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:30:28,304 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:30:28,549 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:30:28,549 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:30:28,551 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:30:28,803 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:30:28,836 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:30:28,842 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:30:28,938 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:30:28,938 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:30:29,749 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:30:30,394 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:30:30,395 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:31:01,388 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:31:01,389 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:31:01,390 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:31:01,390 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:31:01,391 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:31:01,391 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:31:03,005 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:31:03,005 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:31:03,008 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:31:03,210 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:31:03,234 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:31:03,239 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:31:03,870 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:31:04,209 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:31:04,210 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:31:04,212 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:31:04,493 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:31:04,498 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:31:04,500 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:31:04,500 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:31:04,515 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:31:05,173 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:31:05,807 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:31:05,808 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:31:14,491 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:31:14,491 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:31:14,492 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:31:14,531 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:31:14,532 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:31:14,532 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:31:16,125 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:31:16,125 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:31:16,126 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:31:16,329 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:31:16,349 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:31:16,353 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:31:16,998 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:31:17,374 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:31:17,374 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:31:17,376 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:31:17,630 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:31:17,630 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:31:17,702 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:31:17,733 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:31:17,740 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:31:18,710 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:31:19,346 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:31:19,346 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:31:26,458 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:31:26,458 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:31:26,458 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:31:26,479 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:31:26,480 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:31:26,480 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:31:28,132 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:31:28,133 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:31:28,135 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:31:28,335 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:31:28,360 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:31:28,366 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:31:29,305 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:31:29,445 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:31:29,445 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:31:29,447 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:31:29,722 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:31:29,800 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:31:29,804 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:31:29,948 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:31:29,948 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:31:31,169 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:31:31,244 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:31:31,244 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:31:31,245 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:31:31,398 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:31:31,401 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:31:31,428 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:31:31,829 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:31:31,830 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:31:32,133 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:31:32,784 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:31:32,785 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:31:34,671 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:31:34,671 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:31:34,673 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:31:34,928 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:31:34,934 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:31:34,954 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:31:35,736 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:31:36,375 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:31:36,376 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:31:42,036 - INFO - app - [main.py:557] - 使用端口: 8004
2025-05-09 11:31:43,465 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:31:43,466 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:31:43,466 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:31:43,662 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:31:43,687 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:31:43,692 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:31:44,447 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:31:45,092 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:31:45,092 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:32:00,145 - INFO - app - [main.py:557] - 使用端口: 8005
2025-05-09 11:32:01,375 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:32:01,375 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:32:01,378 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:32:01,602 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:32:01,610 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:32:01,637 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:32:02,341 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:32:02,979 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:32:02,979 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:33:52,355 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:33:52,357 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:33:52,357 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:33:52,429 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:33:52,430 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:33:52,430 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:33:54,223 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:33:54,223 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:33:54,225 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:33:54,584 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:33:54,601 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:33:54,606 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:33:55,518 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:33:55,623 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:33:55,623 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:33:55,625 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:33:55,935 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:33:55,952 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:33:55,958 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:33:56,163 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:33:56,163 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:33:56,983 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:33:57,643 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:33:57,644 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:33:57,784 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:33:57,784 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:33:57,785 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:33:57,996 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:33:58,012 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:33:58,016 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:33:58,816 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:33:59,450 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:33:59,450 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:34:00,248 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:34:00,248 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:34:00,250 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:34:00,653 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:34:00,666 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:34:00,725 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:34:01,581 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:34:02,236 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:34:02,237 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:34:05,155 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:34:05,156 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:34:05,158 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:34:05,480 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:34:05,499 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:34:05,513 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:34:06,493 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:34:07,135 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:34:07,135 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:35:13,568 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:35:13,568 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:35:13,568 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:35:13,696 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:35:13,697 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:35:13,697 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:35:15,150 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:35:15,150 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:35:15,152 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:35:15,389 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:35:15,405 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:35:15,409 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:35:16,249 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:35:16,387 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:35:16,387 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:35:16,390 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:35:16,676 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:35:16,680 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:35:16,715 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:35:16,881 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:35:16,881 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:35:17,424 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:35:18,059 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:35:18,059 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:35:34,437 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:35:34,440 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:35:34,441 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:35:34,462 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:35:34,463 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:35:34,463 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:35:35,978 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:35:35,978 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:35:35,981 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:35:36,156 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:35:36,163 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:35:36,207 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:35:36,850 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:35:37,145 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:35:37,145 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:35:37,147 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:35:37,415 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:35:37,446 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:35:37,451 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:35:37,477 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:35:37,477 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:35:38,153 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:35:38,790 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:35:38,790 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:41:22,319 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:41:22,320 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:41:22,320 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:41:22,322 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:41:22,323 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:41:22,323 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:41:24,232 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:41:24,233 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:41:24,235 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:41:24,459 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:41:24,479 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:41:24,483 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:41:25,100 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:41:25,100 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:41:25,102 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:41:25,354 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:41:25,412 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:41:25,419 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:41:25,436 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:41:25,986 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:41:25,986 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:41:26,166 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:41:26,799 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:41:26,799 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:41:29,501 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:41:29,501 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:41:29,503 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:41:29,740 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:41:29,748 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:41:29,799 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:41:30,868 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:41:31,518 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:41:31,518 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:41:36,398 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:41:36,399 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:41:36,399 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:41:36,496 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:41:36,496 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:41:36,497 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:41:37,744 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:41:37,744 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:41:37,745 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:41:38,026 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:41:38,037 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:41:38,113 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:41:39,034 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:41:39,469 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:41:39,470 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:41:39,472 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:41:39,661 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:41:39,661 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:41:39,726 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:41:39,733 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:41:39,757 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:41:40,555 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:41:41,186 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:41:41,187 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:41:46,433 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:41:46,434 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:41:46,434 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:41:46,441 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:41:46,441 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:41:46,442 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:41:47,751 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:41:47,752 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:41:47,753 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:41:47,921 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:41:47,968 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:41:47,972 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:41:48,715 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:41:49,346 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:41:49,346 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:41:49,367 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:41:49,368 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:41:49,370 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:41:49,655 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:41:49,659 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:41:49,679 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:41:50,357 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:41:50,993 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:41:50,994 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:42:07,406 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:42:20,473 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:42:20,473 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:42:20,474 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:42:20,496 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:42:20,497 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:42:20,497 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:42:22,098 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:42:22,098 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:42:22,100 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:42:22,305 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:42:22,309 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:42:22,336 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:42:23,180 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:42:23,528 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:42:23,529 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:42:23,531 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:42:23,815 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:42:23,820 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:42:23,827 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:42:23,827 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:42:23,859 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:42:24,722 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:42:25,362 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:42:25,363 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:42:30,522 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:42:30,523 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:42:30,523 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:42:30,534 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:42:30,535 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:42:30,535 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:42:32,175 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:42:32,175 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:42:32,176 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:42:32,363 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:42:32,367 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:42:32,399 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:42:33,268 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:42:33,268 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:42:33,270 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:42:33,270 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:42:33,667 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:42:33,699 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:42:33,704 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:42:33,899 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:42:33,899 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:42:34,548 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:42:35,188 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:42:35,188 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:42:37,948 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:42:37,949 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:42:37,950 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:42:38,231 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:42:38,235 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:42:38,262 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:42:39,224 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:42:39,905 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:42:39,906 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:42:47,114 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:43:14,649 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:43:14,649 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:43:14,649 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:43:14,671 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:43:14,671 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:43:14,671 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:43:17,235 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:43:17,235 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:43:17,236 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:43:17,402 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:43:17,459 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:43:17,464 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:43:17,733 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:43:17,734 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:43:17,736 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:43:18,075 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:43:18,105 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:43:18,112 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:43:18,432 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:43:18,893 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:43:19,063 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:43:19,064 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:43:19,532 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:43:19,532 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:43:22,353 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:43:22,354 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:43:22,356 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:43:22,687 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:43:22,690 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:43:22,774 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:43:23,608 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:43:24,272 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:43:24,273 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:43:29,930 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:43:29,930 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:43:29,931 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:43:30,001 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:43:30,001 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:43:30,001 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:43:31,311 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:43:31,311 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:43:31,312 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:43:31,505 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:43:31,509 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:43:31,529 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:43:32,199 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:43:32,622 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:43:32,622 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:43:32,624 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:43:32,824 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:43:32,825 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:43:32,909 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:43:32,917 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:43:32,939 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:43:33,690 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:43:34,324 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:43:34,325 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:43:42,025 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:43:42,027 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:43:42,027 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:43:42,058 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:43:42,059 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:43:42,059 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:43:43,525 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:43:43,526 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:43:43,526 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:43:43,727 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:43:43,742 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:43:43,751 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:43:44,631 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:43:45,000 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:43:45,000 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:43:45,002 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:43:45,264 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:43:45,264 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:43:45,324 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:43:45,335 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:43:45,390 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:43:46,154 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:43:46,610 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:43:46,610 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:43:46,611 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:43:46,782 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:43:46,787 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:43:46,794 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:43:46,795 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:43:46,838 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:43:47,661 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:43:48,296 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:43:48,296 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:43:49,448 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:43:49,449 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:43:49,451 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:43:49,739 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:43:49,750 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:43:49,807 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:43:50,598 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:43:51,254 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:43:51,254 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:43:54,205 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:43:54,206 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:43:54,206 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:43:54,256 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:43:54,257 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:43:54,257 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:43:55,556 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:43:55,556 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:43:55,557 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:43:55,764 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:43:55,794 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:43:55,803 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:43:56,951 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:43:57,091 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:43:57,091 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:43:57,093 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:43:57,414 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:43:57,427 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:43:57,464 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:43:57,598 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:43:57,598 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:43:58,439 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:43:59,098 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:43:59,098 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:44:04,912 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:44:04,913 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:44:04,913 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:44:04,957 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:44:04,958 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:44:04,958 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:44:07,626 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:44:07,626 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:44:07,628 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:44:07,832 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:44:07,855 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:44:07,858 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:44:08,047 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:44:08,048 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:44:08,050 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:44:08,310 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:44:08,314 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:44:08,389 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:44:08,578 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:44:09,238 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:44:09,238 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:44:09,371 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:44:10,016 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:44:10,016 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:44:12,681 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:44:12,682 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:44:12,684 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:44:12,952 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:44:12,958 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:44:12,981 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:44:13,745 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:44:14,013 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:44:14,393 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:44:14,394 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:44:29,331 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:44:29,331 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:44:29,331 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:44:29,438 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:44:29,439 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:44:29,439 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:44:30,928 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:44:30,928 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:44:30,931 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:44:31,232 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:44:31,255 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:44:31,259 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:44:32,035 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:44:32,368 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:44:32,368 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:44:32,370 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:44:32,605 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:44:32,608 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:44:32,667 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:44:32,667 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:44:32,685 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:44:33,461 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:44:33,917 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:44:33,917 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:44:33,918 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:44:34,099 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:44:34,099 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:44:34,111 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:44:34,115 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:44:34,142 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:44:34,879 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:44:35,511 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:44:35,511 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:44:36,753 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:44:36,754 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:44:36,756 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:44:37,044 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:44:37,050 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:44:37,098 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:44:37,905 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:44:38,549 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:44:38,549 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:44:44,924 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:44:44,925 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:44:44,925 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:44:45,003 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:44:45,003 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:44:45,003 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:44:47,501 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:44:47,501 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:44:47,503 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:44:47,717 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:44:47,725 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:44:47,743 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:44:47,882 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:44:47,882 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:44:47,884 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:44:48,172 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:44:48,199 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:44:48,210 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:44:48,532 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:44:48,969 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:44:49,165 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:44:49,166 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:44:49,605 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:44:49,606 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:44:52,275 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:44:52,276 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:44:52,278 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:44:52,558 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:44:52,605 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:44:52,610 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:44:53,357 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:44:54,003 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:44:54,004 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:45:00,170 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:45:00,173 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:45:00,173 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:45:06,824 - INFO - app - [main.py:557] - 使用端口: 8005
2025-05-09 11:45:08,169 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:45:08,169 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:45:08,171 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:45:08,382 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:45:08,409 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:45:08,416 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:45:09,159 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:45:09,802 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:45:09,802 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:45:14,116 - ERROR - app - [main.py:310] - 请求处理异常: name 'ResponseCode' is not defined
2025-05-09 11:45:22,239 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:45:22,239 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:45:22,239 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:45:22,298 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:45:22,298 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:45:22,299 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:45:24,841 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:45:24,842 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:45:24,844 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:45:25,056 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:45:25,075 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:45:25,082 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:45:25,190 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:45:25,190 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:45:25,192 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:45:25,509 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:45:25,518 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:45:25,592 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:45:25,992 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:45:26,528 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:45:26,636 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:45:26,636 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:45:27,173 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:45:27,173 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:45:27,248 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:45:27,248 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:45:27,248 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:45:28,537 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:45:28,537 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:45:28,537 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:45:28,750 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:45:28,753 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:45:28,782 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:45:29,603 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:45:30,006 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:45:30,007 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:45:30,009 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:45:30,283 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:45:30,283 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:45:30,305 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:45:30,443 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:45:30,460 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:45:31,224 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:45:31,871 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:45:31,871 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:45:34,623 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:45:34,623 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:45:34,625 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:45:34,985 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:45:35,008 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:45:35,014 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:45:35,891 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:45:36,544 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:45:36,544 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:57:10,158 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:57:10,159 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:57:10,160 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:57:10,195 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:57:10,196 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:57:10,196 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:57:11,811 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:57:11,811 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:57:11,813 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:57:12,014 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:57:12,063 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:57:12,066 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:57:13,125 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:57:13,129 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:57:13,130 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:57:13,132 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:57:13,434 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:57:13,441 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:57:13,471 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:57:13,765 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:57:13,765 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:57:14,305 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:57:14,951 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:57:14,951 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:57:27,105 - ERROR - app - [main.py:310] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-05-09 11:57:44,278 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:57:44,278 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:57:44,278 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:57:44,279 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:57:44,279 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:57:44,280 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:57:46,260 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:57:46,260 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:57:46,262 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:57:46,442 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:57:46,446 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:57:46,464 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:57:47,242 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:57:47,498 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:57:47,499 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:57:47,501 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:57:47,773 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:57:47,780 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:57:47,825 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:57:47,878 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:57:47,878 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:57:48,609 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:57:49,242 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:57:49,242 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:57:58,984 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:57:58,985 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:57:58,985 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:57:59,058 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:57:59,059 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:57:59,059 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:58:00,750 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:58:00,750 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:58:00,751 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:58:00,941 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:58:00,945 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:58:00,970 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:58:02,063 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:58:02,416 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:58:02,416 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:58:02,419 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:58:02,695 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:58:02,695 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:58:02,708 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:58:02,714 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:58:02,732 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:58:03,743 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:58:04,374 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:58:04,374 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:58:04,375 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:58:04,391 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:58:04,392 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:58:04,546 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:58:04,605 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:58:04,612 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:58:05,422 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:58:06,055 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:58:06,055 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:58:07,249 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:58:07,249 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:58:07,251 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:58:07,541 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:58:07,561 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:58:07,571 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:58:08,647 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:58:08,647 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:58:08,647 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:58:08,661 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:58:09,322 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:58:09,322 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:58:09,969 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:58:09,969 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:58:09,970 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:58:10,127 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:58:10,131 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:58:10,155 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:58:11,100 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:58:11,741 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:58:11,741 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:58:12,275 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:58:12,275 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:58:12,278 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:58:12,600 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:58:12,607 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:58:12,630 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:58:13,476 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:58:14,128 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:58:14,128 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:58:16,809 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:58:16,810 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:58:16,812 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:58:17,124 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:58:17,130 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:58:17,151 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:58:18,023 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:58:18,668 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:58:18,668 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:58:21,194 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:58:21,194 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:58:21,194 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:58:21,237 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:58:21,237 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:58:21,237 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:58:22,756 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:58:22,756 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:58:22,757 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:58:22,998 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:58:23,011 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:58:23,073 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:58:23,861 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:58:24,291 - INFO - app - [main.py:410] - 开始应用启动初始化...
2025-05-09 11:58:24,292 - INFO - app - [main.py:425] - Redis连接池初始化完成
2025-05-09 11:58:24,294 - INFO - app - [main.py:432] - MinIO对象存储服务初始化完成
2025-05-09 11:58:24,490 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:58:24,490 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:58:24,617 - INFO - app - [main.py:474] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:58:24,640 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:58:24,644 - INFO - app - [main.py:458] - 动态配置加载完成
2025-05-09 11:58:25,378 - INFO - app - [main.py:419] - 数据库初始化完成
2025-05-09 11:58:26,034 - INFO - app - [main.py:496] - 数据库连接状态: healthy
2025-05-09 11:58:26,034 - INFO - app - [main.py:500] - 应用启动完成: 管理后台服务
2025-05-09 11:58:33,106 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:58:33,107 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:58:33,107 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:58:33,192 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-09 11:58:33,193 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-09 11:58:33,193 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-09 11:58:34,712 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 11:58:34,712 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 11:58:34,713 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 11:58:34,899 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:58:34,920 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:58:34,923 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 11:58:35,793 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 11:58:35,975 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 11:58:35,976 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 11:58:35,978 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 11:58:36,358 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:58:36,363 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 11:58:36,384 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:58:36,432 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 11:58:36,432 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 11:58:37,407 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 11:58:38,044 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 11:58:38,044 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 11:58:42,335 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 11:58:42,336 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 11:58:42,336 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 11:58:49,283 - INFO - app - [main.py:573] - 使用端口: 8005
2025-05-09 11:58:50,543 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 11:58:50,543 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 11:58:50,545 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 11:58:50,743 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:58:50,747 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 11:58:50,775 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:58:51,538 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 11:58:52,171 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 11:58:52,171 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 11:59:27,525 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 11:59:27,525 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 11:59:27,525 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 11:59:27,526 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 11:59:27,526 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 11:59:27,526 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 11:59:29,928 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 11:59:29,928 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 11:59:29,930 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 11:59:30,129 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:59:30,134 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 11:59:30,160 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:59:30,462 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 11:59:30,463 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 11:59:30,464 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 11:59:30,764 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:59:30,774 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 11:59:30,790 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:59:30,918 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 11:59:31,539 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 11:59:31,548 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 11:59:31,548 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 11:59:32,179 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 11:59:32,179 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 11:59:34,577 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 11:59:34,577 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 11:59:34,578 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 11:59:34,864 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 11:59:34,864 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 11:59:34,867 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 11:59:35,154 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:59:35,201 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:59:35,204 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 11:59:35,966 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 11:59:35,966 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 11:59:35,967 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 11:59:36,000 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 11:59:36,137 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:59:36,187 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:59:36,191 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 11:59:36,642 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 11:59:36,643 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 11:59:36,989 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 11:59:37,624 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 11:59:37,624 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 11:59:39,450 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 11:59:39,450 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 11:59:39,452 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 11:59:39,928 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 11:59:39,942 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 11:59:39,947 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 11:59:40,743 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 11:59:41,373 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 11:59:41,374 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 12:00:09,174 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 12:00:09,174 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 12:00:09,174 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 12:00:09,175 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 12:00:09,175 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 12:00:09,176 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 12:00:11,315 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 12:00:11,316 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 12:00:11,318 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 12:00:11,497 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 12:00:11,516 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 12:00:11,525 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 12:00:11,965 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 12:00:11,965 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 12:00:11,967 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 12:00:12,218 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 12:00:12,223 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 12:00:12,270 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 12:00:12,323 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 12:00:12,958 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 12:00:12,958 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 12:00:13,088 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 12:00:13,723 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 12:00:13,723 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 12:00:16,759 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 12:00:16,761 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 12:00:16,764 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 12:00:17,072 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 12:00:17,080 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 12:00:17,099 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 12:00:18,092 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 12:00:18,737 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 12:00:18,737 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 12:01:50,245 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 12:01:50,246 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 12:01:50,246 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 12:01:50,345 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 12:01:50,346 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 12:01:50,346 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 12:01:51,887 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 12:01:51,887 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 12:01:51,889 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 12:01:52,063 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 12:01:52,069 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 12:01:52,095 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 12:01:52,837 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 12:01:52,943 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 12:01:52,943 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 12:01:52,945 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 12:01:53,236 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 12:01:53,291 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 12:01:53,301 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 12:01:53,465 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 12:01:53,465 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 12:01:54,089 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 12:01:54,727 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 12:01:54,727 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:48:05,856 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:48:05,858 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:48:05,859 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:48:05,911 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:48:05,912 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:48:05,912 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:48:07,967 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:48:07,967 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:48:07,969 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:48:08,178 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:48:08,184 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:48:08,203 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:48:08,895 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:48:09,103 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:48:09,103 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:48:09,105 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:48:09,458 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:48:09,491 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:48:09,495 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:48:09,527 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:48:09,527 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:48:10,221 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:48:10,858 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:48:10,859 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:48:11,255 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:48:11,255 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:48:11,261 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:48:11,727 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:48:11,759 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:48:11,782 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:48:13,125 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:48:13,755 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:48:13,755 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:48:15,171 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:48:15,171 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:48:15,174 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:48:15,541 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:48:15,549 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:48:15,575 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:48:16,524 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:48:17,168 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:48:17,168 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:48:20,853 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:48:20,854 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:48:20,854 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:48:20,911 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:48:20,911 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:48:20,911 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:48:22,266 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:48:22,267 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:48:22,269 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:48:22,458 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:48:22,462 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:48:22,515 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:48:23,354 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:48:23,903 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:48:23,903 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:48:23,905 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:48:23,986 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:48:23,987 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:48:24,142 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:48:24,146 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:48:24,193 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:48:25,232 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:48:25,877 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:48:25,877 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:48:40,531 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:48:40,533 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:48:40,533 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:48:40,534 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:48:40,534 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:48:40,534 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:48:42,229 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:48:42,229 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:48:42,231 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:48:42,825 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:48:43,006 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:48:43,015 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:48:43,711 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:48:43,712 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:48:43,714 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:48:44,465 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:48:44,508 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:48:44,592 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:48:45,516 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:48:45,952 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:48:46,179 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:48:46,180 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:48:46,590 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:48:46,591 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:48:56,408 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:48:56,410 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:48:56,410 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:48:56,453 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:48:56,453 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:48:56,454 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:48:58,172 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:48:58,173 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:48:58,175 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:48:58,357 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:48:58,362 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:48:58,420 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:48:59,431 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:48:59,431 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:48:59,433 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:48:59,666 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:48:59,776 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:48:59,804 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:48:59,813 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:49:00,319 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:49:00,319 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:49:01,157 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:49:01,887 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:49:01,887 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:49:21,904 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:49:21,904 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:49:21,904 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:49:21,917 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:49:21,917 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:49:21,918 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:49:24,825 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:49:24,825 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:49:24,828 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:49:25,018 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:49:25,032 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:49:25,032 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:49:25,034 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:49:25,048 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:49:25,054 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:49:25,424 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:49:25,475 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:49:25,483 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:49:26,074 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:49:26,437 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:49:26,715 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:49:26,715 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:49:27,077 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:49:27,077 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:49:29,971 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:49:29,972 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:49:29,974 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:49:30,231 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:49:30,315 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:49:30,319 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:49:31,038 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:49:31,668 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:49:31,669 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:49:31,873 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:49:31,873 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:49:31,873 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:49:31,889 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:49:31,889 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:49:31,889 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:49:33,286 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:49:33,286 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:49:33,287 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:49:33,488 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:49:33,492 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:49:33,548 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:49:34,253 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:49:34,770 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:49:34,770 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:49:34,772 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:49:34,880 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:49:34,880 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:49:35,068 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:49:35,120 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:49:35,126 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:49:36,015 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:49:36,648 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:49:36,649 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:49:54,893 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:49:54,894 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:49:54,895 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:49:54,904 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:49:54,905 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:49:54,906 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:49:57,076 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:49:57,077 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:49:57,080 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:49:57,289 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:49:57,319 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:49:57,324 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:49:58,002 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:49:58,400 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:49:58,401 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:49:58,403 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:49:58,634 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:49:58,634 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:49:58,678 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:49:58,687 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:49:58,749 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:49:59,452 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:49:59,971 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:49:59,971 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:49:59,972 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:50:00,084 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:50:00,084 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:50:00,176 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:50:00,193 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:50:00,199 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:50:01,003 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:50:01,632 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:50:01,632 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:50:02,975 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:50:02,976 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:50:02,977 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:50:03,138 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:50:03,140 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:50:03,142 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:50:03,185 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:50:03,192 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:50:03,237 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:50:03,453 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:50:03,458 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:50:03,482 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:50:03,994 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:50:04,287 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:50:04,625 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:50:04,626 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:50:04,923 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:50:04,924 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:50:07,814 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:50:07,814 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:50:07,816 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:50:08,104 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:50:08,122 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:50:08,127 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:50:08,849 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:50:09,498 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:50:09,499 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:50:49,753 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:50:49,756 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:50:49,757 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:50:49,785 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:50:49,786 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:50:49,786 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:50:52,348 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:50:52,348 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:50:52,349 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:50:52,549 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:50:52,569 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:50:52,573 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:50:53,280 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:50:53,465 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:50:53,465 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:50:53,467 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:50:53,763 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:50:53,769 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:50:53,796 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:50:53,909 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:50:53,910 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:50:54,672 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:50:55,313 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:50:55,313 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:03,308 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:53:03,310 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:53:03,312 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:53:03,312 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:53:03,312 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:53:03,313 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:53:05,285 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:05,286 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:05,287 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:05,570 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:05,591 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:05,624 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:06,407 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:06,408 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:06,410 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:06,575 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:53:06,741 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:06,743 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:06,801 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:07,209 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:53:07,209 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:07,585 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:53:08,225 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:53:08,225 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:09,842 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:53:09,842 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:53:09,842 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:53:09,845 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:53:09,845 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:53:09,845 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:53:11,148 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:11,149 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:11,149 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:11,359 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:11,367 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:11,385 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:12,175 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:53:12,776 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:12,776 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:12,779 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:12,810 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:53:12,810 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:13,020 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:13,024 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:13,128 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:13,960 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:53:14,659 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:53:14,660 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:33,097 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:53:33,097 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:53:33,098 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:53:33,099 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:53:33,100 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:53:33,101 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:53:34,777 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:34,777 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:34,778 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:34,957 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:34,987 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:34,993 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:35,834 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:53:36,015 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:36,016 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:36,018 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:36,322 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:36,332 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:36,349 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:36,467 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:53:36,467 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:37,367 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:53:38,006 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:53:38,007 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:38,225 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:38,225 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:38,225 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:38,449 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:38,458 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:38,479 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:39,190 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:53:39,822 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:53:39,822 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:40,803 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:40,803 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:40,806 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:41,091 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:41,096 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:41,127 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:41,916 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:53:42,552 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:53:42,552 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:45,277 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:53:45,278 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:53:45,278 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:53:45,364 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:45,364 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:45,366 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:45,915 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:45,946 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:45,954 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:46,808 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:46,808 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:46,809 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:47,093 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:53:47,103 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:47,139 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:47,146 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:47,732 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:53:47,733 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:48,099 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:53:48,747 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:53:48,747 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:50,060 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:53:50,060 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:53:50,061 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:53:50,546 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:50,546 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:50,549 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:50,895 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:50,908 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:50,963 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:51,915 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:53:52,554 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:53:52,555 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:57,196 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:57,198 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:57,199 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:57,463 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:57,486 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:57,490 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:58,382 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:53:58,799 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:53:58,800 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:53:58,802 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:53:59,016 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:53:59,016 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:53:59,093 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:53:59,141 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:53:59,144 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:53:59,890 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:54:00,528 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:54:00,529 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:54:09,119 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:54:09,121 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:54:09,121 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:54:21,290 - INFO - app - [main.py:573] - 使用端口: 8005
2025-05-09 13:54:22,726 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:54:22,726 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:54:22,728 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:54:23,113 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:54:23,135 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:54:23,141 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:54:24,105 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:54:24,746 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:54:24,747 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:54:45,975 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:54:45,977 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:54:45,977 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:54:46,039 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 13:54:46,040 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 13:54:46,041 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 13:54:47,525 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:54:47,525 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:54:47,527 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:54:47,819 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:54:47,823 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:54:47,846 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:54:48,603 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:54:48,766 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 13:54:48,766 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 13:54:48,768 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 13:54:49,162 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 13:54:49,244 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 13:54:49,251 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 13:54:49,265 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:54:49,265 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 13:54:50,158 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 13:54:50,795 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 13:54:50,795 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 15:27:12,529 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 15:27:12,532 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 15:27:12,532 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 15:27:12,553 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-09 15:27:12,554 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-09 15:27:12,554 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-09 15:27:15,793 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 15:27:15,793 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 15:27:15,796 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 15:27:16,086 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 15:27:16,091 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 15:27:16,144 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 15:27:17,005 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 15:27:17,221 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 15:27:17,221 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 15:27:17,222 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 15:27:17,409 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 15:27:17,413 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 15:27:17,433 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 15:27:17,642 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 15:27:17,642 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 15:27:18,423 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 15:27:19,053 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 15:27:19,053 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 15:27:20,538 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 15:27:20,539 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 15:27:20,540 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 15:27:20,698 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 15:27:20,699 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 15:27:20,700 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 15:27:20,858 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 15:27:20,871 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 15:27:20,878 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 15:27:20,894 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 15:27:20,896 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 15:27:20,920 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 15:27:21,775 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 15:27:21,786 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 15:27:22,421 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 15:27:22,421 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 15:27:22,437 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 15:27:22,438 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 15:27:24,074 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 15:27:24,074 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 15:27:24,075 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 15:27:24,270 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 15:27:24,275 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 15:27:24,303 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 15:27:25,097 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 15:27:25,098 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 15:27:25,100 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 15:27:25,205 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 15:27:25,396 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 15:27:25,460 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 15:27:25,464 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 15:27:25,841 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 15:27:25,842 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 15:27:26,364 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 15:27:27,014 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 15:27:27,014 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 15:27:27,094 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 15:27:27,095 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 15:27:27,095 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 15:27:27,268 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 15:27:27,272 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 15:27:27,311 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 15:27:28,146 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 15:27:28,777 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 15:27:28,778 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 15:27:29,597 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 15:27:29,597 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 15:27:29,599 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 15:27:29,909 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 15:27:29,912 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 15:27:29,932 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 15:27:30,772 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 15:27:31,420 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 15:27:31,421 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 15:27:33,963 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 15:27:33,963 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 15:27:33,965 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 15:27:34,211 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 15:27:34,214 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 15:27:34,272 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 15:27:35,167 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 15:27:35,815 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 15:27:35,815 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-09 15:29:42,147 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-09 15:29:42,147 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-09 15:29:42,150 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-09 15:29:42,450 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-09 15:29:42,466 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-09 15:29:42,476 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-09 15:29:43,284 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-09 15:29:43,924 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-09 15:29:43,924 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-11 11:58:35,659 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-11 11:58:35,672 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-11 11:58:35,675 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-11 11:58:35,676 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-11 11:58:35,681 - INFO - app - [main.py:508] - 应用关闭，执行清理操作...
2025-05-11 11:58:35,684 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-11 11:58:35,685 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-11 11:58:35,697 - INFO - app - [main.py:514] - 已停止认证配置刷新任务
2025-05-11 11:58:35,701 - INFO - app - [main.py:530] - 应用已完全关闭

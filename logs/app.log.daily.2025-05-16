2025-05-16 15:23:44,355 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:23:44,356 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:23:44,358 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:23:44,693 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:23:44,710 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:23:44,715 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:23:45,736 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:23:46,385 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:23:46,387 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:46:18,132 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 15:46:18,133 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 15:46:18,133 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 15:46:22,635 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:46:22,635 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:46:22,638 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:46:22,886 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:46:22,895 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:46:22,951 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:46:23,844 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:46:24,500 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:46:24,501 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:46:28,781 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 15:46:28,782 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 15:46:28,782 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 15:46:32,610 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:46:32,610 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:46:32,614 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:46:32,891 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:46:32,898 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:46:32,918 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:46:33,853 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:46:34,490 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:46:34,491 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:46:46,632 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 15:46:46,633 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 15:46:46,633 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 15:46:50,240 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:46:50,240 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:46:50,244 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:46:50,574 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:46:50,582 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:46:50,610 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:46:51,472 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:46:52,106 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:46:52,106 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:48:21,144 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:48:21,145 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:48:21,148 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:48:21,401 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:48:21,437 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:48:21,440 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:48:22,324 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:48:22,981 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:48:22,981 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:49:51,801 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 15:49:51,802 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 15:49:51,803 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 15:49:55,694 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:49:55,694 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:49:55,698 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:49:56,004 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:49:56,010 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:49:56,031 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:49:56,961 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:49:57,605 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:49:57,607 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:50:15,658 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 15:50:15,661 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 15:50:15,662 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 15:50:19,931 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:50:19,931 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:50:19,934 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:50:20,193 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:50:20,203 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:50:20,252 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:50:21,080 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:50:21,719 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:50:21,719 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:50:43,613 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 15:50:43,614 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 15:50:43,615 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 15:53:16,840 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:53:16,841 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:53:16,844 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:53:17,114 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:53:17,161 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:53:17,169 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:53:17,966 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:53:18,609 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:53:18,610 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:53:19,623 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 15:53:19,624 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 15:53:19,625 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 15:53:43,970 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:53:43,972 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:53:43,974 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:53:44,263 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:53:44,286 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:53:44,292 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:53:45,104 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:53:45,745 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:53:45,746 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:53:48,683 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 15:53:48,684 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 15:53:48,694 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 15:53:54,917 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:53:54,918 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:53:54,921 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:53:55,297 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:53:55,304 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:53:55,346 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:53:56,134 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:53:56,776 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:53:56,776 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:54:06,685 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 15:54:06,688 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 15:54:06,689 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 15:54:11,154 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:54:11,154 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:54:11,158 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:54:11,460 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:54:11,474 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:54:11,501 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:54:12,383 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:54:13,021 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:54:13,021 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:54:33,488 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 15:54:33,489 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 15:54:33,490 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 15:55:02,146 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:55:02,147 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:55:02,149 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:55:02,520 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:55:02,526 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:55:03,180 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:55:03,351 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:55:03,988 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:55:03,988 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:56:28,352 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 15:56:28,355 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 15:56:28,356 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 15:56:32,377 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:56:32,377 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:56:32,381 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:56:32,772 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:56:32,776 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:56:32,863 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:56:33,781 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:56:34,431 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:56:34,431 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 15:56:40,320 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 15:56:40,321 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 15:56:40,321 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 15:56:44,081 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 15:56:44,081 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 15:56:44,084 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 15:56:44,368 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 15:56:44,372 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 15:56:44,393 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 15:56:45,414 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 15:56:46,055 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 15:56:46,055 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:01:01,915 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:01:01,917 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:01:01,919 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:01:02,264 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:01:02,282 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:01:02,286 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:01:03,011 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:01:03,656 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:01:03,657 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:02:28,756 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:02:28,757 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:02:28,758 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:02:32,727 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:02:32,727 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:02:32,731 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:02:33,024 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:02:33,030 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:02:33,054 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:02:33,787 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:02:34,423 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:02:34,423 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:02:51,427 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:02:51,428 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:02:51,429 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:02:55,185 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:02:55,185 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:02:55,189 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:02:55,543 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:02:55,549 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:02:55,572 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:02:56,894 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:02:57,567 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:02:57,568 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:04:45,100 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:04:45,101 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:04:45,102 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:04:49,033 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:04:49,033 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:04:49,036 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:04:49,342 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:04:49,346 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:04:49,371 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:04:50,166 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:04:50,817 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:04:50,818 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:05:25,837 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:05:25,840 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:05:25,840 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:05:29,652 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:05:29,653 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:05:29,656 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:05:29,946 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:05:29,950 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:05:29,968 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:05:30,696 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:05:31,345 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:05:31,346 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:07:04,351 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:07:04,357 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:07:04,359 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:07:10,948 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:07:10,948 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:07:10,952 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:07:11,223 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:07:11,230 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:07:11,258 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:07:12,150 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:07:12,798 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:07:12,798 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:08:20,483 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:08:20,483 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:08:20,487 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:08:20,765 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:08:20,810 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:08:20,813 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:08:21,670 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:08:22,310 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:08:22,311 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:12:24,462 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:12:24,463 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:12:24,465 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:12:24,712 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:12:24,734 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:12:24,740 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:12:25,667 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:12:26,336 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:12:26,337 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:14:32,258 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:14:32,259 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:14:32,259 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:14:36,139 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:14:36,140 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:14:36,142 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:14:36,442 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:14:36,457 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:14:36,463 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:14:37,325 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:14:37,964 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:14:37,964 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:14:50,223 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:14:50,244 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:14:50,244 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:14:54,580 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:14:54,580 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:14:54,584 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:14:54,895 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:14:54,904 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:14:54,937 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:14:55,864 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:14:56,535 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:14:56,536 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:16:13,889 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:16:13,891 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:16:13,892 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:16:17,891 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:16:17,891 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:16:17,894 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:16:18,132 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:16:18,137 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:16:18,173 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:16:18,951 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:16:19,602 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:16:19,602 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:17:05,384 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:17:05,389 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:17:05,390 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:17:09,385 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:17:09,385 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:17:09,391 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:17:09,690 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:17:09,725 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:17:09,733 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:17:10,763 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:17:11,409 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:17:11,410 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:17:36,102 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:17:36,103 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:17:36,104 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:17:40,352 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:17:40,352 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:17:40,355 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:17:40,692 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:17:40,696 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:17:40,724 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:17:41,574 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:17:42,212 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:17:42,213 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:18:11,052 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:18:11,053 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:18:11,054 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:18:15,307 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:18:15,307 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:18:15,311 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:18:15,623 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:18:15,627 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:18:15,649 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:18:16,563 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:18:17,295 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:18:17,296 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:37:13,332 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:37:13,334 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:37:13,335 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:37:18,118 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:37:18,119 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:37:18,119 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:37:18,377 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:37:18,381 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:37:19,152 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:37:19,221 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:37:19,916 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:37:19,917 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:43:28,020 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:43:28,020 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:43:28,021 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:43:28,289 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:43:28,293 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:43:29,053 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:43:29,176 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:43:29,818 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:43:29,818 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:45:30,925 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:45:30,926 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:45:30,926 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:45:31,314 - INFO - app.config - [config.py:282] - 成功从数据库加载系统配置，共10项
2025-05-16 16:45:31,325 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:45:31,958 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:45:32,399 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:45:33,034 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:45:33,035 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:47:13,898 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:47:13,902 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:47:13,903 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:47:18,332 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:47:18,332 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:47:18,333 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:51:18,538 - ERROR - app - [main.py:437] - 数据库初始化失败: 
2025-05-16 16:51:18,542 - WARNING - app.config - [config.py:284] - 无法加载动态配置: 
2025-05-16 16:51:18,542 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:51:19,731 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:51:20,476 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:51:20,476 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:51:20,480 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:51:20,481 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:51:20,482 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:51:35,480 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:51:35,481 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:51:35,482 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:51:35,759 - INFO - app.config - [config.py:279] - 成功从数据库加载系统配置，共10项
2025-05-16 16:51:35,763 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:51:35,838 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:51:36,467 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:51:37,104 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:51:37,105 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:53:50,573 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:53:50,575 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:53:50,576 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:53:59,835 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:53:59,835 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:53:59,836 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:54:00,240 - INFO - app.config - [config.py:279] - 成功从数据库加载系统配置，共10项
2025-05-16 16:54:00,248 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:54:00,339 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:54:01,022 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:54:01,660 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:54:01,660 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:54:16,098 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:54:16,100 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:54:16,100 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:55:38,619 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:55:38,621 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:55:38,622 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:55:38,949 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:55:38,956 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:55:39,022 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:55:39,800 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:55:40,441 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:55:40,441 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:55:51,249 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:55:51,250 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:55:51,250 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:55:55,549 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:55:55,549 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:55:55,549 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:55:55,838 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:55:55,842 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:55:55,935 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:55:57,071 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:55:57,717 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:55:57,718 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:56:01,071 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:56:01,072 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:56:01,073 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:56:05,863 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:56:05,863 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:56:05,864 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:56:06,219 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:56:06,224 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:56:06,249 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:56:07,179 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:56:07,841 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:56:07,841 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:56:32,034 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:56:32,036 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:56:32,037 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:56:37,525 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:56:37,525 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:56:37,526 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:56:37,841 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:56:37,845 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:56:37,910 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:56:38,753 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:56:39,386 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:56:39,387 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:57:09,607 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:57:09,610 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:57:09,611 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:57:13,917 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:57:13,917 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:57:13,918 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:57:14,191 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:57:14,200 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:57:14,451 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:57:15,448 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:57:16,097 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:57:16,097 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:57:25,796 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:57:25,798 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:57:25,798 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:57:30,419 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:57:30,419 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:57:30,420 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:57:30,823 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:57:30,848 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:57:30,862 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:57:31,660 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:57:32,329 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:57:32,329 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:57:32,331 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:57:32,331 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:57:32,332 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:57:36,301 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:57:36,301 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:57:36,302 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:57:36,624 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:57:36,629 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:57:36,657 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:57:37,338 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:57:37,977 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:57:37,978 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:57:38,186 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:57:38,186 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:57:38,187 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:57:42,710 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:57:42,710 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:57:42,711 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:57:42,993 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:57:42,998 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:57:43,103 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:57:43,953 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:57:44,613 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:57:44,614 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:57:46,847 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:57:46,848 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:57:46,848 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:57:50,933 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:57:50,934 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:57:50,934 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:57:51,309 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:57:51,333 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:57:51,340 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:57:52,187 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:57:52,840 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:57:52,841 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:58:10,101 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:58:10,107 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:58:10,108 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:58:14,480 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:58:14,481 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:58:14,481 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:58:14,912 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:58:14,927 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:58:14,940 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:58:15,797 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:58:16,432 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:58:16,433 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:58:55,351 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:58:55,352 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:58:55,353 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:59:00,941 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:59:00,942 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:59:00,942 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:59:01,177 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:59:01,182 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:59:01,217 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:59:02,154 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:59:02,798 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:59:02,799 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:59:22,724 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:59:22,725 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:59:22,725 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:59:24,903 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:59:24,903 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:59:24,903 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:59:27,241 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:59:27,257 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:59:27,833 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:59:33,728 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:59:34,527 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:59:34,528 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 16:59:34,529 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 16:59:34,530 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 16:59:34,530 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 16:59:50,703 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 16:59:50,703 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 16:59:50,703 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 16:59:50,969 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 16:59:50,973 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 16:59:51,003 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 16:59:51,687 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 16:59:52,314 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 16:59:52,314 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:00:10,222 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:00:10,224 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:00:10,224 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:00:26,721 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:00:26,722 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:00:26,722 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:00:27,008 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:00:27,011 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:00:27,041 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:00:27,780 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:00:28,435 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:00:28,436 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:03:10,933 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:03:10,934 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:03:10,935 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:03:13,739 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:03:13,739 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:03:13,740 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:03:14,133 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:03:14,140 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:03:14,236 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:03:15,298 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:03:15,932 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:03:15,932 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:03:33,440 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:03:33,443 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:03:33,443 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:03:35,717 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:03:35,718 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:03:35,718 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:03:36,005 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:03:36,009 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:03:36,110 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:03:36,918 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:03:37,546 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:03:37,546 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:04:00,842 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:04:00,847 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:04:00,847 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:04:03,538 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:04:03,538 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:04:03,538 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:04:03,871 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:04:03,876 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:04:03,939 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:04:04,942 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:04:05,597 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:04:05,598 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:04:39,992 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:04:39,996 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:04:39,997 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:04:42,407 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:04:42,408 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:04:42,408 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:04:42,802 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:04:42,868 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:04:42,877 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:04:44,171 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:04:44,850 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:04:44,850 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:04:44,851 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:04:44,851 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:04:44,851 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:04:46,819 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:04:46,819 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:04:46,819 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:04:47,103 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:04:47,107 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:04:47,142 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:04:48,182 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:04:48,837 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:04:48,838 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:04:53,005 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:04:53,006 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:04:53,006 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:05:19,080 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:05:19,081 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:05:19,081 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:05:19,525 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:05:19,530 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:05:19,582 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:05:20,903 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:05:21,544 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:05:21,545 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:49:37,338 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:49:37,339 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:49:37,340 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:49:38,168 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:49:38,184 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:49:38,310 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:49:40,639 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:49:41,356 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:49:41,356 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:49:42,003 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:49:42,003 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:49:42,003 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:49:42,643 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:49:42,656 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:49:42,979 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:49:44,473 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:49:45,104 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:49:45,104 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:49:45,105 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:49:45,105 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:49:45,106 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:52:46,097 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:52:46,099 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:52:46,099 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:52:46,381 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:52:46,386 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:52:46,462 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:52:47,090 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:52:47,725 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:52:47,726 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:55:38,575 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:55:38,577 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:55:38,578 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:55:42,934 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:55:42,934 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:55:42,935 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:55:43,248 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:55:43,254 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:55:43,294 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:55:44,113 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:55:44,745 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:55:44,746 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:56:11,101 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:56:11,103 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:56:11,106 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:56:15,475 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:56:15,476 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:56:15,476 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:56:15,812 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:56:15,816 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:56:15,840 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:56:16,457 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:56:17,088 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:56:17,089 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:56:42,272 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:56:42,275 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:56:42,275 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:56:46,633 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:56:46,633 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:56:46,634 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:56:46,965 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:56:46,987 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:56:46,991 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:56:47,641 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:56:48,275 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:56:48,275 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:57:08,444 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:57:08,446 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:57:08,447 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:57:12,763 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:57:12,764 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:57:12,764 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:57:13,058 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:57:13,069 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:57:13,127 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:57:13,791 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:57:14,422 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:57:14,423 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:57:15,434 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:57:15,435 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:57:15,436 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:57:20,636 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:57:20,636 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:57:20,637 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:57:21,111 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:57:21,114 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:57:21,216 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:57:21,853 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:57:22,490 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:57:22,490 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:57:26,644 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:57:26,646 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:57:26,646 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:57:26,875 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:57:26,886 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:57:26,891 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:57:27,630 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:57:28,258 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:57:28,258 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务
2025-05-16 17:57:28,260 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:57:28,260 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:57:28,260 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:57:47,275 - INFO - app - [main.py:524] - 应用关闭，执行清理操作...
2025-05-16 17:57:47,277 - INFO - app - [main.py:530] - 已停止认证配置刷新任务
2025-05-16 17:57:47,278 - INFO - app - [main.py:546] - 应用已完全关闭
2025-05-16 17:57:51,424 - INFO - app - [main.py:426] - 开始应用启动初始化...
2025-05-16 17:57:51,424 - INFO - app - [main.py:441] - Redis连接池初始化完成
2025-05-16 17:57:51,425 - INFO - app - [main.py:448] - MinIO对象存储服务初始化完成
2025-05-16 17:57:51,672 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-16 17:57:51,678 - INFO - app - [main.py:474] - 动态配置加载完成
2025-05-16 17:57:51,782 - INFO - app - [main.py:490] - 认证配置初始化完成，并已启动自动刷新
2025-05-16 17:57:52,553 - INFO - app - [main.py:435] - 数据库初始化完成
2025-05-16 17:57:53,189 - INFO - app - [main.py:512] - 数据库连接状态: healthy
2025-05-16 17:57:53,190 - INFO - app - [main.py:516] - 应用启动完成: 管理后台服务

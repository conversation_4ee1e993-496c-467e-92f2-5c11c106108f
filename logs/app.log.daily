2025-06-03 09:08:54,019 - INFO - app - [main.py:557] - 使用端口: 8000
2025-06-03 09:08:54,228 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:08:54,228 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:08:54,228 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:08:54,762 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:08:54,764 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:08:54,783 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:08:55,704 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:08:56,338 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:08:56,338 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:10:11,874 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:10:11,874 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:10:11,874 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:10:12,305 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:10:12,352 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:10:12,360 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:10:13,665 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:10:14,369 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:10:14,369 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:11:00,899 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:13:24,142 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:14:29,207 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:15:49,408 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:23:01,920 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:23:01,920 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:23:01,921 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:23:02,248 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:23:02,253 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:23:02,342 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:23:03,129 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:23:03,778 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:23:03,779 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:24:02,000 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:24:02,000 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:24:02,001 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:24:05,901 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:24:05,901 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:24:05,902 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:24:06,233 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:24:06,237 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:24:06,262 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:24:07,035 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:24:07,680 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:24:07,680 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:24:11,030 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:24:11,030 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:24:11,031 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:24:23,389 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:24:23,389 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:24:23,390 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:24:23,934 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:24:23,940 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:24:23,971 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:24:25,476 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:24:26,121 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:24:26,122 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:24:29,374 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:24:29,376 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:24:29,376 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:25:10,080 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:25:10,081 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:25:10,082 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:25:10,400 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:25:10,406 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:25:10,481 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:25:11,215 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:25:11,867 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:25:11,867 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:25:15,005 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:25:15,006 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:25:15,006 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:25:18,579 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:25:18,580 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:25:18,580 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:25:18,840 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:25:18,846 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:25:18,936 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:25:19,764 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:25:20,414 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:25:20,414 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:25:24,268 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:25:24,269 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:25:24,269 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:25:47,548 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:25:47,548 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:25:47,549 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:25:47,891 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:25:47,895 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:25:47,955 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:25:48,679 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:25:49,356 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:25:49,357 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:25:53,108 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:25:53,108 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:25:53,108 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:25:56,769 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:25:56,770 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:25:56,770 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:25:57,053 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:25:57,059 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:25:57,122 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:25:57,920 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:25:58,568 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:25:58,569 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:26:04,435 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:26:04,436 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:26:04,436 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:26:08,093 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:26:08,093 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:26:08,094 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:26:08,375 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:26:08,379 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:26:08,490 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:26:09,382 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:26:10,053 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:26:10,054 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:26:15,131 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:26:15,132 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:26:15,132 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:26:18,792 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:26:18,792 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:26:18,793 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:26:19,077 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:26:19,085 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:26:19,146 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:26:19,969 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:26:20,615 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:26:20,616 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:27:35,745 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:27:35,745 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:27:35,745 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:27:35,968 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:27:35,973 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:27:36,051 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:27:36,844 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:27:37,488 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:27:37,488 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:28:21,879 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:33:22,678 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:33:22,679 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:33:22,679 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:33:22,693 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:33:22,693 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:33:22,694 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:33:24,681 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:33:24,681 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:33:24,681 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:33:24,931 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:33:24,935 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:33:24,961 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:33:25,719 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:33:26,347 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:33:26,347 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:33:26,380 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:33:26,380 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:33:26,381 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:33:26,749 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:33:26,771 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:33:26,775 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:33:27,818 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:33:28,462 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:33:28,463 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:34:01,757 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:34:01,758 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:34:01,758 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:34:01,760 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:34:01,760 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:34:01,761 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:34:03,738 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:34:03,738 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:34:03,738 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:34:03,920 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:34:03,925 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:34:04,043 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:34:04,949 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:34:05,444 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:34:05,445 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:34:05,445 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:34:05,588 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:34:05,588 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:34:05,830 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:34:05,837 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:34:05,842 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:34:06,675 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:34:07,370 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:34:07,371 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:34:14,555 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:34:14,556 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:34:14,556 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:34:14,589 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:34:14,589 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:34:14,589 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:34:16,257 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:34:16,257 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:34:16,258 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:34:16,474 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:34:16,479 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:34:16,569 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:34:17,315 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:34:17,949 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:34:17,950 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:34:18,461 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:34:18,461 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:34:18,462 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:34:18,730 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:34:18,738 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:34:18,798 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:34:19,689 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:34:20,338 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:34:20,338 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:34:24,112 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:34:24,112 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:34:24,112 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:34:24,120 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:34:24,120 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:34:24,120 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:34:25,648 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:34:25,648 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:34:25,648 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:34:25,982 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:34:25,994 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:34:26,001 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:34:26,864 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:34:27,495 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:34:27,495 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:34:27,709 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:34:27,710 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:34:27,710 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:34:28,058 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:34:28,066 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:34:28,139 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:34:29,390 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:34:30,056 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:34:30,057 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:35:03,725 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:35:03,725 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:35:03,725 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:35:04,068 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:35:04,078 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:35:04,156 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:35:05,115 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:35:05,748 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:35:05,748 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:36:08,820 - ERROR - app - [main.py:309] - 请求处理异常: 'Settings' object has no attribute 'VERSION'
2025-06-03 09:37:48,742 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:37:48,742 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:37:48,743 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:37:48,755 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:37:48,755 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:37:48,755 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:37:50,736 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:37:50,736 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:37:50,736 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:37:50,931 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:37:50,935 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:37:51,004 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:37:51,694 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:37:52,340 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:37:52,340 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:37:52,424 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:37:52,425 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:37:52,425 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:37:52,792 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:37:52,804 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:37:52,830 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:37:53,735 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:37:54,381 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:37:54,382 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:38:37,855 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:38:37,855 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:38:37,855 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:38:37,863 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:38:37,863 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:38:37,864 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:38:39,422 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:38:39,422 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:38:39,422 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:38:39,690 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:38:39,697 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:38:39,714 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:38:40,515 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:38:41,152 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:38:41,152 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:38:41,558 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:38:41,559 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:38:41,559 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:38:41,851 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:38:41,856 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:38:41,939 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:38:43,085 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:38:43,733 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:38:43,734 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:38:58,845 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:38:58,846 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:38:58,846 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:38:58,886 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:38:58,886 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:38:58,886 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:39:00,545 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:39:00,545 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:39:00,545 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:39:00,802 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:39:00,836 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:39:00,840 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:39:01,625 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:39:02,258 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:39:02,258 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:39:02,483 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:39:02,483 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:39:02,484 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:39:02,837 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:39:02,846 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:39:02,884 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:39:03,698 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:39:04,349 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:39:04,350 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:41:37,852 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:41:37,854 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:41:37,854 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:41:37,955 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:41:37,956 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:41:37,956 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:41:39,630 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:41:39,630 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:41:39,630 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:41:39,936 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:41:39,939 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:41:39,998 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:41:40,747 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:41:41,376 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:41:41,376 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:41:41,516 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:41:41,516 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:41:41,517 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:41:41,939 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:41:41,944 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:41:41,973 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:41:43,075 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:41:43,723 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:41:43,723 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:41:50,707 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:41:50,707 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:41:50,707 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:41:50,735 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:41:50,735 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:41:50,735 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:41:52,266 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:41:52,266 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:41:52,266 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:41:52,579 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:41:52,599 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:41:52,604 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:41:53,492 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:41:54,125 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:41:54,125 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:41:54,376 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:41:54,376 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:41:54,377 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:41:54,704 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:41:54,711 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:41:54,797 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:41:55,988 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:41:56,638 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:41:56,639 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:43:04,103 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:43:04,103 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:43:04,103 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:43:04,423 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:43:04,433 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:43:04,548 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:43:05,391 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:43:06,065 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:43:06,066 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:45:35,943 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:45:35,943 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:45:35,944 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:45:36,297 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:45:36,347 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:45:36,357 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:45:37,407 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:45:38,050 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:45:38,051 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:47:59,179 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:47:59,179 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:47:59,179 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:47:59,185 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:47:59,185 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:47:59,185 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:48:01,269 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:48:01,269 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:48:01,269 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:48:01,695 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:48:01,732 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:48:01,735 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:48:02,858 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:48:03,065 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:48:03,065 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:48:03,066 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:48:03,412 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:48:03,427 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:48:03,505 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:48:03,506 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:48:03,529 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:48:04,561 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:48:05,238 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:48:05,238 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:48:08,663 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:48:08,663 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:48:08,663 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:48:08,673 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:48:08,673 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:48:08,674 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:48:10,303 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:48:10,304 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:48:10,304 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:48:10,536 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:48:10,541 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:48:10,604 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:48:11,305 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:48:12,028 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:48:12,028 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:48:12,330 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:48:12,331 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:48:12,331 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:48:12,724 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:48:12,728 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:48:12,815 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:48:13,720 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:48:14,424 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:48:14,424 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:48:26,183 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:48:26,183 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:48:26,183 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:48:26,254 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:48:26,255 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:48:26,255 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:48:28,169 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:48:28,169 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:48:28,169 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:48:28,424 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:48:28,430 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:48:28,547 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:48:29,343 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:48:30,132 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:48:30,133 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:48:30,256 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:48:30,257 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:48:30,257 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:48:30,665 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:48:30,674 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:48:30,811 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:48:30,842 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:48:30,842 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:48:30,842 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:48:32,376 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:48:32,376 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:48:32,376 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:48:32,426 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:48:32,717 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:48:32,752 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:48:32,755 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:48:33,074 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:48:33,074 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:48:33,075 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:48:33,075 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:48:33,076 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:48:33,840 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:48:34,475 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:48:34,475 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:48:36,616 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:48:36,616 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:48:36,617 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:48:36,919 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:48:36,922 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:48:36,993 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:48:37,869 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:48:38,506 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:48:38,506 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:49:01,736 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:49:01,736 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:49:01,736 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:49:01,736 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:49:01,736 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:49:01,736 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:49:05,199 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:49:05,199 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:49:05,199 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:49:05,532 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:49:05,536 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:49:05,588 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:49:06,402 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:49:06,726 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:49:06,726 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:49:06,727 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:49:07,036 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:49:07,037 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:49:07,097 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:49:07,111 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:49:07,114 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:49:08,106 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:49:08,748 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:49:08,748 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:49:35,254 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:49:35,255 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:49:35,255 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:49:35,255 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:49:35,255 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:49:35,256 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:49:37,195 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:49:37,195 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:49:37,195 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:49:37,539 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:49:37,544 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:49:37,696 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:49:38,639 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:49:38,949 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:49:38,950 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:49:38,950 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:49:39,279 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:49:39,279 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:49:39,338 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:49:39,344 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:49:39,362 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:49:40,466 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:49:41,177 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:49:41,177 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:49:45,829 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:49:45,830 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:49:45,830 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:49:45,855 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:49:45,855 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:49:45,855 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:49:48,521 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:49:48,521 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:49:48,521 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:49:48,755 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:49:48,760 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:49:48,819 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:49:49,713 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:49:49,983 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:49:49,983 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:49:49,984 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:49:50,340 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:49:50,340 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:49:50,341 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:49:50,348 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:49:50,377 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:49:51,077 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:49:51,718 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:49:51,718 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:49:57,718 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:49:57,719 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:49:57,720 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:49:57,780 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:49:57,781 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:49:57,781 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:49:59,809 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:49:59,810 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:49:59,810 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:50:00,267 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:50:00,281 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:50:00,371 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:50:01,119 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:50:01,758 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:50:01,758 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:50:01,806 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:50:01,806 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:50:01,807 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:50:02,129 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:50:02,135 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:50:02,274 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:50:03,421 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:50:04,093 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:50:04,094 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:50:14,714 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:50:14,714 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:50:14,715 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:50:14,787 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:50:14,787 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:50:14,787 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:50:16,864 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:50:16,865 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:50:16,865 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:50:17,269 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:50:17,275 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:50:17,360 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:50:18,172 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:50:18,722 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:50:18,722 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:50:18,723 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:50:18,795 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:50:18,795 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:50:19,058 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:50:19,063 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:50:19,142 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:50:20,041 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:50:20,715 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:50:20,715 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:50:27,294 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:50:27,294 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:50:27,294 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:50:27,300 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:50:27,300 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:50:27,301 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:50:29,257 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:50:29,258 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:50:29,258 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:50:29,531 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:50:29,547 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:50:29,551 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:50:30,438 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:50:31,067 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:50:31,067 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:50:31,338 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:50:31,338 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:50:31,339 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:50:31,669 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:50:31,694 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:50:31,697 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:50:32,455 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:50:33,103 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:50:33,104 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:50:38,142 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:50:38,142 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:50:38,142 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:50:38,159 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:50:38,159 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:50:38,159 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:50:40,012 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:50:40,012 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:50:40,013 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:50:40,275 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:50:40,285 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:50:40,308 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:50:41,249 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:50:41,886 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:50:41,886 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:50:42,105 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:50:42,106 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:50:42,107 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:50:42,485 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:50:42,490 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:50:42,516 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:50:43,599 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:50:44,233 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:50:44,234 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:50:45,521 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:50:45,522 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:50:45,522 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:50:45,550 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:50:45,551 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:50:45,551 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:50:47,390 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:50:47,390 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:50:47,390 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:50:47,642 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:50:47,648 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:50:47,687 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:50:48,554 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:50:49,188 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:50:49,188 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:50:49,403 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:50:49,403 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:50:49,404 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:50:49,805 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:50:49,877 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:50:49,881 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:50:50,979 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:50:51,631 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:50:51,631 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:51:29,737 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:51:29,738 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:51:29,738 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:51:29,787 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:51:29,788 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:51:29,788 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:51:31,631 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:51:31,631 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:51:31,632 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:51:31,936 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:51:31,962 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:51:31,966 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:51:32,814 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:51:33,343 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:51:33,343 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:51:33,344 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:51:33,454 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:51:33,455 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:51:33,607 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:51:33,614 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:51:33,764 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:51:34,925 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:51:35,575 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:51:35,576 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:52:11,671 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 09:53:16,700 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:53:16,700 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:53:16,700 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:53:16,704 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:53:16,705 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:53:16,705 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:53:19,274 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:53:19,274 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:53:19,275 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:53:19,728 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:53:19,735 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:53:19,912 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:53:20,825 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:53:20,825 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:53:20,826 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:53:21,039 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:53:21,219 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:53:21,238 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:53:21,361 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:53:21,706 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:53:21,707 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:53:22,839 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:53:23,535 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:53:23,535 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:53:27,977 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:53:27,977 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:53:27,977 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:53:27,978 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:53:27,979 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:53:27,979 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:53:29,857 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:53:29,857 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:53:29,858 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:53:30,104 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:53:30,110 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:53:30,130 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:53:31,053 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:53:31,698 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:53:31,698 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:53:31,911 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:53:31,911 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:53:31,912 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:53:32,361 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:53:32,399 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:53:32,426 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:53:33,368 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:53:34,073 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:53:34,074 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:53:49,150 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:53:49,150 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:53:49,151 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:53:49,195 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:53:49,195 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:53:49,195 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:53:51,062 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:53:51,062 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:53:51,062 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:53:51,364 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:53:51,371 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:53:51,420 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:53:52,471 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:53:52,895 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:53:52,895 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:53:52,896 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:53:53,135 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:53:53,135 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:53:53,302 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:53:53,308 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:53:53,334 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:53:54,325 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:53:54,987 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:53:54,987 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:54:03,545 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:54:03,546 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:54:03,546 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:54:03,578 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:54:03,579 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:54:03,579 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:54:05,769 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:54:05,769 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:54:05,769 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:54:06,072 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:54:06,077 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:54:06,144 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:54:07,177 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:54:07,519 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:54:07,519 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:54:07,520 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:54:07,811 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:54:07,811 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:54:07,855 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:54:07,859 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:54:07,890 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:54:08,857 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:54:09,494 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:54:09,495 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:54:14,660 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:54:14,661 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:54:14,661 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:54:14,688 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:54:14,688 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:54:14,688 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:54:16,554 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:54:16,554 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:54:16,554 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:54:16,827 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:54:16,843 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:54:16,847 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:54:17,714 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:54:18,346 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:54:18,347 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:54:18,549 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:54:18,550 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:54:18,550 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:54:18,977 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:54:19,001 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:54:19,007 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:54:19,882 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:54:20,521 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:54:20,521 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:54:42,161 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:54:42,161 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:54:42,161 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:54:42,311 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:54:42,312 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:54:42,312 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:54:44,198 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:54:44,198 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:54:44,199 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:54:44,447 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:54:44,459 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:54:44,535 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:54:45,295 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:54:45,926 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:54:45,926 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:54:46,093 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:54:46,093 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:54:46,094 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:54:46,578 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:54:46,602 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:54:46,794 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:54:46,936 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:54:46,936 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:54:46,936 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:54:47,941 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:54:48,414 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:54:48,414 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:54:48,414 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:54:48,585 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:54:48,585 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:54:48,587 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:54:48,587 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:54:48,587 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:54:48,787 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:54:48,824 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:54:48,865 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:54:49,966 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:54:50,631 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:54:50,631 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:54:52,252 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:54:52,252 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:54:52,253 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:54:52,735 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:54:52,740 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:54:52,799 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:54:54,018 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:54:54,166 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:54:54,166 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:54:54,166 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:54:54,653 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:54:54,653 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:54:54,654 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:54:54,655 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:54:54,655 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:54:56,106 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:54:56,107 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:54:56,107 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:54:56,397 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:54:56,405 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:54:56,486 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:54:57,302 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:54:57,934 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:54:57,934 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:54:58,699 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:54:58,699 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:54:58,699 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:54:59,114 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:54:59,123 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:54:59,132 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:55:00,074 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:55:00,716 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:55:00,716 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:55:05,164 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:55:05,164 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:55:05,165 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:55:05,204 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:55:05,204 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:55:05,204 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:55:07,881 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:55:07,882 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:55:07,882 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:55:08,522 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:55:08,544 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:55:08,549 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:55:09,471 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:55:09,471 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:55:09,472 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:55:09,709 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:55:09,848 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:55:09,855 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:55:09,966 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:55:10,362 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:55:10,362 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:55:11,132 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:55:11,823 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:55:11,824 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:56:21,136 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:56:21,137 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:56:21,138 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:56:21,160 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:56:21,160 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:56:21,161 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:56:23,380 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:56:23,380 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:56:23,380 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:56:23,932 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:56:24,021 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:56:24,025 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:56:24,884 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:56:25,105 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:56:25,105 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:56:25,106 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:56:25,461 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:56:25,480 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:56:25,545 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:56:25,545 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:56:25,556 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:56:26,553 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:56:27,190 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:56:27,191 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:56:34,643 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:56:34,643 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:56:34,643 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:56:34,669 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:56:34,670 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:56:34,670 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:56:37,342 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:56:37,343 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:56:37,343 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:56:37,694 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:56:37,704 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:56:37,765 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:56:38,574 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:56:38,633 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:56:38,633 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:56:38,634 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:56:38,935 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:56:38,941 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:56:38,970 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:56:39,207 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:56:39,207 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:56:39,743 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:56:40,383 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:56:40,383 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:56:52,037 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:56:52,037 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:56:52,037 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:56:52,104 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 09:56:52,104 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-06-03 09:56:52,105 - INFO - app - [main.py:530] - 应用已完全关闭
2025-06-03 09:56:54,101 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:56:54,101 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:56:54,102 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:56:54,427 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:56:54,461 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:56:54,465 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:56:55,343 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:56:55,973 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:56:55,974 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:56:56,067 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:56:56,067 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:56:56,068 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:56:56,424 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:56:56,437 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:56:56,442 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:56:57,485 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:56:58,153 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:56:58,154 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:58:29,737 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 09:58:49,042 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 09:58:49,043 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 09:58:49,043 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 09:58:49,429 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 09:58:49,436 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 09:58:49,514 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 09:58:50,478 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 09:58:51,134 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 09:58:51,135 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 09:59:12,126 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 10:00:36,004 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:00:36,005 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:00:36,005 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:00:36,005 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:00:36,005 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:00:36,005 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:00:36,005 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:00:36,006 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:00:38,178 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:00:38,178 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:00:38,178 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:00:38,393 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:00:38,470 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:00:38,474 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:00:39,617 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:00:40,048 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:00:40,048 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:00:40,049 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:00:40,286 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:00:40,286 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:00:40,479 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:00:40,491 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:00:40,533 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:00:41,422 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:00:42,068 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:00:42,069 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:00:48,988 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:00:48,989 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:00:48,989 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:00:48,990 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:00:49,044 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:00:49,044 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:00:49,045 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:00:49,045 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:00:51,661 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:00:51,661 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:00:51,661 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:00:51,893 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:00:51,897 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:00:51,928 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:00:52,646 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:00:53,280 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:00:53,281 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:00:53,428 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:00:53,429 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:00:53,429 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:00:53,897 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:00:53,929 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:00:53,938 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:00:54,871 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:00:55,540 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:00:55,541 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:01:00,861 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:01:00,861 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:01:00,861 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:01:00,862 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:01:00,906 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:01:00,910 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:01:00,921 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:01:00,949 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:01:03,887 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:01:03,887 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:01:03,887 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:01:04,186 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:01:04,193 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:01:04,318 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:01:05,193 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:01:05,210 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:01:05,210 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:01:05,211 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:01:05,499 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:01:05,503 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:01:05,634 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:01:05,826 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:01:05,827 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:01:06,374 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:01:07,020 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:01:07,021 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:01:15,411 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:01:15,412 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:01:15,413 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:01:15,413 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:01:15,523 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:01:15,523 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:01:15,523 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:01:15,523 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:01:18,841 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:01:18,841 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:01:18,842 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:01:19,121 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:01:19,157 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:01:19,161 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:01:19,999 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:01:20,328 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:01:20,329 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:01:20,329 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:01:20,621 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:01:20,621 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:01:20,644 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:01:20,665 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:01:20,669 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:01:21,501 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:01:22,151 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:01:22,151 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:01:30,821 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:01:30,823 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:01:30,823 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:01:30,823 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:01:30,845 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:01:30,846 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:01:30,846 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:01:30,847 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:01:32,507 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:01:32,507 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:01:32,507 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:01:32,714 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:01:32,716 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:01:32,780 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:01:33,672 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:01:34,297 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:01:34,297 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:01:34,298 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:01:34,298 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:01:34,298 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:01:34,298 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:01:36,137 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:01:36,137 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:01:36,137 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:01:36,252 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:01:36,252 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:01:36,253 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:01:36,436 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:01:36,452 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:01:36,456 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:01:36,559 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:01:36,566 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:01:36,639 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:01:37,252 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:01:37,598 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:01:37,898 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:01:37,898 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:01:38,246 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:01:38,247 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:01:44,398 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:01:44,398 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:01:44,399 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:01:44,399 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:01:44,441 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:01:44,441 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:01:44,442 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:01:44,442 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:01:46,236 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:01:46,236 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:01:46,236 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:01:46,619 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:01:46,678 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:01:46,682 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:01:47,927 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:01:48,571 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:01:48,571 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:01:48,572 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:01:48,572 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:01:48,572 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:01:48,572 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:01:49,112 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:01:49,112 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:01:49,113 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:01:49,567 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:01:49,580 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:01:49,637 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:01:50,020 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:01:50,021 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:01:50,021 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:01:50,360 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:01:50,374 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:01:50,379 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:01:50,580 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:01:51,206 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:01:51,234 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:01:51,234 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:01:51,834 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:01:51,834 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:04:11,381 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 10:06:27,387 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:06:27,388 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:06:27,390 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:06:27,389 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:06:27,390 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:06:27,390 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:06:27,390 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:06:27,390 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:06:29,375 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:06:29,375 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:06:29,375 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:06:29,635 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:06:29,655 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:06:29,804 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:06:30,793 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:06:31,009 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:06:31,010 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:06:31,011 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:06:31,370 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:06:31,401 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:06:31,405 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:06:31,429 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:06:31,430 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:06:32,529 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:06:33,175 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:06:33,175 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:06:51,889 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:06:51,889 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:06:51,889 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:06:51,890 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:06:51,954 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:06:51,954 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:06:51,954 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:06:51,954 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:06:53,705 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:06:53,705 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:06:53,705 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:06:54,196 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:06:54,218 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:06:54,223 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:06:55,210 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:06:55,374 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:06:55,375 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:06:55,375 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:06:55,771 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:06:55,777 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:06:55,843 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:06:55,844 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:06:55,858 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:06:57,069 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:06:57,734 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:06:57,735 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:08:06,338 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 10:08:43,246 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:08:43,247 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:08:43,247 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:08:43,247 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:08:43,248 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:08:43,249 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:08:43,249 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:08:43,250 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:08:45,372 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:08:45,372 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:08:45,373 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:08:45,699 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:08:45,704 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:08:45,759 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:08:46,771 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:08:47,318 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:08:47,318 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:08:47,319 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:08:47,455 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:08:47,455 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:08:47,735 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:08:47,741 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:08:47,772 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:08:48,881 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:08:49,542 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:08:49,543 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:08:52,818 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 10:09:50,309 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:09:50,310 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:09:50,310 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:09:50,311 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:09:50,369 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:09:50,369 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:09:50,370 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:09:50,370 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:09:52,236 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:09:52,236 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:09:52,236 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:09:52,619 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:09:52,628 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:09:52,662 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:09:53,693 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:09:53,945 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:09:53,946 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:09:53,946 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:09:54,321 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:09:54,321 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:09:54,413 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:09:54,421 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:09:54,476 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:09:55,415 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:09:56,061 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:09:56,063 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:10:22,345 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:10:22,345 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:10:22,345 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:10:22,345 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:10:22,379 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:10:22,380 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:10:22,380 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:10:22,381 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:10:24,334 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:10:24,334 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:10:24,335 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:10:24,590 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:10:24,600 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:10:24,625 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:10:25,543 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:10:26,172 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:10:26,172 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:10:26,172 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:10:26,172 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:10:26,172 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:10:26,173 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:10:27,642 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:10:27,642 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:10:27,643 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:10:27,880 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:10:27,884 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:10:27,905 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:10:28,336 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:10:28,336 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:10:28,337 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:10:28,808 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:10:28,817 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:10:28,840 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:10:28,867 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:10:29,471 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:10:29,471 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:10:29,527 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:10:30,169 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:10:30,170 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:10:46,056 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:10:46,056 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:10:46,057 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:10:46,057 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:10:46,057 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:10:46,057 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:10:46,057 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:10:46,058 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:10:47,610 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:10:47,610 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:10:47,611 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:10:47,872 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:10:47,876 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:10:47,909 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:10:48,633 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:10:49,264 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:10:49,264 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:10:49,265 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:10:49,265 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:10:49,265 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:10:49,265 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:10:50,528 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:10:50,529 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:10:50,529 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:10:50,763 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:10:50,763 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:10:50,763 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:10:50,856 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:10:50,874 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:10:50,880 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:10:51,080 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:10:51,089 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:10:51,094 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:10:51,751 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:10:51,873 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:10:52,415 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:10:52,415 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:10:52,522 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:10:52,522 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:10:56,363 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:10:56,363 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:10:56,363 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:10:56,364 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:10:56,364 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:10:56,365 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:10:56,365 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:10:56,366 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:10:59,128 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:10:59,128 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:10:59,128 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:10:59,314 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:10:59,319 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:10:59,453 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:11:00,201 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:11:00,657 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:11:00,657 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:11:00,658 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:11:00,832 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:11:00,833 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:11:00,978 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:11:00,984 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:11:01,023 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:11:01,784 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:11:02,427 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:11:02,428 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:11:03,039 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:11:03,039 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:11:03,040 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:11:03,041 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:11:03,052 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:11:03,052 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:11:03,052 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:11:03,053 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:11:04,682 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:11:04,682 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:11:04,683 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:11:04,938 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:11:04,988 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:11:04,991 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:11:05,816 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:11:06,446 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:11:06,447 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:11:06,685 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:11:06,685 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:11:06,686 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:11:06,972 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:11:06,979 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:11:07,028 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:11:07,661 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:11:07,662 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:11:07,662 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:11:07,664 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:11:08,164 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:11:08,817 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:11:08,818 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:11:08,819 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:11:08,819 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:11:08,820 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:11:08,820 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:11:09,535 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:11:09,535 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:11:09,535 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:11:09,859 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:11:09,865 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:11:09,888 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:11:10,681 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:11:11,306 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:11:11,306 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:11:12,730 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:11:12,730 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:11:12,730 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:11:13,055 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:11:13,061 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:11:13,136 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:11:14,079 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:11:14,714 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:11:14,715 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:11:15,624 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:11:15,624 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:11:15,625 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:11:15,626 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:11:15,649 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:11:15,649 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:11:15,649 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:11:15,649 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:11:17,550 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:11:17,556 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:11:17,560 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:11:17,906 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:11:17,910 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:11:17,960 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:11:18,679 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:11:19,309 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:11:19,309 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:11:19,562 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:11:19,562 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:11:19,563 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:11:19,816 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:11:19,820 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:11:19,944 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:11:20,723 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:11:21,370 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:11:21,371 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:11:32,860 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:11:32,860 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:11:32,860 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:11:32,861 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:11:32,889 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:11:32,890 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:11:32,890 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:11:32,891 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:11:34,414 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:11:34,414 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:11:34,415 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:11:34,727 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:11:34,734 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:11:34,785 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:11:35,763 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:11:36,422 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:11:36,422 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:11:36,724 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:11:36,725 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:11:36,725 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:11:37,078 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:11:37,095 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:11:37,101 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:11:37,993 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:11:38,643 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:11:38,644 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:11:44,813 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 10:18:33,263 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 10:19:01,491 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 10:20:20,333 - ERROR - app - [main.py:309] - 请求处理异常: object dict can't be used in 'await' expression
2025-06-03 10:22:56,438 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:22:56,441 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:22:56,441 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:22:56,442 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:22:56,538 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:22:56,539 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:22:56,539 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:22:56,539 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:22:58,451 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:22:58,451 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:22:58,451 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:22:58,868 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:22:58,873 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:22:58,930 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:22:59,766 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:22:59,981 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:22:59,982 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:22:59,982 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:23:00,392 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:23:00,397 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:23:00,400 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:23:00,400 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:23:00,436 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:23:01,389 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:23:02,048 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:23:02,049 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:23:27,527 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:23:27,529 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:23:27,530 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:23:27,530 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:23:27,585 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:23:27,585 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:23:27,586 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:23:27,586 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:23:29,505 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:23:29,505 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:23:29,505 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:23:29,906 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:23:29,912 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:23:29,946 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:23:31,038 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:23:31,276 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:23:31,276 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:23:31,277 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:23:31,724 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:23:31,724 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:23:31,879 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:23:31,895 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:23:32,006 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:23:33,222 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:23:33,924 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:23:33,924 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:23:39,699 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:23:39,700 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:23:39,700 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:23:39,701 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:23:39,738 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:23:39,739 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:23:39,739 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:23:39,739 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:23:41,473 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:23:41,474 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:23:41,474 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:23:41,860 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:23:41,864 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:23:42,013 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:23:43,377 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:23:43,377 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:23:43,378 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:23:43,482 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:23:43,899 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:23:43,908 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:23:44,041 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:23:44,201 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:23:44,202 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:23:45,451 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:23:46,137 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:23:46,138 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:23:48,555 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:23:48,555 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:23:48,555 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:23:48,556 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:23:48,567 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:23:48,568 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:23:48,569 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:23:48,569 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:23:50,142 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:23:50,142 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:23:50,142 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:23:50,460 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:23:50,490 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:23:50,494 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:23:51,249 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:23:51,891 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:23:51,891 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:23:52,210 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:23:52,210 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:23:52,211 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:23:52,677 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:23:52,721 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:23:52,724 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:23:53,851 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:23:54,539 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:23:54,539 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:25:53,672 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:25:53,673 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:25:53,673 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:25:53,674 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:25:53,733 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:25:53,733 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:25:53,734 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:25:53,734 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:25:55,792 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:25:55,792 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:25:55,792 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:25:56,094 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:25:56,098 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:25:56,189 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:25:57,519 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:25:57,519 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:25:57,520 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:25:57,534 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:25:57,786 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:25:57,793 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:25:58,017 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:25:58,187 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:25:58,188 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:25:59,284 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:25:59,961 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:25:59,962 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:26:23,967 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:26:23,967 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:26:23,967 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:26:23,968 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:26:24,045 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:26:24,047 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:26:24,048 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:26:24,048 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:26:25,745 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:26:25,745 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:26:25,745 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:26:25,965 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:26:25,971 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:26:26,105 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:26:27,113 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:26:27,703 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:26:27,703 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:26:27,704 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:26:27,743 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:26:27,743 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:26:28,152 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:26:28,155 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:26:28,190 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:26:29,055 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:26:29,698 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:26:29,699 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:26:44,676 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:26:44,677 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:26:44,678 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:26:44,679 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:26:44,764 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:26:44,765 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:26:44,765 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:26:44,765 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:26:46,882 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:26:46,882 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:26:46,882 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:26:47,204 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:26:47,221 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:26:47,225 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:26:48,283 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:26:48,774 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:26:48,775 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:26:48,776 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:26:48,916 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:26:48,916 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:26:49,098 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:26:49,103 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:26:49,184 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:26:50,319 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:26:51,016 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:26:51,016 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:27:34,411 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:27:34,412 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:27:34,412 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:27:34,412 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:27:34,468 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:27:34,469 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:27:34,470 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:27:34,470 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:27:36,365 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:27:36,365 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:27:36,365 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:27:36,630 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:27:36,671 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:27:36,778 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:27:37,671 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:27:38,148 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:27:38,148 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:27:38,149 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:27:38,304 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:27:38,304 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:27:38,434 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:27:38,484 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:27:38,492 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:27:39,590 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:27:40,234 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:27:40,234 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:27:47,522 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:27:47,523 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:27:47,523 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:27:47,523 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:27:47,565 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:27:47,565 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:27:47,565 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:27:47,565 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:27:49,522 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:27:49,522 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:27:49,522 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:27:49,944 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:27:49,949 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:27:50,018 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:27:51,117 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:27:51,545 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:27:51,546 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:27:51,546 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:27:51,790 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:27:51,791 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:27:51,973 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:27:51,977 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:27:52,050 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:27:53,368 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:27:54,035 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:27:54,036 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:28:06,564 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:28:06,564 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:28:06,565 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:28:06,565 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:28:06,603 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:28:06,604 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:28:06,604 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:28:06,604 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:28:08,548 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:28:08,549 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:28:08,549 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:28:08,815 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:28:08,821 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:28:08,963 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:28:10,080 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:28:10,168 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:28:10,168 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:28:10,169 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:28:10,672 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:28:10,681 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:28:10,724 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:28:10,724 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:28:10,726 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:28:12,254 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:28:12,912 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:28:12,914 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:28:24,543 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:28:24,544 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:28:24,544 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:28:24,544 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:28:24,593 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:28:24,593 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:28:24,594 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:28:24,594 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:28:26,151 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:28:26,151 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:28:26,151 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:28:26,382 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:28:26,392 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:28:26,397 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:28:27,210 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:28:27,845 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:28:27,845 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:28:28,222 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:28:28,222 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:28:28,223 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:28:28,625 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:28:28,654 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:28:28,788 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:28:29,778 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:28:30,426 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:28:30,427 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:28:36,533 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:28:36,534 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:28:36,534 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:28:36,535 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:28:36,568 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:28:36,569 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:28:36,569 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:28:36,569 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:28:38,297 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:28:38,297 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:28:38,297 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:28:38,568 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:28:38,598 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:28:38,604 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:28:39,540 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:28:40,181 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:28:40,181 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:28:40,249 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:28:40,249 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:28:40,251 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:28:40,573 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:28:40,593 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:28:40,732 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:28:41,989 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:28:42,631 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:28:42,631 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:34:47,375 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:34:47,377 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:34:47,377 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:34:47,378 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:34:51,860 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:34:51,860 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:34:51,861 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:34:52,248 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:34:52,265 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:34:52,270 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:34:53,514 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:34:54,164 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:34:54,165 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:35:09,150 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:35:09,151 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:35:09,151 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:35:09,152 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:35:13,479 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:35:13,479 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:35:13,480 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:35:13,896 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:35:13,918 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:35:13,927 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:35:14,806 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:35:15,458 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:35:15,459 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:35:34,481 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:35:34,482 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:35:34,482 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:35:34,483 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:35:38,096 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:35:38,096 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:35:38,097 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:35:38,402 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:35:38,408 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:35:38,522 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:35:39,816 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:35:40,462 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:35:40,462 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:35:55,735 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:35:55,735 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:35:55,735 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:35:55,736 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:36:00,073 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:36:00,074 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:36:00,074 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:36:00,414 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:36:00,424 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:36:00,539 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:36:02,919 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:36:03,650 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:36:03,651 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:36:13,778 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-06-03 10:36:13,778 - INFO - app - [main.py:519] - 已停止认证配置刷新任务
2025-06-03 10:36:13,779 - INFO - app - [main.py:536] - 系统指标收集已停止
2025-06-03 10:36:13,779 - INFO - app - [main.py:558] - 应用已完全关闭
2025-06-03 10:36:19,400 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:36:19,401 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:36:19,401 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:36:19,898 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:36:19,913 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:36:19,918 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:36:21,252 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:36:21,894 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:36:21,894 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-06-03 10:36:30,712 - INFO - app - [main.py:585] - 使用端口: 8001
2025-06-03 10:36:30,809 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-06-03 10:36:30,809 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-06-03 10:36:30,809 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-06-03 10:36:31,157 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-06-03 10:36:31,162 - INFO - app - [main.py:459] - 动态配置加载完成
2025-06-03 10:36:31,200 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-06-03 10:36:32,528 - INFO - app - [main.py:421] - 数据库初始化完成
2025-06-03 10:36:33,210 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-06-03 10:36:33,210 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务

2025-05-20 10:35:54,067 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 10:35:54,068 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 10:35:54,068 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 10:35:54,369 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 10:35:54,383 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 10:35:54,388 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 10:35:55,308 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 10:35:55,958 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 10:35:55,959 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 10:42:44,301 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 10:42:44,302 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 10:42:44,303 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 10:42:44,687 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 10:42:44,692 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 10:42:44,792 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 10:42:47,267 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 10:42:47,918 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 10:42:47,918 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 10:44:10,503 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 10:44:10,505 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 10:44:10,505 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 10:44:26,577 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 10:44:26,578 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 10:44:26,579 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 10:44:26,986 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 10:44:26,989 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 10:44:27,142 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 10:44:28,279 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 10:44:28,941 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 10:44:28,943 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 10:55:47,547 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 10:55:47,548 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 10:55:47,549 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 10:55:48,016 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 10:55:48,022 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 10:55:48,062 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 10:55:49,039 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 10:55:49,679 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 10:55:49,680 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 10:56:32,731 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 10:56:32,733 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 10:56:32,734 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 10:56:44,656 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 10:56:44,657 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 10:56:44,658 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 10:56:44,939 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 10:56:44,944 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 10:56:45,060 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 10:56:45,906 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 10:56:46,575 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 10:56:46,575 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 10:57:44,678 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 10:57:44,680 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 10:57:44,680 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 10:57:48,762 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 10:57:48,763 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 10:57:48,763 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 10:57:49,138 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 10:57:49,144 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 10:57:49,189 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 10:57:49,903 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 10:57:50,544 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 10:57:50,545 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 10:57:52,875 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 10:57:52,876 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 10:57:52,876 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 10:57:57,001 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 10:57:57,002 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 10:57:57,002 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 10:57:57,336 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 10:57:57,346 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 10:57:57,449 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 10:57:59,005 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 10:57:59,671 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 10:57:59,672 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 10:58:39,091 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 10:58:39,094 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 10:58:39,095 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 10:58:43,190 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 10:58:43,190 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 10:58:43,191 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 10:58:43,586 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 10:58:43,597 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 10:58:43,835 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 10:58:44,655 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 10:58:45,297 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 10:58:45,297 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:00:35,725 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:00:35,726 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:00:35,726 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:00:36,005 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:00:36,011 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:00:36,062 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:00:36,990 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:00:37,633 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:00:37,634 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:02:34,796 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:02:34,797 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:02:34,798 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:12:32,388 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:12:32,390 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:12:32,390 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:12:32,714 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:12:32,757 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:12:32,764 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:12:33,541 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:12:34,194 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:12:34,195 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:13:03,767 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:13:03,768 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:13:03,768 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:13:10,701 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:13:10,701 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:13:10,702 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:13:11,024 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:13:11,029 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:13:11,059 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:13:11,816 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:13:12,460 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:13:12,461 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:14:20,697 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:14:20,700 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:14:20,701 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:14:34,540 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:14:34,541 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:14:34,543 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:14:34,867 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:14:34,905 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:14:34,910 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:14:35,742 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:14:36,381 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:14:36,381 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:17:37,334 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:17:37,335 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:17:37,335 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:17:37,643 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:17:37,648 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:17:37,733 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:17:38,530 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:17:39,182 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:17:39,183 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:18:34,277 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:18:34,280 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:18:34,280 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:18:41,700 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:18:41,701 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:18:41,701 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:18:42,050 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:18:42,055 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:18:42,123 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:18:42,993 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:18:43,622 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:18:43,623 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:18:44,534 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:18:44,534 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:18:44,534 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:18:50,252 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:18:50,253 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:18:50,253 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:18:50,580 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:18:50,586 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:18:50,609 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:18:51,425 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:18:52,064 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:18:52,064 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:18:52,065 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:18:52,066 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:18:52,066 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:18:55,969 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:18:55,970 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:18:55,970 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:18:56,246 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:18:56,253 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:18:56,340 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:18:57,340 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:18:57,974 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:18:57,974 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:18:57,976 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:18:57,976 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:18:57,977 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:19:02,260 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:19:02,260 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:19:02,261 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:19:02,725 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:19:02,736 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:19:02,781 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:19:03,605 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:19:04,272 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:19:04,272 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:19:04,273 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:19:04,274 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:19:04,274 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:19:08,108 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:19:08,108 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:19:08,109 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:19:08,464 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:19:08,485 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:19:08,492 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:19:09,387 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:19:10,038 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:19:10,039 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:19:22,693 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:19:22,694 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:19:22,694 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:19:26,583 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:19:26,583 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:19:26,584 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:19:27,009 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:19:27,037 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:19:27,048 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:19:27,890 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:19:28,543 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:19:28,543 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:19:57,181 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:19:57,181 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:19:57,182 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:20:01,217 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:20:01,217 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:20:01,218 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:20:01,646 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:20:01,651 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:20:01,710 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:20:02,783 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:20:03,463 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:20:03,463 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:20:45,020 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:20:45,021 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:20:45,021 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:20:49,288 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:20:49,289 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:20:49,290 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:20:49,623 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:20:49,635 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:20:49,748 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:20:50,924 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:20:51,588 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:20:51,589 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:21:44,479 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:21:44,481 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:21:44,482 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:21:48,497 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:21:48,497 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:21:48,498 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:21:48,810 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:21:48,857 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:21:48,865 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:21:49,917 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:21:50,594 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:21:50,595 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:24:40,236 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:24:40,240 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:24:40,241 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:24:44,209 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:24:44,209 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:24:44,210 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:24:44,598 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:24:44,604 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:24:44,629 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:24:45,395 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:24:46,767 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:24:46,768 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:25:12,569 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:25:12,570 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:25:12,570 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:25:16,474 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:25:16,474 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:25:16,475 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:25:16,807 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:25:16,825 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:25:16,832 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:25:17,756 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:25:18,414 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:25:18,415 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:29:53,855 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:29:53,856 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:29:53,857 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:29:57,798 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:29:57,799 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:29:57,799 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:29:58,121 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:29:58,124 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:29:58,152 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:29:59,102 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:29:59,745 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:29:59,745 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:30:15,123 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:30:15,124 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:30:15,125 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:30:19,029 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:30:19,030 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:30:19,030 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:30:19,343 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:30:19,347 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:30:19,405 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:30:20,181 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:30:20,814 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:30:20,815 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:30:51,782 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:30:51,783 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:30:51,784 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:30:55,753 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:30:55,754 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:30:55,754 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:30:56,015 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:30:56,018 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:30:56,082 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:30:57,013 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:30:57,655 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:30:57,656 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务
2025-05-20 11:31:12,320 - INFO - app - [main.py:509] - 应用关闭，执行清理操作...
2025-05-20 11:31:12,321 - INFO - app - [main.py:515] - 已停止认证配置刷新任务
2025-05-20 11:31:12,321 - INFO - app - [main.py:530] - 应用已完全关闭
2025-05-20 11:34:06,530 - INFO - app - [main.py:412] - 开始应用启动初始化...
2025-05-20 11:34:06,531 - INFO - app - [main.py:427] - Redis连接池初始化完成
2025-05-20 11:34:06,531 - INFO - app - [main.py:434] - MinIO对象存储服务初始化完成
2025-05-20 11:34:06,811 - INFO - app.config - [config.py:310] - 成功从数据库加载系统配置，共10项
2025-05-20 11:34:06,815 - INFO - app - [main.py:459] - 动态配置加载完成
2025-05-20 11:34:06,912 - INFO - app - [main.py:475] - 认证配置初始化完成，并已启动自动刷新
2025-05-20 11:34:07,915 - INFO - app - [main.py:421] - 数据库初始化完成
2025-05-20 11:34:08,555 - INFO - app - [main.py:497] - 数据库连接状态: healthy
2025-05-20 11:34:08,555 - INFO - app - [main.py:501] - 应用启动完成: 管理后台服务

#!/bin/bash
# API测试脚本：用户登录、文件上传和获取功能测试

# 设置API地址
API_HOST="localhost:8000"
API_BASE="http://$API_HOST/api/v1"

# 创建临时文件夹
mkdir -p ./temp

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 打印带颜色的消息
print_message() {
  local color=$1
  local message=$2
  echo -e "${color}$message${NC}"
}

# 保存登录token
TOKEN_FILE="./temp/auth_token.txt"
FILE_ID_FILE="./temp/file_id.txt"
FILE_URL_FILE="./temp/file_url.txt"

print_message $BLUE "======= API测试开始 ======="

# 1. 用户登录测试
print_message $BLUE "\n[1] 测试用户登录"
print_message $BLUE "--------------------------------"

# 准备登录信息 - 使用表单格式
print_message $BLUE "执行登录请求..."
LOGIN_RESPONSE=$(curl -s -X POST \
  "$API_BASE/jwt/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=123456")

# 如果第一个登录方式失败，尝试灵活登录
if ! echo "$LOGIN_RESPONSE" | grep -q "access_token"; then
  print_message $BLUE "JWT登录失败，尝试灵活登录..."
  LOGIN_DATA='{
    "username": "admin",
    "password": "123456"
  }'
  LOGIN_RESPONSE=$(curl -s -X POST \
    "$API_BASE/auth-settings/flexible-login" \
    -H "Content-Type: application/json" \
    -d "$LOGIN_DATA")
fi

# 检查登录结果
if echo "$LOGIN_RESPONSE" | grep -q "access_token"; then
  print_message $GREEN "登录成功!"
  # 提取token
  ACCESS_TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
  echo $ACCESS_TOKEN > $TOKEN_FILE
  print_message $BLUE "Token已保存至$TOKEN_FILE"
else
  print_message $RED "登录失败: $LOGIN_RESPONSE"
  # 不退出，继续尝试匿名上传
  print_message $BLUE "将尝试无需登录的公开上传..."
fi

# 2. 文件上传测试
print_message $BLUE "\n[2] 测试文件上传"
print_message $BLUE "--------------------------------"

# 创建测试文件
TEST_FILE="./temp/test_file.txt"
echo "This is an API test file content: $(date)" > $TEST_FILE
print_message $BLUE "已创建测试文件: $TEST_FILE"

# 检查是否有登录令牌
if [ -s "$TOKEN_FILE" ]; then
  ACCESS_TOKEN=$(cat $TOKEN_FILE)
  print_message $BLUE "使用认证令牌上传文件..."
  
  # 执行文件上传 - 带认证
  UPLOAD_RESPONSE=$(curl -s -X POST \
    "$API_BASE/files/upload" \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -F "file=@$TEST_FILE" \
    -F "is_public=true" \
    -F "storage_type=minio" \
    -F "description=API测试上传(认证)")
else
  print_message $BLUE "使用匿名公开上传API..."
  
  # 执行匿名文件上传
  UPLOAD_RESPONSE=$(curl -s -X POST \
    "$API_BASE/files/public-upload" \
    -F "file=@$TEST_FILE" \
    -F "is_public=true" \
    -F "storage_type=minio" \
    -F "description=API测试上传(匿名)")
fi

# 检查上传结果
if echo "$UPLOAD_RESPONSE" | grep -q "file_url"; then
  print_message $GREEN "文件上传成功!"
  # 提取文件ID和URL
  FILE_ID=$(echo $UPLOAD_RESPONSE | grep -o '"id":"[^"]*' | cut -d'"' -f4)
  FILE_URL=$(echo $UPLOAD_RESPONSE | grep -o '"file_url":"[^"]*' | cut -d'"' -f4)
  
  echo $FILE_ID > $FILE_ID_FILE
  echo $FILE_URL > $FILE_URL_FILE
  
  print_message $BLUE "文件ID: $FILE_ID"
  print_message $BLUE "文件URL: $FILE_URL"
else
  print_message $RED "文件上传失败: $UPLOAD_RESPONSE"
fi

# 3. 文件获取测试
print_message $BLUE "\n[3] 测试文件获取"
print_message $BLUE "--------------------------------"

if [ -f $FILE_ID_FILE ]; then
  FILE_ID=$(cat $FILE_ID_FILE)
  
  # 尝试获取文件信息
  print_message $BLUE "获取文件信息..."
  
  # 如果有认证令牌，使用认证API
  if [ -s "$TOKEN_FILE" ]; then
    ACCESS_TOKEN=$(cat $TOKEN_FILE)
    FILE_INFO_RESPONSE=$(curl -s -X GET \
      "$API_BASE/files/$FILE_ID" \
      -H "Authorization: Bearer $ACCESS_TOKEN")
  else
    # 尝试直接下载文件（对于公开文件）
    print_message $BLUE "无认证令牌，尝试匿名访问下载API..."
    FILE_INFO_RESPONSE=$(curl -s -I \
      "$API_BASE/files/$FILE_ID/download")
    
    # 如果FILE_URL_FILE存在，直接使用保存的URL
    if [ -s "$FILE_URL_FILE" ]; then
      FILE_URL=$(cat $FILE_URL_FILE)
      print_message $BLUE "使用上传时返回的文件URL: $FILE_URL"
      
      # 构造一个包含正确URL的响应用于后续处理
      FILE_INFO_RESPONSE="{\"file_url\":\"$FILE_URL\"}"
    fi
  fi
  
  if echo "$FILE_INFO_RESPONSE" | grep -q "file_url"; then
    print_message $GREEN "获取文件信息成功!"
    API_FILE_URL=$(echo $FILE_INFO_RESPONSE | grep -o '"file_url":"[^"]*' | cut -d'"' -f4)
    print_message $BLUE "API返回的文件URL: $API_FILE_URL"
    
    # 检查URL格式
    if [[ $API_FILE_URL == http* ]]; then
      print_message $GREEN "文件URL格式正确 (以http开头)"
      
      # 尝试下载文件
      print_message $BLUE "\n尝试直接访问预签名URL..."
      HTTP_CODE=$(curl -s -o ./temp/downloaded_file.txt -w "%{http_code}" "$API_FILE_URL")
      
      if [ "$HTTP_CODE" -ge 200 ] && [ "$HTTP_CODE" -lt 400 ]; then
        print_message $GREEN "成功访问预签名URL! HTTP状态码: $HTTP_CODE"
        print_message $BLUE "下载的文件内容:"
        cat ./temp/downloaded_file.txt
      else
        print_message $RED "访问预签名URL失败! HTTP状态码: $HTTP_CODE"
        
        # 尝试通过API下载
        print_message $BLUE "\n尝试通过API下载接口获取文件..."
        DOWNLOAD_RESPONSE=$(curl -s -o ./temp/downloaded_file.txt -w "%{http_code}" \
          "$API_BASE/files/$FILE_ID/download")
          
        if [ "$DOWNLOAD_RESPONSE" -ge 200 ] && [ "$DOWNLOAD_RESPONSE" -lt 400 ]; then
          print_message $GREEN "通过API接口下载成功! HTTP状态码: $DOWNLOAD_RESPONSE"
          print_message $BLUE "下载的文件内容:"
          cat ./temp/downloaded_file.txt
        else
          print_message $RED "通过API接口下载失败! HTTP状态码: $DOWNLOAD_RESPONSE"
        fi
      fi
    else
      print_message $RED "文件URL格式不正确: $API_FILE_URL"
      
      # 尝试通过API直接下载
      print_message $BLUE "\n尝试通过API下载接口获取文件..."
      DOWNLOAD_RESPONSE=$(curl -s -o ./temp/downloaded_file.txt -w "%{http_code}" \
        "$API_BASE/files/$FILE_ID/download")
        
      if [ "$DOWNLOAD_RESPONSE" -ge 200 ] && [ "$DOWNLOAD_RESPONSE" -lt 400 ]; then
        print_message $GREEN "通过API接口下载成功! HTTP状态码: $DOWNLOAD_RESPONSE"
        print_message $BLUE "下载的文件内容:"
        cat ./temp/downloaded_file.txt
      else
        print_message $RED "通过API接口下载失败! HTTP状态码: $DOWNLOAD_RESPONSE"
      fi
    fi
  else
    print_message $RED "获取文件信息失败: $FILE_INFO_RESPONSE"
    
    # 尝试直接通过下载API获取文件
    print_message $BLUE "\n尝试直接通过下载API获取文件..."
    DOWNLOAD_RESPONSE=$(curl -s -o ./temp/downloaded_file.txt -w "%{http_code}" \
      "$API_BASE/files/$FILE_ID/download")
      
    if [ "$DOWNLOAD_RESPONSE" -ge 200 ] && [ "$DOWNLOAD_RESPONSE" -lt 400 ]; then
      print_message $GREEN "通过API接口下载成功! HTTP状态码: $DOWNLOAD_RESPONSE"
      print_message $BLUE "下载的文件内容:"
      cat ./temp/downloaded_file.txt
    else
      print_message $RED "通过API接口下载失败! HTTP状态码: $DOWNLOAD_RESPONSE"
    fi
  fi
else
  print_message $RED "找不到文件ID记录，无法进行文件获取测试"
fi

print_message $BLUE "\n======= API测试完成 =======" 
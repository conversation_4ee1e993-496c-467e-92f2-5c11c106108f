# 短信登录配置指南

本文档提供配置和使用短信验证码登录功能的详细说明，包括环境配置、API接口和测试方法。

## 1. 环境变量配置

在项目的`.env`文件中添加以下配置：

```
# 短信登录配置
SMS_ENABLED=true                 # 是否启用短信登录功能
SMS_PROVIDER=aliyun              # 短信服务提供商，支持: aliyun, tencent, custom
SMS_ACCESS_KEY=your_access_key   # 服务提供商的访问密钥ID
SMS_SECRET_KEY=your_secret_key   # 服务提供商的访问密钥Secret
SMS_SIGN_NAME=您的签名          # 短信签名
SMS_TEMPLATE_CODE=SMS_*********  # 短信模板代码
SMS_AUTO_CREATE_USER=true        # 是否自动创建不存在的用户
SMS_CODE_EXPIRE_MINUTES=10       # 验证码有效期（分钟）
SMS_CODE_LENGTH=6                # 验证码长度
SMS_COOLDOWN_SECONDS=60          # 验证码发送冷却时间（秒）
```

## 2. 阿里云短信服务配置

如果使用阿里云作为短信服务提供商，请按照以下步骤配置：

1. 登录[阿里云控制台](https://account.aliyun.com/login/login.htm)
2. 进入"短信服务"控制台
3. 创建签名：
   - 签名类型选择"公司"或"APP应用"
   - 签名名称填入与`SMS_SIGN_NAME`环境变量相同的内容
   - 上传相关证明材料
4. 创建模板：
   - 模板类型选择"验证码"
   - 模板内容示例：`您的验证码${code}，有效期10分钟，请勿泄露给他人。`
   - 申请通过后，将得到的模板CODE填入`SMS_TEMPLATE_CODE`环境变量

## 3. 腾讯云短信服务配置

如果选择腾讯云作为短信服务提供商，请按照以下步骤配置：

1. 登录[腾讯云控制台](https://console.cloud.tencent.com/)
2. 进入"短信"服务
3. 配置如下环境变量：

```
SMS_PROVIDER=tencent
SMS_ACCESS_KEY=your_secret_id       # 对应腾讯云的SecretId
SMS_SECRET_KEY=your_secret_key      # 对应腾讯云的SecretKey
SMS_SIGN_NAME=您的签名             # 短信签名，需要在腾讯云申请
SMS_APP_ID=*********                # 应用ID，在腾讯云短信服务创建应用时获取
SMS_TEMPLATE_CODE=123456            # 模板ID，创建模板后获取
```

## 4. API接口说明

短信登录功能提供以下API接口：

### 4.1 发送验证码

```
POST /api/v1/auth/sms/send-code
```

**请求参数**：

```json
{
  "phone": "13800138000"  // 手机号码，必填
}
```

**响应**：

```json
{
  "code": 0,
  "message": "验证码已发送",
  "data": {
    "expire_in": 600,     // 过期时间（秒）
    "cooldown": 60        // 冷却时间（秒）
  }
}
```

### 4.2 短信验证码登录

```
POST /api/v1/auth/sms/login
```

**请求参数**：

```json
{
  "phone": "13800138000",  // 手机号码，必填
  "code": "123456"         // 验证码，必填
}
```

**响应**：

```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1...",  // JWT访问令牌
    "refresh_token": "eyJhbGciOiJIUzI1...", // JWT刷新令牌
    "token_type": "bearer",                 // 令牌类型
    "expires_in": 3600                      // 令牌有效期（秒）
  }
}
```

## 5. 测试短信登录

可以使用以下命令测试短信登录功能：

### 5.1 发送验证码

```bash
curl -X POST "http://localhost:8000/api/v1/auth/sms/send-code" \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000"}'
```

### 5.2 使用验证码登录

```bash
curl -X POST "http://localhost:8000/api/v1/auth/sms/login" \
  -H "Content-Type: application/json" \
  -d '{"phone":"13800138000", "code":"123456"}'
```

## 6. 前端实现示例

### 6.1 发送验证码

```javascript
async function sendSmsCode(phone) {
  try {
    const response = await fetch('/api/v1/auth/sms/send-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phone }),
    });
    
    const data = await response.json();
    
    if (data.code === 0) {
      // 设置倒计时
      let countdown = data.data.cooldown;
      const timer = setInterval(() => {
        countdown -= 1;
        // 更新UI显示剩余冷却时间
        if (countdown <= 0) {
          clearInterval(timer);
          // 重新启用发送按钮
        }
      }, 1000);
      
      return true;
    } else {
      throw new Error(data.message || '发送验证码失败');
    }
  } catch (error) {
    console.error('发送验证码错误:', error);
    return false;
  }
}
```

### 6.2 短信登录

```javascript
async function smsLogin(phone, code) {
  try {
    const response = await fetch('/api/v1/auth/sms/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phone, code }),
    });
    
    const data = await response.json();
    
    if (data.code === 0) {
      // 保存令牌
      localStorage.setItem('access_token', data.data.access_token);
      localStorage.setItem('refresh_token', data.data.refresh_token);
      
      // 跳转到首页或其他页面
      window.location.href = '/dashboard';
      
      return true;
    } else {
      throw new Error(data.message || '登录失败');
    }
  } catch (error) {
    console.error('登录错误:', error);
    return false;
  }
}
```

## 7. 注意事项

1. 在生产环境中，确保使用HTTPS协议保护API通信
2. 定期更新短信服务的访问密钥，提高安全性
3. 实施适当的速率限制，防止短信轰炸攻击
4. 监控短信发送量，避免超出服务提供商的限额
5. 考虑使用图形验证码作为发送短信前的预验证，进一步提高安全性

## 8. 故障排查

如果遇到短信登录问题，请检查以下几点：

1. 确认环境变量是否正确配置
2. 检查短信服务提供商的控制台，查看发送记录和错误原因
3. 查看服务器日志，寻找相关错误信息
4. 使用以下工具测试短信发送流程：

```bash
python tools/auth_tests/test_sms_code.py 13800138000
```

如果还是无法解决问题，请联系系统管理员或开发团队获取帮助。 
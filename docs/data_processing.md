# 大型数据处理指南

## 功能概述

本系统集成了基于Polars的高性能数据处理引擎，用于处理大规模数据集。Polars是一个高性能的数据处理库，专为大型数据集优化，具有以下特点：

- **极高的性能**：比Pandas快5-10倍，甚至更多
- **内存高效**：使用Apache Arrow内存模型，避免数据复制
- **并行计算**：自动利用多核CPU加速处理
- **懒惰执行**：先构建执行计划，再一次性计算，减少中间步骤
- **支持多种文件格式**：CSV, JSON, Parquet, Excel等

## API端点

以下是数据处理相关的API端点：

| 端点 | 方法 | 描述 |
|-----|-----|------|
| `/api/v1/processing/data/upload` | POST | 上传数据文件进行处理 |
| `/api/v1/processing/data/{file_id}/status` | GET | 获取数据处理状态 |
| `/api/v1/processing/data/{file_id}/process` | POST | 处理数据文件 |
| `/api/v1/processing/data/tasks/{task_id}` | GET | 获取处理结果 |
| `/api/v1/processing/data/tasks/{task_id}/export` | GET | 导出处理结果 |

## 使用流程

### 1. 上传数据文件

首先上传需要处理的数据文件：

```http
POST /api/v1/processing/data/upload
```

请求参数：
- `file`: 文件对象（必须）
- `use_lazy`: 布尔值，是否使用懒加载模式（推荐用于大文件）

响应：
```json
{
  "file_id": "5f8d7a3b9c2e1d4f6a8b7c5d",
  "filename": "example.csv",
  "status": "processing",
  "message": "文件已上传，正在处理中"
}
```

### 2. 检查处理状态

使用返回的file_id检查处理状态：

```http
GET /api/v1/processing/data/{file_id}/status
```

响应示例：
```json
{
  "status": "completed",
  "filename": "example.csv",
  "statistics": {
    "row_count": 10000,
    "column_count": 15,
    "columns": ["id", "name", "age", ...],
    "dtypes": {"id": "Int64", "name": "Utf8", ...},
    "memory_usage": 1250000,
    "numeric_stats": {
      "age": {
        "min": 18,
        "max": 80,
        "mean": 42.5,
        "median": 41,
        "std": 12.3,
        "null_count": 0
      },
      ...
    }
  }
}
```

### 3. 处理数据

提交数据处理请求：

```http
POST /api/v1/processing/data/{file_id}/process
```

请求体示例：
```json
{
  "operations": [
    {
      "type": "filter",
      "params": {
        "column": "age",
        "operator": "gt",
        "value": 30
      }
    },
    {
      "type": "select",
      "params": {
        "columns": ["id", "name", "age", "salary"]
      }
    },
    {
      "type": "sort",
      "params": {
        "by": ["salary"],
        "descending": true
      }
    }
  ],
  "output_format": "json"
}
```

响应：
```json
{
  "task_id": "7e6d5f4a3b2c1e0d",
  "status": "processing",
  "message": "数据处理已开始"
}
```

### 4. 获取处理结果

使用task_id获取处理结果：

```http
GET /api/v1/processing/data/tasks/{task_id}
```

响应示例（data格式）：
```json
{
  "status": "completed",
  "result_type": "data",
  "result": {
    "data": [
      {"id": 101, "name": "张三", "age": 45, "salary": 15000},
      {"id": 203, "name": "李四", "age": 38, "salary": 12000},
      ...
    ],
    "total_rows": 500,
    "limited": true
  }
}
```

### 5. 导出结果

导出处理后的数据：

```http
GET /api/v1/processing/data/tasks/{task_id}/export?format=csv
```

此API会返回文件下载响应。

## 支持的数据处理操作

系统支持以下数据处理操作：

### 1. 过滤操作 (filter)

根据条件过滤数据行。

参数：
- `column`: 列名
- `operator`: 操作符，如 "eq"(等于), "gt"(大于), "lt"(小于)等
- `value`: 比较值

示例：
```json
{
  "type": "filter",
  "params": {
    "column": "age",
    "operator": "gte",
    "value": 18
  }
}
```

支持的操作符：
- `eq`: 等于
- `neq`: 不等于
- `gt`: 大于
- `gte`: 大于等于
- `lt`: 小于
- `lte`: 小于等于
- `contains`: 包含（字符串）
- `starts_with`: 以...开始（字符串）
- `ends_with`: 以...结束（字符串）
- `is_null`: 是否为空
- `is_not_null`: 是否非空
- `in`: 包含在列表中

### 2. 选择列 (select)

选择需要的列。

参数：
- `columns`: 列名数组

示例：
```json
{
  "type": "select",
  "params": {
    "columns": ["id", "name", "email"]
  }
}
```

### 3. 重命名列 (rename)

重命名列。

参数：
- `mapping`: 映射字典（旧名:新名）

示例：
```json
{
  "type": "rename",
  "params": {
    "mapping": {
      "id": "user_id",
      "name": "user_name"
    }
  }
}
```

### 4. 排序 (sort)

按指定列排序。

参数：
- `by`: 排序列名数组
- `descending`: 是否降序（布尔值）

示例：
```json
{
  "type": "sort",
  "params": {
    "by": ["age", "salary"],
    "descending": true
  }
}
```

### 5. 分组聚合 (group_by)

分组并聚合。

参数：
- `by`: 分组列名数组
- `aggs`: 聚合配置（列名:聚合函数）

示例：
```json
{
  "type": "group_by",
  "params": {
    "by": ["department", "role"],
    "aggs": {
      "salary": "mean",
      "age": "mean",
      "id": "count"
    }
  }
}
```

支持的聚合函数：
- `sum`: 求和
- `mean`: 平均值
- `count`: 计数
- `min`: 最小值
- `max`: 最大值

### 6. 添加派生列 (with_column)

添加基于现有列计算的新列。

参数：
- `name`: 新列名
- `expr`: 表达式

示例：
```json
{
  "type": "with_column",
  "params": {
    "name": "full_name",
    "expr": "col('first_name') + ' ' + col('last_name')"
  }
}
```

### 7. 限制行数 (limit)

限制返回的行数。

参数：
- `n`: 最大行数

示例：
```json
{
  "type": "limit",
  "params": {
    "n": 100
  }
}
```

### 8. 展开数组 (explode)

展开数组列。

参数：
- `columns`: 要展开的列名数组

示例：
```json
{
  "type": "explode",
  "params": {
    "columns": ["tags"]
  }
}
```

### 9. 强制计算 (collect)

强制立即计算（仅适用于LazyFrame）。

示例：
```json
{
  "type": "collect",
  "params": {}
}
```

## 性能优化建议

1. **使用懒加载模式**：对于大型文件，始终启用`use_lazy=true`

2. **减少数据加载量**：先使用select和filter操作减少数据量，再进行复杂计算

3. **批量操作**：将多个操作合并到一个请求中，减少中间数据传输

4. **适当列选择**：只选择必要的列，减少内存使用

5. **使用Parquet格式**：导出和保存数据时优先使用Parquet格式，它比CSV或JSON更紧凑且更快

6. **避免collect操作**：除非必要，避免早期使用collect操作，保持懒加载模式直到最终结果

7. **分组聚合优化**：先过滤数据再分组，而不是先分组再过滤，可以显著提高性能

## 使用场景示例

### 场景1：销售数据分析

假设有一个销售数据文件，需要分析不同区域的销售情况：

```json
{
  "operations": [
    {"type": "filter", "params": {"column": "date", "operator": "gte", "value": "2023-01-01"}},
    {"type": "group_by", "params": {
      "by": ["region", "product_category"],
      "aggs": {"sales_amount": "sum", "quantity": "sum", "order_id": "count"}
    }},
    {"type": "sort", "params": {"by": ["region", "sales_amount"], "descending": true}}
  ],
  "output_format": "json"
}
```

### 场景2：用户行为筛选

筛选特定用户行为数据：

```json
{
  "operations": [
    {"type": "filter", "params": {"column": "user_type", "operator": "eq", "value": "premium"}},
    {"type": "filter", "params": {"column": "activity", "operator": "in", "value": ["login", "purchase", "view"]}},
    {"type": "with_column", "params": {"name": "session_length_min", "expr": "col('session_length_sec') / 60"}},
    {"type": "select", "params": {"columns": ["user_id", "activity", "timestamp", "session_length_min"]}},
    {"type": "sort", "params": {"by": ["user_id", "timestamp"], "descending": false}}
  ],
  "output_format": "json"
}
```

### 场景3：数据集转换和导出

转换数据格式并导出：

```json
{
  "operations": [
    {"type": "select", "params": {"columns": ["id", "name", "email", "registration_date"]}},
    {"type": "rename", "params": {"mapping": {"id": "user_id", "name": "full_name"}}},
    {"type": "filter", "params": {"column": "email", "operator": "is_not_null", "value": null}},
    {"type": "sort", "params": {"by": ["registration_date"], "descending": true}}
  ],
  "output_format": "json"
}
```

然后使用`export`端点导出为所需格式。

## 常见问题排查

1. **文件上传失败**：检查文件格式是否受支持，文件大小是否超过限制

2. **处理速度慢**：考虑先过滤或选择必要列减少数据量，使用懒加载模式

3. **内存错误**：对于超大文件，应该使用懒加载模式并分步处理，先过滤再计算

4. **任务超时**：复杂处理可能需要更长时间，可以分解为多个简单任务

5. **数据类型问题**：特别注意日期和数值类型，必要时先转换类型再处理 
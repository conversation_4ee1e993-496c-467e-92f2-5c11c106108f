# CAS单点登录配置指南

本文档提供配置和使用CAS（Central Authentication Service）单点登录功能的详细说明，包括环境配置、API接口和测试方法。

## 1. 环境变量配置

在项目的`.env`文件中添加以下配置：

```
# CAS登录配置
CAS_ENABLED=true                                  # 是否启用CAS登录功能
CAS_SERVER_URL=https://cas.example.com            # CAS服务器URL
CAS_SERVICE_URL=http://localhost:8000/api/v1/auth/cas/callback  # 回调服务URL
CAS_VERSION=3                                     # CAS协议版本，支持2或3
CAS_AUTO_CREATE_USER=true                         # 是否自动创建不存在的用户
CAS_DEFAULT_ROLE=user                             # 自动创建用户时分配的角色
CAS_VALIDATE_CERT=true                            # 是否验证SSL证书
```

## 2. CAS服务器集成

### 2.1 CAS服务注册

在CAS服务器端注册您的应用：

1. 登录CAS管理控制台（通常是 `https://cas.example.com/cas-management`）
2. 添加新服务（Service）
3. 配置服务URL：`http://localhost:8000/api/v1/auth/cas/callback`
4. 配置属性释放策略（Attribute Release Policy），至少包含以下属性：
   - `id`/`uid` - 用户唯一标识
   - `name`/`username` - 用户名
   - `email` - 用户邮箱地址
5. 保存并激活服务

### 2.2 CAS协议版本说明

CAS协议有几个主要版本，本系统支持CAS 2.0和CAS 3.0：

- **CAS 2.0**: 基本的票据验证协议，仅返回用户ID
- **CAS 3.0**: 扩展的票据验证协议，支持用户属性返回（推荐使用）

根据您的CAS服务器版本，设置适当的`CAS_VERSION`。

## 3. API接口说明

CAS登录功能提供以下API接口：

### 3.1 CAS登录

```
POST /api/v1/auth/cas/login
```

**请求参数**：

```json
{
  "ticket": "ST-1234-abcdefg...",  // CAS服务票据，必填
  "service": "http://localhost:8000/api/v1/auth/cas/callback"  // 服务URL，必填
}
```

**响应**：

```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1...",  // JWT访问令牌
    "refresh_token": "eyJhbGciOiJIUzI1...", // JWT刷新令牌
    "token_type": "bearer",                 // 令牌类型
    "expires_in": 3600                      // 令牌有效期（秒）
  }
}
```

### 3.2 CAS登录回调

```
GET /api/v1/auth/cas/callback?ticket=ST-1234-abcdefg...
```

此端点接收CAS服务器的回调，验证票据并重定向到前端，通常不直接调用。

## 4. CAS登录流程

CAS单点登录流程如下：

1. 用户访问前端应用
2. 用户点击"CAS登录"按钮
3. 前端重定向到CAS服务器登录页面
4. 用户在CAS页面输入凭据
5. CAS认证成功后重定向回应用的回调URL，并附带票据(ticket)
6. 后端验证票据并颁发JWT令牌
7. 前端使用JWT令牌进行后续API调用

## 5. 测试CAS登录

测试CAS登录需要有效的CAS服务器。以下是使用curl测试CAS登录的方法（假设已从CAS获得有效票据）：

```bash
# 使用CAS票据登录
curl -X POST "http://localhost:8000/api/v1/auth/cas/login" \
  -H "Content-Type: application/json" \
  -d '{
    "ticket": "ST-1234-abcdefg...", 
    "service": "http://localhost:8000/api/v1/auth/cas/callback"
  }'
```

## 6. 前端实现示例

### 6.1 CAS登录按钮

```javascript
function redirectToCasLogin() {
  // 获取当前应用的回调URL
  const serviceUrl = encodeURIComponent('http://localhost:8000/api/v1/auth/cas/callback');
  
  // 构建CAS登录URL
  const casLoginUrl = `${process.env.CAS_SERVER_URL}/login?service=${serviceUrl}`;
  
  // 重定向到CAS登录页面
  window.location.href = casLoginUrl;
}
```

### 6.2 CAS回调处理

```javascript
// 在前端路由中处理CAS回调
// 例如：/cas-callback?ticket=ST-1234-abcdefg...
async function handleCasCallback() {
  // 从URL获取票据
  const urlParams = new URLSearchParams(window.location.search);
  const ticket = urlParams.get('ticket');
  
  if (!ticket) {
    console.error('CAS回调缺少票据');
    return;
  }
  
  try {
    // 调用后端验证票据
    const serviceUrl = 'http://localhost:8000/api/v1/auth/cas/callback';
    const response = await fetch('/api/v1/auth/cas/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        ticket, 
        service: serviceUrl 
      }),
    });
    
    const data = await response.json();
    
    if (data.code === 0) {
      // 保存令牌
      localStorage.setItem('access_token', data.data.access_token);
      localStorage.setItem('refresh_token', data.data.refresh_token);
      
      // 跳转到首页或其他页面
      window.location.href = '/dashboard';
    } else {
      throw new Error(data.message || 'CAS登录失败');
    }
  } catch (error) {
    console.error('CAS登录错误:', error);
    // 显示错误信息
  }
}
```

## 7. 注意事项

1. CAS服务器通常要求HTTPS，确保在生产环境中使用有效的SSL证书
2. CAS服务器与应用服务器需要时间同步，否则可能导致票据验证失败
3. 在多节点部署环境中，确保所有节点都能访问CAS服务器
4. CAS票据只能使用一次，不能重复验证

## 8. 常见问题

### 8.1 票据验证失败

可能的原因：
- 票据已过期（CAS票据通常有效期很短，只有几秒钟）
- 票据已被使用
- 服务URL不匹配（必须与注册在CAS服务器上的完全一致）
- 时间不同步

### 8.2 属性获取问题

如果无法获取用户属性，请检查：
- CAS服务器的属性释放策略配置
- 确认使用CAS 3.0协议（CAS 2.0不支持属性）
- 检查CAS服务器日志

## 9. 故障排查

如果遇到CAS登录问题，请检查以下几点：

1. 查看服务器日志中的详细错误信息
2. 使用以下工具测试CAS票据验证：

```bash
python tools/auth_tests/test_cas_ticket.py ST-1234-abcdefg... http://localhost:8000/api/v1/auth/cas/callback
```

3. 检查CAS服务器日志，查看票据生成和验证情况
4. 确认CAS服务器配置中已正确注册应用服务URL

如有其他问题，请联系系统管理员或开发团队获取帮助。 
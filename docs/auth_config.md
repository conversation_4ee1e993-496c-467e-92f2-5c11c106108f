# 认证系统配置说明

## 支持的认证方式

本系统支持以下认证方式：

1. **JWT令牌认证** - 基于JSON Web Token的标准认证方式
2. **Cookie认证** - 基于Cookie的会话认证
3. **CAS单点登录** - 集成企业单点登录系统
4. **OAuth2第三方认证** - 支持Google、GitHub等第三方登录
5. **短信验证码认证** - 使用手机号和验证码登录

## 认证端点

### JWT认证

**端点**：`POST /api/v1/auth/jwt/login`

**请求格式**：
```
Content-Type: application/x-www-form-urlencoded

username=用户名或邮箱&password=密码
```

**响应示例**：
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

**使用说明**：
- 可使用用户名或邮箱登录（自动识别）
- 获取到令牌后，在后续请求中添加请求头：`Authorization: Bearer {access_token}`

### 灵活登录接口

**端点**：`POST /api/v1/auth/flexible-login`

**请求格式**：
```json
{
  "username": "用户名或邮箱",
  "password": "密码"
}
```

**响应格式**：与JWT认证相同

### Cookie认证

**端点**：`POST /api/v1/auth/cookie/login`

**请求格式**：与JWT认证相同

**特点**：
- 登录成功后会在响应中设置名为`fastapiusers_auth`的Cookie
- 后续请求会自动携带Cookie进行身份验证
- 适用于浏览器环境

## 配置说明

### JWT认证配置

在`.env`文件中配置：

```
JWT_SECRET=your-jwt-secret-key  # JWT签名密钥
JWT_ALGORITHM=HS256             # JWT算法
ACCESS_TOKEN_EXPIRE_MINUTES=30  # 访问令牌过期时间(分钟)
REFRESH_TOKEN_EXPIRE_MINUTES=10080  # 刷新令牌过期时间(分钟)
```

### CAS单点登录配置

```
CAS_ENABLED=true                # 是否启用CAS认证
CAS_SERVER_URL=https://cas.example.com/cas  # CAS服务器URL
CAS_SERVICE_URL=http://your-app-url/api/v1/auth/cas/callback  # 回调URL
CAS_AUTO_CREATE_USER=true       # 是否自动创建用户
```

**端点**：
- 登录入口：`GET /api/v1/auth/cas/login`
- 回调地址：`GET /api/v1/auth/cas/callback`

### OAuth2第三方登录配置

```
OAUTH2_ENABLED=true             # 是否启用OAuth2认证
GOOGLE_CLIENT_ID=your-client-id        # Google客户端ID
GOOGLE_CLIENT_SECRET=your-client-secret  # Google客户端密钥
GITHUB_CLIENT_ID=your-client-id        # GitHub客户端ID
GITHUB_CLIENT_SECRET=your-client-secret  # GitHub客户端密钥
OAUTH2_AUTO_CREATE_USER=true    # 是否自动创建用户
```

**端点**：
- Google登录：`GET /api/v1/auth/google/login`
- GitHub登录：`GET /api/v1/auth/github/login`
- 回调地址：`GET /api/v1/auth/oauth/{provider}/callback`

### 短信验证码登录配置

```
ALIYUN_SMS_ENABLED=true         # 是否启用短信验证码登录
ALIYUN_ACCESS_KEY_ID=your-key   # 阿里云AccessKeyId
ALIYUN_ACCESS_KEY_SECRET=your-secret  # 阿里云AccessKeySecret
ALIYUN_SMS_SIGN_NAME=sign-name  # 短信签名
ALIYUN_SMS_TEMPLATE_CODE=template-code  # 短信模板代码
SMS_CODE_EXPIRE_MINUTES=10      # 验证码有效期(分钟)
SMS_AUTO_CREATE_USER=true       # 是否自动创建用户
```

**端点**：
- 发送验证码：`POST /api/v1/auth/sms/send-code`
- 短信登录：`POST /api/v1/auth/sms/login`

## 其他认证相关端点

### 用户注册

**端点**：`POST /api/v1/auth/register`

**配置**：
```
ALLOW_USER_REGISTRATION=true    # 是否允许用户注册
VERIFY_EMAIL_ON_REGISTER=false  # 是否要求注册时验证邮箱
```

### 重置密码

**端点**：
- 请求重置：`POST /api/v1/auth/forgot-password`
- 使用令牌重置：`POST /api/v1/auth/reset-password`

### 邮箱验证

**端点**：
- 请求验证：`POST /api/v1/auth/request-verify-token`
- 验证：`POST /api/v1/auth/verify`

## 使用建议

1. 对于Web应用，推荐使用JWT认证或Cookie认证
2. 对于移动应用，推荐使用JWT认证
3. 对于企业内部系统，可以考虑启用CAS单点登录
4. 对于面向公众的应用，建议启用OAuth2第三方登录和短信验证码登录

## 安全建议

1. 在生产环境中使用强密钥（至少32字符）作为JWT_SECRET
2. 将ACCESS_TOKEN_EXPIRE_MINUTES设置为合理的值（15-60分钟）
3. 启用HTTPS保护所有API端点
4. 定期检查用户登录日志，发现异常及时处理 
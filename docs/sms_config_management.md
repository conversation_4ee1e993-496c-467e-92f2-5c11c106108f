# 短信配置管理指南

本文档提供管理数据库中短信配置的详细说明，适用于需要配置或修改短信验证功能的管理员。

## 配置管理方式

与以往使用环境变量进行配置不同，我们现在将短信配置存储在数据库中，这带来以下优势：

1. 更灵活的配置管理
2. 可以通过管理界面或API动态修改配置
3. 支持多个配置同时存在，可随时切换
4. 更好的安全性，避免敏感配置出现在环境文件中

## 管理工具

我们提供了两个命令行工具来管理短信配置：

### 1. 配置初始化工具

`tools/setup_sms_config.py` 用于首次将短信配置导入到数据库中。

```bash
# 基本用法
python tools/setup_sms_config.py

# 如果配置参数不完整，会提示手动输入
```

### 2. 配置管理工具

`tools/update_sms_config.py` 是一个功能全面的配置管理工具，支持以下功能：

#### 列出所有配置

```bash
python tools/update_sms_config.py list
```

#### 查看配置详情

```bash
python tools/update_sms_config.py list
```

#### 激活指定配置

```bash
python tools/update_sms_config.py activate <配置ID>
```

#### 更新配置

```bash
python tools/update_sms_config.py update <配置ID> --provider aliyun --sign-name "新签名" --template-code "SMS_123456789"
```

支持的参数：
- `--provider`: 提供商
- `--access-key`: 访问密钥ID
- `--secret-key`: 访问密钥Secret
- `--sign-name`: 短信签名
- `--template-code`: 短信模板代码
- `--auto-create-user`: 是否自动创建用户(true/false)
- `--code-expire-minutes`: 验证码有效期(分钟)
- `--code-length`: 验证码长度
- `--cooldown-seconds`: 冷却时间(秒)
- `--description`: 配置描述

#### 创建新配置

```bash
python tools/update_sms_config.py create --name "new_config" --provider aliyun --access-key "YOUR_KEY" --secret-key "YOUR_SECRET" --sign-name "签名" --template-code "SMS_123456789" --active
```

必选参数：
- `--name`: 配置名称
- `--provider`: 提供商
- `--access-key`: 访问密钥ID
- `--secret-key`: 访问密钥Secret
- `--sign-name`: 短信签名
- `--template-code`: 短信模板代码

可选参数：
- `--auto-create-user`: 是否自动创建用户(默认true)
- `--code-expire-minutes`: 验证码有效期(默认10分钟)
- `--code-length`: 验证码长度(默认6)
- `--cooldown-seconds`: 冷却时间(默认60秒)
- `--active`: 是否激活该配置(添加此参数表示激活)
- `--description`: 配置描述

#### 删除配置

```bash
python tools/update_sms_config.py delete <配置ID>
```

注意：不能删除当前处于激活状态的配置，需要先激活其他配置。

## 配置项说明

- **provider**: 短信服务提供商，如 "aliyun"
- **access_key**: 访问密钥ID
- **secret_key**: 访问密钥密码
- **sign_name**: 短信签名
- **template_code**: 短信模板代码
- **auto_create_user**: 是否自动为未注册用户创建账号
- **code_expire_minutes**: 验证码有效期(分钟)
- **code_length**: 验证码长度
- **cooldown_seconds**: 验证码发送冷却时间(秒)

## 阿里云短信配置流程

1. 登录[阿里云控制台](https://account.aliyun.com/login/login.htm)
2. 访问"短信服务"控制台
3. 获取或创建AccessKey (RAM访问控制)
4. 创建短信签名
5. 创建短信模板
6. 使用上述信息创建短信配置

例如：
```bash
python tools/update_sms_config.py create --name "aliyun_sms" --provider aliyun --access-key "YOUR_KEY" --secret-key "YOUR_SECRET" --sign-name "签名名称" --template-code "SMS_123456789" --active
```

## 故障排查

如果短信验证码发送失败，可以通过以下步骤排查：

1. 确认至少有一个配置处于激活状态
   ```bash
   python tools/update_sms_config.py list
   ```

2. 检查配置参数是否正确
   - 阿里云AccessKey是否有效
   - 短信签名是否已审核通过
   - 短信模板是否已审核通过

3. 查看服务器日志，了解详细错误信息
   ```bash
   tail -100 server.log | grep "短信"
   ```

4. 如有需要，更新配置参数
   ```bash
   python tools/update_sms_config.py update <配置ID> --access-key "新密钥" --secret-key "新密码"
   ``` 
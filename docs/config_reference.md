# 配置参考文档

本文档提供项目配置的完整参考。配置可通过环境变量或`.env`文件设置。

## 目录

1. [基础配置](#基础配置)
2. [安全与认证配置](#安全与认证配置)
3. [数据库配置](#数据库配置)
4. [Redis缓存配置](#redis缓存配置)
5. [MongoDB配置](#mongodb配置)
6. [用户配置](#用户配置)
7. [认证方式配置](#认证方式配置)
   - [OAuth2配置](#oauth2配置)
   - [CAS单点登录配置](#cas单点登录配置)
   - [短信验证码登录配置](#短信验证码登录配置)
8. [日志配置](#日志配置)

## 基础配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `PROJECT_NAME` | 字符串 | "Admin Server" | 项目名称 |
| `API_V1_STR` | 字符串 | "/api/v1" | API路径前缀 |
| `ENV` | 字符串 | "development" | 环境类型，可选值: development, testing, production |
| `DEBUG` | 布尔值 | `True` | 是否开启调试模式，生产环境应设为`False` |
| `FRONTEND_URL` | 字符串 | "http://localhost:3000" | 前端应用URL，用于CAS/OAuth回调等 |
| `DOMAIN` | 字符串 | `None` | 应用域名，用于Cookie设置等 |

## 安全与认证配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `SECRET_KEY` | 字符串 | 随机生成 | 通用密钥，用于加密和签名 |
| `JWT_SECRET` | 字符串 | 随机生成 | JWT令牌签名密钥 |
| `JWT_ALGORITHM` | 字符串 | "HS256" | JWT签名算法 |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | 整数 | 30 | 访问令牌过期时间(分钟) |
| `REFRESH_TOKEN_EXPIRE_MINUTES` | 整数 | 10080 (7天) | 刷新令牌过期时间(分钟) |
| `REFRESH_TOKEN_EXPIRE_DAYS` | 整数 | 7 | 刷新令牌过期天数(与上同) |
| `CORS_ORIGINS` | 字符串/列表 | `None` | 允许的跨域来源，逗号分隔或JSON格式 |

## 数据库配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `POSTGRES_SERVER` | 字符串 | "localhost" | PostgreSQL服务器地址 |
| `POSTGRES_USER` | 字符串 | "postgres" | PostgreSQL用户名 |
| `POSTGRES_PASSWORD` | 字符串 | "postgres" | PostgreSQL密码 |
| `POSTGRES_DB` | 字符串 | "admin_server" | PostgreSQL数据库名 |
| `POSTGRES_PORT` | 整数 | 5432 | PostgreSQL端口 |
| `DATABASE_URL` | 字符串 | `None` | 数据库连接URL，若设置则优先使用 |

## Redis缓存配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `REDIS_HOST` | 字符串 | "localhost" | Redis服务器地址 |
| `REDIS_PORT` | 整数 | 6379 | Redis端口 |
| `REDIS_DB` | 整数 | 0 | Redis数据库编号 |
| `REDIS_PASSWORD` | 字符串 | `None` | Redis密码 |
| `REDIS_USE_SSL` | 布尔值 | `False` | 是否使用SSL连接Redis |
| `REDIS_TIMEOUT` | 整数 | 5 | Redis连接超时时间(秒) |
| `REDIS_CACHE_EXPIRE_SECONDS` | 整数 | 3600 | Redis缓存默认过期时间(秒) |
| `REDIS_SESSION_PREFIX` | 字符串 | "session:" | Redis会话缓存前缀 |
| `REDIS_VERIFY_CODE_PREFIX` | 字符串 | "verify:" | Redis验证码缓存前缀 |

## MongoDB配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `MONGODB_ENABLED` | 布尔值 | `False` | 是否启用MongoDB |
| `MONGODB_URI` | 字符串 | `None` | MongoDB连接URI |
| `MONGODB_DB` | 字符串 | "admin_server" | MongoDB数据库名 |
| `MONGODB_FILE_COLLECTION` | 字符串 | "files" | MongoDB文件集合名 |

## 用户配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `DEFAULT_ROLE` | 字符串 | "user" | 新用户默认角色 |
| `ALLOW_USER_REGISTRATION` | 布尔值 | `True` | 是否允许用户注册 |
| `VERIFY_EMAIL_ON_REGISTER` | 布尔值 | `False` | 注册时是否要求验证邮箱 |

## 认证方式配置

### OAuth2配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `OAUTH2_ENABLED` | 布尔值 | `False` | 是否启用OAuth2认证 |
| `GOOGLE_CLIENT_ID` | 字符串 | `None` | Google OAuth客户端ID |
| `GOOGLE_CLIENT_SECRET` | 字符串 | `None` | Google OAuth客户端密钥 |
| `GITHUB_CLIENT_ID` | 字符串 | `None` | GitHub OAuth客户端ID |
| `GITHUB_CLIENT_SECRET` | 字符串 | `None` | GitHub OAuth客户端密钥 |
| `OAUTH2_AUTO_CREATE_USER` | 布尔值 | `True` | OAuth2登录时是否自动创建用户 |

### CAS单点登录配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `CAS_ENABLED` | 布尔值 | `False` | 是否启用CAS单点登录 |
| `CAS_SERVER_URL` | 字符串 | `None` | CAS服务器URL |
| `CAS_SERVICE_URL` | 字符串 | `None` | CAS服务回调URL |
| `CAS_AUTO_CREATE_USER` | 布尔值 | `True` | CAS登录时是否自动创建用户 |

### 短信验证码登录配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `ALIYUN_SMS_ENABLED` | 布尔值 | `False` | 是否启用短信验证码登录 |
| `ALIYUN_ACCESS_KEY_ID` | 字符串 | `None` | 阿里云AccessKeyId |
| `ALIYUN_ACCESS_KEY_SECRET` | 字符串 | `None` | 阿里云AccessKeySecret |
| `ALIYUN_SMS_SIGN_NAME` | 字符串 | `None` | 阿里云短信签名名称 |
| `ALIYUN_SMS_TEMPLATE_CODE` | 字符串 | `None` | 阿里云短信模板代码 |
| `SMS_CODE_EXPIRE_MINUTES` | 整数 | 10 | 短信验证码有效期(分钟) |
| `SMS_AUTO_CREATE_USER` | 布尔值 | `True` | 短信验证码登录时是否自动创建用户 |

## 日志配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `LOG_LEVEL` | 字符串 | "INFO" | 日志级别，可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL |

## .env文件示例

以下是一个完整的.env文件示例，包含所有配置项：

```dotenv
# 基础配置
PROJECT_NAME="Admin Server"
API_V1_STR="/api/v1"
ENV=development  # development, testing, production
DEBUG=true
FRONTEND_URL=http://localhost:3000
DOMAIN=example.com

# 安全与认证配置
SECRET_KEY=your_very_long_and_secure_secret_key
JWT_SECRET=your_very_long_and_secure_jwt_secret
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_MINUTES=10080  # 7天
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=admin_server
POSTGRES_PORT=5432
# DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/admin_server

# Redis缓存配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_USE_SSL=false
REDIS_TIMEOUT=5
REDIS_CACHE_EXPIRE_SECONDS=3600

# MongoDB配置
MONGODB_ENABLED=false
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB=admin_server

# 用户配置
DEFAULT_ROLE=user
ALLOW_USER_REGISTRATION=true
VERIFY_EMAIL_ON_REGISTER=false

# OAuth2配置
OAUTH2_ENABLED=false
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=
OAUTH2_AUTO_CREATE_USER=true

# CAS单点登录配置
CAS_ENABLED=false
CAS_SERVER_URL=https://cas.example.com/cas
CAS_SERVICE_URL=http://localhost:8000/api/v1/auth/cas/callback
CAS_AUTO_CREATE_USER=true

# 短信验证码登录配置
ALIYUN_SMS_ENABLED=false
ALIYUN_ACCESS_KEY_ID=
ALIYUN_ACCESS_KEY_SECRET=
ALIYUN_SMS_SIGN_NAME=
ALIYUN_SMS_TEMPLATE_CODE=
SMS_CODE_EXPIRE_MINUTES=10
SMS_AUTO_CREATE_USER=true

# 日志配置
LOG_LEVEL=INFO
```

## 注意事项

1. 在生产环境中，请确保为`SECRET_KEY`和`JWT_SECRET`设置足够长的随机密钥
2. 为确保安全，建议将所有认证相关配置通过环境变量设置，而非.env文件
3. 在生产环境中，应将`DEBUG`设置为`false`
4. 如需对接第三方认证服务，必须正确配置相关参数 
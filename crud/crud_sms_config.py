#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
短信服务配置CRUD操作
"""
from typing import Any, Dict, List, Optional, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from crud.base import CRUDBase
from models.sms_config import SMSConfig
from schemas.sms_config import SMSConfigCreate, SMSConfigUpdate

class CRUDSMSConfig(CRUDBase[SMSConfig, SMSConfigCreate, SMSConfigUpdate]):
    async def get_by_config_name(self, db: AsyncSession, *, config_name: str) -> Optional[SMSConfig]:
        """通过配置名称获取短信配置"""
        statement = select(self.model).where(self.model.config_name == config_name)
        result = await db.execute(statement)
        return result.scalar_one_or_none()
    
    async def get_active_config(self, db: AsyncSession) -> Optional[SMSConfig]:
        """获取当前激活的短信配置 (通常只应该有一个激活的)
           如果允许多个激活，则需要调整此逻辑，例如按名称或提供商获取。
        """
        statement = select(self.model).where(self.model.is_active == True).limit(1)
        result = await db.execute(statement)
        return result.scalar_one_or_none()

sms_config_crud = CRUDSMSConfig(SMSConfig)

async def get_sms_config(db: AsyncSession, config_id: int) -> Optional[SMSConfig]:
    """
    根据ID获取SMS配置
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        
    Returns:
        SMS配置对象，不存在则返回None
    """
    result = await db.execute(select(SMSConfig).where(SMSConfig.id == config_id))
    return result.scalar_one_or_none()


async def get_sms_config_by_name(db: AsyncSession, config_name: str) -> Optional[SMSConfig]:
    """
    根据配置名称获取SMS配置
    
    Args:
        db: 数据库会话
        config_name: 配置名称
        
    Returns:
        SMS配置对象，不存在则返回None
    """
    result = await db.execute(select(SMSConfig).where(SMSConfig.config_name == config_name))
    return result.scalar_one_or_none()


async def get_sms_config_by_provider(db: AsyncSession, provider: str) -> List[SMSConfig]:
    """
    根据提供商获取SMS配置
    
    Args:
        db: 数据库会话
        provider: 提供商名称
        
    Returns:
        SMS配置对象列表
    """
    result = await db.execute(select(SMSConfig).where(SMSConfig.provider == provider))
    return result.scalars().all()


async def get_default_sms_config(db: AsyncSession) -> Optional[SMSConfig]:
    """
    获取默认SMS配置
    
    Args:
        db: 数据库会话
        
    Returns:
        默认SMS配置对象，不存在则返回None
    """
    # 首先尝试获取名为 "default_sms" 的配置
    default_config = await get_sms_config_by_name(db, "default_sms")
    if default_config:
        return default_config
    
    # 如果默认配置不存在，则获取第一个激活的配置
    result = await db.execute(
        select(SMSConfig)
        .where(SMSConfig.is_active == True)
        .order_by(SMSConfig.id)
        .limit(1)
    )
    return result.scalar_one_or_none()


async def get_active_sms_configs(db: AsyncSession) -> List[SMSConfig]:
    """
    获取所有激活的SMS配置
    
    Args:
        db: 数据库会话
        
    Returns:
        激活的SMS配置列表
    """
    result = await db.execute(select(SMSConfig).where(SMSConfig.is_active == True))
    return result.scalars().all()


async def get_all_sms_configs(db: AsyncSession) -> List[SMSConfig]:
    """
    获取所有SMS配置
    
    Args:
        db: 数据库会话
        
    Returns:
        所有SMS配置列表
    """
    result = await db.execute(select(SMSConfig))
    return result.scalars().all()


async def create_sms_config(db: AsyncSession, config_data: SMSConfigCreate) -> SMSConfig:
    """
    创建新的SMS配置
    
    Args:
        db: 数据库会话
        config_data: 配置数据
        
    Returns:
        创建的SMS配置对象
    """
    db_config = SMSConfig(**config_data.model_dump())
    db.add(db_config)
    await db.commit()
    await db.refresh(db_config)
    return db_config


async def update_sms_config(
    db: AsyncSession, 
    config_id: int, 
    config_data: Union[SMSConfigUpdate, Dict[str, Any]]
) -> Optional[SMSConfig]:
    """
    更新SMS配置
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        config_data: 要更新的数据
        
    Returns:
        更新后的SMS配置对象，不存在则返回None
    """
    db_config = await get_sms_config(db, config_id)
    if not db_config:
        return None
    
    # 如果是Pydantic模型，转换为字典
    update_data = config_data.model_dump(exclude_unset=True) if hasattr(config_data, "model_dump") else config_data
    
    for key, value in update_data.items():
        setattr(db_config, key, value)
    
    await db.commit()
    await db.refresh(db_config)
    return db_config


async def delete_sms_config(db: AsyncSession, config_id: int) -> bool:
    """
    删除SMS配置
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        
    Returns:
        是否成功删除
    """
    db_config = await get_sms_config(db, config_id)
    if not db_config:
        return False
    
    await db.delete(db_config)
    await db.commit()
    return True


async def toggle_sms_config_status(db: AsyncSession, config_id: int, is_active: bool) -> Optional[SMSConfig]:
    """
    切换SMS配置的激活状态
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        is_active: 激活状态
        
    Returns:
        更新后的SMS配置对象，不存在则返回None
    """
    db_config = await get_sms_config(db, config_id)
    if not db_config:
        return None
    
    db_config.is_active = is_active
    await db.commit()
    await db.refresh(db_config)
    return db_config 
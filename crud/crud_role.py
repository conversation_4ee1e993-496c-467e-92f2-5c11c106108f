from typing import Any, Dict, List, Optional, Union
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy import text

from crud.base import CRUDBase
from models.role import Role, Permission
from schemas.role import RoleCreate, RoleUpdate


class CRUDRole(CRUDBase[Role, RoleCreate, RoleUpdate]):
    """
    角色CRUD操作
    """
    
    async def get_by_name(self, db: AsyncSession, name: str) -> Optional[Role]:
        """
        通过名称获取角色
        """
        return await self.get_by(db, name=name)
    
    async def get_with_permissions(self, db: AsyncSession, id: int) -> Optional[Role]:
        """
        获取角色包括权限信息
        """
        query = select(Role).options(selectinload(Role.permissions)).where(Role.id == id)
        result = await db.execute(query)
        return result.scalars().first()
    
    async def get_multi_with_permissions(
        self, 
        db: AsyncSession, 
        *, 
        skip: int = 0, 
        limit: int = 100,
    ) -> List[Role]:
        """
        获取多个角色包括权限信息
        """
        query = select(Role).options(selectinload(Role.permissions)).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def create_with_permissions(
        self, 
        db: AsyncSession, 
        *, 
        obj_in: RoleCreate, 
        permissions: List[str] = None
    ) -> Role:
        """
        创建角色并分配权限
        """
        # 创建角色对象
        db_obj = Role(
            name=obj_in.name,
            description=obj_in.description,
        )
        
        # 如果指定了权限，添加权限关联
        if permissions:
            # 查询权限
            perm_objs = []
            for perm_name in permissions:
                query = select(Permission).where(Permission.name == perm_name)
                result = await db.execute(query)
                perm = result.scalars().first()
                if perm:
                    perm_objs.append(perm)
            
            if perm_objs:
                db_obj.permissions = perm_objs
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def update_with_permissions(
        self, 
        db: AsyncSession, 
        *, 
        db_obj: Role, 
        obj_in: Union[RoleUpdate, Dict[str, Any]],
        permissions: List[str] = None
    ) -> Role:
        """
        更新角色并更新权限
        """
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        
        # 更新权限
        if permissions is not None:
            # 查询权限
            perm_objs = []
            for perm_name in permissions:
                query = select(Permission).where(Permission.name == perm_name)
                result = await db.execute(query)
                perm = result.scalars().first()
                if perm:
                    perm_objs.append(perm)
            
            # 设置新权限
            db_obj.permissions = perm_objs
        
        # 调用父类更新方法
        return await super().update(db, db_obj=db_obj, obj_in=update_data)
    
    async def get_user_roles(self, db: AsyncSession, user_id: int) -> List[Role]:
        """
        获取用户的所有角色
        """
        from models.user import User
        query = select(Role).join(Role.users).where(User.id == user_id)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def assign_role_to_user(self, db: AsyncSession, user_id: int, role_name: str) -> bool:
        """
        将角色分配给用户
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            role_name: 角色名称
            
        Returns:
            bool: 分配成功返回True，失败返回False
        """
        from models.user import User
        
        try:
            # 先查询角色ID
            query = select(Role).where(Role.name == role_name)
            result = await db.execute(query)
            role = result.scalars().first()
            
            if not role:
                print(f"角色不存在: {role_name}")
                return False
                
            # 查询用户
            user_query = select(User).where(User.id == user_id)
            user_result = await db.execute(user_query)
            user = user_result.scalars().first()
            
            if not user:
                print(f"用户不存在: {user_id}")
                return False
            
            # 检查是否已经分配
            for existing_role in user.roles:
                if existing_role.id == role.id:
                    print(f"用户 {user_id} 已经拥有角色 {role_name}")
                    return True
            
            # 使用原生SQL直接插入关系，避免ORM关系映射问题
            await db.execute(
                text("INSERT INTO user_role (user_id, role_id) VALUES (:user_id, :role_id)"),
                {"user_id": user_id, "role_id": role.id}
            )
            await db.commit()
            print(f"成功为用户 {user_id} 分配角色 {role_name}")
            return True
        except Exception as e:
            await db.rollback()
            print(f"分配角色失败: {str(e)}")
            return False


role = CRUDRole(Role) 
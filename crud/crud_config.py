from typing import Dict, List, Optional, Any
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from crud.base import CRUDBase
from models.config import Config
from schemas.config import ConfigCreate, ConfigUpdate


class CRUDConfig(CRUDBase[Config, ConfigCreate, ConfigUpdate]):
    """
    配置CRUD操作
    """
    
    async def get_by_key(self, db: AsyncSession, key: str) -> Optional[Config]:
        """
        通过键名获取配置项
        """
        return await self.get_by(db, key=key)
    
    async def get_value_by_key(self, db: AsyncSession, key: str) -> Optional[str]:
        """
        通过键名获取配置值
        """
        config = await self.get_by_key(db, key)
        return config.value if config else None
    
    async def update_by_key(self, db: AsyncSession, key: str, value: str) -> Optional[Config]:
        """
        通过键名更新配置值
        """
        config = await self.get_by_key(db, key)
        if config:
            config.value = value
            db.add(config)
            await db.commit()
            await db.refresh(config)
        return config
    
    async def get_all_config_settings(self, db: AsyncSession) -> Dict[str, Any]:
        """
        获取所有配置项，转换为字典
        """
        config_dict = {}
        
        query = select(Config)
        result = await db.execute(query)
        configs = result.scalars().all()
        
        for config in configs:
            config_dict[config.key] = config.value
            
        return config_dict
    
    async def create_config_setting(self, db: AsyncSession, *, obj_in: ConfigCreate) -> Config:
        """
        创建配置项
        """
        # 检查键是否已存在
        existing = await self.get_by_key(db, obj_in.key)
        if existing:
            # 如果存在，更新值
            existing.value = obj_in.value
            existing.description = obj_in.description
            db.add(existing)
            await db.commit()
            await db.refresh(existing)
            return existing
        
        # 不存在，创建新配置
        return await super().create(db, obj_in=obj_in)


config = CRUDConfig(Config)

# 为了方便导入，提供一个函数
async def get_all_config_settings(db: AsyncSession) -> Dict[str, Any]:
    """
    获取所有配置项，转换为字典
    """
    return await config.get_all_config_settings(db)

# 创建配置的便捷函数
async def create_config_setting(db: AsyncSession, obj_in: ConfigCreate) -> Config:
    """
    创建配置项
    """
    return await config.create_config_setting(db, obj_in=obj_in) 
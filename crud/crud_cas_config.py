#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CAS服务配置CRUD操作
"""
from typing import Any, Dict, List, Optional, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from crud.base import CRUDBase
from models.cas_config import CASConfig
from schemas.cas_config import CASConfigCreate, CASConfigUpdate

class CRUDCASConfig(CRUDBase[CASConfig, CASConfigCreate, CASConfigUpdate]):
    async def get_by_config_name(self, db: AsyncSession, *, config_name: str) -> Optional[CASConfig]:
        """通过配置名称获取CAS配置"""
        statement = select(self.model).where(self.model.config_name == config_name)
        result = await db.execute(statement)
        return result.scalar_one_or_none()

    async def get_active_config(self, db: AsyncSession) -> Optional[CASConfig]:
        """获取当前激活的CAS配置 (通常只应该有一个激活的)
           如果允许多个激活，则需要调整此逻辑，例如按名称获取。
        """
        statement = select(self.model).where(self.model.is_active == True).limit(1)
        result = await db.execute(statement)
        return result.scalar_one_or_none()

cas_config_crud = CRUDCASConfig(CASConfig)

async def get_cas_config(db: AsyncSession, config_id: int) -> Optional[CASConfig]:
    """
    根据ID获取CAS配置
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        
    Returns:
        CAS配置对象，不存在则返回None
    """
    result = await db.execute(select(CASConfig).where(CASConfig.id == config_id))
    return result.scalar_one_or_none()

async def get_cas_config_by_name(db: AsyncSession, config_name: str) -> Optional[CASConfig]:
    """
    根据配置名称获取CAS配置
    
    Args:
        db: 数据库会话
        config_name: 配置名称
        
    Returns:
        CAS配置对象，不存在则返回None
    """
    result = await db.execute(select(CASConfig).where(CASConfig.config_name == config_name))
    return result.scalar_one_or_none()

async def get_default_cas_config(db: AsyncSession) -> Optional[CASConfig]:
    """
    获取默认CAS配置
    
    Args:
        db: 数据库会话
        
    Returns:
        默认CAS配置对象，不存在则返回None
    """
    # 首先尝试获取名为 "default_cas" 的配置
    default_config = await get_cas_config_by_name(db, "default_cas")
    if default_config:
        return default_config
    
    # 如果默认配置不存在，则获取第一个激活的配置
    result = await db.execute(
        select(CASConfig)
        .where(CASConfig.is_active == True)
        .order_by(CASConfig.id)
        .limit(1)
    )
    return result.scalar_one_or_none()

async def get_active_cas_configs(db: AsyncSession) -> List[CASConfig]:
    """
    获取所有激活的CAS配置
    
    Args:
        db: 数据库会话
        
    Returns:
        激活的CAS配置列表
    """
    result = await db.execute(select(CASConfig).where(CASConfig.is_active == True))
    return result.scalars().all()

async def get_all_cas_configs(db: AsyncSession) -> List[CASConfig]:
    """
    获取所有CAS配置
    
    Args:
        db: 数据库会话
        
    Returns:
        所有CAS配置列表
    """
    result = await db.execute(select(CASConfig))
    return result.scalars().all()

async def create_cas_config(db: AsyncSession, config_data: CASConfigCreate) -> CASConfig:
    """
    创建新的CAS配置
    
    Args:
        db: 数据库会话
        config_data: 配置数据
        
    Returns:
        创建的CAS配置对象
    """
    db_config = CASConfig(**config_data.model_dump())
    db.add(db_config)
    await db.commit()
    await db.refresh(db_config)
    return db_config

async def update_cas_config(
    db: AsyncSession, 
    config_id: int, 
    config_data: Union[CASConfigUpdate, Dict[str, Any]]
) -> Optional[CASConfig]:
    """
    更新CAS配置
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        config_data: 要更新的数据
        
    Returns:
        更新后的CAS配置对象，不存在则返回None
    """
    db_config = await get_cas_config(db, config_id)
    if not db_config:
        return None
    
    # 如果是Pydantic模型，转换为字典
    update_data = config_data.model_dump(exclude_unset=True) if hasattr(config_data, "model_dump") else config_data
    
    for key, value in update_data.items():
        setattr(db_config, key, value)
    
    await db.commit()
    await db.refresh(db_config)
    return db_config

async def delete_cas_config(db: AsyncSession, config_id: int) -> bool:
    """
    删除CAS配置
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        
    Returns:
        是否成功删除
    """
    db_config = await get_cas_config(db, config_id)
    if not db_config:
        return False
    
    await db.delete(db_config)
    await db.commit()
    return True

async def toggle_cas_config_status(db: AsyncSession, config_id: int, is_active: bool) -> Optional[CASConfig]:
    """
    切换CAS配置的激活状态
    
    Args:
        db: 数据库会话
        config_id: 配置ID
        is_active: 激活状态
        
    Returns:
        更新后的CAS配置对象，不存在则返回None
    """
    db_config = await get_cas_config(db, config_id)
    if not db_config:
        return None
    
    db_config.is_active = is_active
    await db.commit()
    await db.refresh(db_config)
    return db_config 
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import select, and_, or_, func, desc, text
from sqlalchemy.ext.asyncio import AsyncSession

from models.audit_log import AuditLog

async def create_audit_log(
    db: AsyncSession,
    *,
    user_id: Optional[int] = None,
    username: Optional[str] = None,
    action: str,
    resource: str,
    resource_id: Optional[str] = None,
    method: str,
    path: str,
    query_params: Optional[Dict[str, Any]] = None,
    request_body: Optional[Dict[str, Any]] = None,
    response_code: Optional[int] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    execution_time: Optional[float] = None,
    details: Optional[Dict[str, Any]] = None,
    message: Optional[str] = None,
    success: bool = True,
) -> AuditLog:
    """
    创建一条审计日志记录
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        username: 用户名
        action: 操作类型（create, update, delete, read）
        resource: 资源类型（user, role等）
        resource_id: 资源ID
        method: HTTP方法
        path: 请求路径
        query_params: 查询参数
        request_body: 请求体内容
        response_code: 响应状态码
        ip_address: IP地址
        user_agent: 用户代理
        execution_time: 执行时间（秒）
        details: 其他详细信息
        message: 操作描述
        success: 操作是否成功
        
    Returns:
        创建的审计日志对象
    """
    # 创建审计日志对象
    db_audit_log = AuditLog(
        user_id=user_id,
        username=username,
        action=action,
        resource=resource,
        resource_id=resource_id,
        method=method,
        path=path,
        query_params=query_params,
        request_body=request_body,
        response_code=response_code,
        ip_address=ip_address,
        user_agent=user_agent,
        execution_time=execution_time,
        details=details,
        message=message,
        success=success,
    )
    
    # 添加到数据库
    db.add(db_audit_log)
    await db.commit()
    await db.refresh(db_audit_log)
    
    return db_audit_log

async def get_audit_logs(
    db: AsyncSession,
    *,
    user_id: Optional[int] = None,
    username: Optional[str] = None,
    action: Optional[str] = None,
    resource: Optional[str] = None,
    resource_id: Optional[str] = None,
    success: Optional[bool] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    ip_address: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
) -> List[AuditLog]:
    """
    查询审计日志
    
    Args:
        db: 数据库会话
        user_id: 过滤特定用户ID
        username: 过滤特定用户名
        action: 过滤特定操作类型
        resource: 过滤特定资源类型
        resource_id: 过滤特定资源ID
        success: 过滤是否成功
        start_date: 开始日期
        end_date: 结束日期
        ip_address: 过滤特定IP地址
        skip: 跳过的记录数
        limit: 返回的记录数限制
        
    Returns:
        审计日志列表
    """
    # 构建查询
    query = select(AuditLog)
    
    # 应用过滤条件
    conditions = []
    
    if user_id is not None:
        conditions.append(AuditLog.user_id == user_id)
    
    if username is not None:
        conditions.append(AuditLog.username.ilike(f"%{username}%"))
    
    if action is not None:
        conditions.append(AuditLog.action == action)
    
    if resource is not None:
        conditions.append(AuditLog.resource == resource)
    
    if resource_id is not None:
        conditions.append(AuditLog.resource_id == resource_id)
    
    if success is not None:
        conditions.append(AuditLog.success == success)
    
    if start_date is not None:
        conditions.append(AuditLog.created_at >= start_date)
    
    if end_date is not None:
        conditions.append(AuditLog.created_at <= end_date)
    
    if ip_address is not None:
        conditions.append(AuditLog.ip_address.ilike(f"%{ip_address}%"))
    
    # 应用条件
    if conditions:
        query = query.where(and_(*conditions))
    
    # 排序：最新的记录优先
    query = query.order_by(desc(AuditLog.created_at))
    
    # 分页
    query = query.offset(skip).limit(limit)
    
    # 执行查询
    result = await db.execute(query)
    
    return result.scalars().all()

async def get_audit_logs_count(
    db: AsyncSession,
    *,
    user_id: Optional[int] = None,
    username: Optional[str] = None,
    action: Optional[str] = None,
    resource: Optional[str] = None,
    resource_id: Optional[str] = None,
    success: Optional[bool] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    ip_address: Optional[str] = None,
) -> int:
    """
    获取符合条件的审计日志数量
    
    Args:
        与get_audit_logs相同
        
    Returns:
        记录数量
    """
    # 构建查询
    query = select(func.count(AuditLog.id))
    
    # 应用过滤条件
    conditions = []
    
    if user_id is not None:
        conditions.append(AuditLog.user_id == user_id)
    
    if username is not None:
        conditions.append(AuditLog.username.ilike(f"%{username}%"))
    
    if action is not None:
        conditions.append(AuditLog.action == action)
    
    if resource is not None:
        conditions.append(AuditLog.resource == resource)
    
    if resource_id is not None:
        conditions.append(AuditLog.resource_id == resource_id)
    
    if success is not None:
        conditions.append(AuditLog.success == success)
    
    if start_date is not None:
        conditions.append(AuditLog.created_at >= start_date)
    
    if end_date is not None:
        conditions.append(AuditLog.created_at <= end_date)
    
    if ip_address is not None:
        conditions.append(AuditLog.ip_address.ilike(f"%{ip_address}%"))
    
    # 应用条件
    if conditions:
        query = query.where(and_(*conditions))
    
    # 执行查询
    result = await db.execute(query)
    
    return result.scalar()

async def get_audit_log_stats(
    db: AsyncSession,
    *,
    days: int = 7,
) -> Dict[str, Any]:
    """
    获取审计日志统计信息
    
    Args:
        db: 数据库会话
        days: 统计的天数
        
    Returns:
        统计信息字典
    """
    # 计算开始日期
    start_date = datetime.now(timezone.utc) - timedelta(days=days)
    
    # 查询总记录数
    total_query = select(func.count(AuditLog.id))
    total_result = await db.execute(total_query)
    total_count = total_result.scalar()
    
    # 查询按操作类型分组的记录数
    action_query = select(
        AuditLog.action, 
        func.count(AuditLog.id).label("count")
    ).group_by(AuditLog.action)
    action_result = await db.execute(action_query)
    action_stats = {row[0]: row[1] for row in action_result}
    
    # 查询按资源类型分组的记录数
    resource_query = select(
        AuditLog.resource, 
        func.count(AuditLog.id).label("count")
    ).group_by(AuditLog.resource)
    resource_result = await db.execute(resource_query)
    resource_stats = {row[0]: row[1] for row in resource_result}
    
    # 查询按成功标志分组的记录数
    success_query = select(
        AuditLog.success, 
        func.count(AuditLog.id).label("count")
    ).group_by(AuditLog.success)
    success_result = await db.execute(success_query)
    success_stats = {str(row[0]): row[1] for row in success_result}
    
    # 查询每日操作统计
    daily_query = select(
        func.date(AuditLog.created_at).label("date"),
        func.count(AuditLog.id).label("count")
    ).where(
        AuditLog.created_at >= start_date
    ).group_by(
        func.date(AuditLog.created_at)
    ).order_by(
        text("date")
    )
    daily_result = await db.execute(daily_query)
    daily_stats = {str(row[0]): row[1] for row in daily_result}
    
    # 返回统计结果
    return {
        "total": total_count,
        "by_action": action_stats,
        "by_resource": resource_stats,
        "by_success": success_stats,
        "daily": daily_stats,
    } 
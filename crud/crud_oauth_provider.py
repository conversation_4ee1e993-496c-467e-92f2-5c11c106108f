#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OAuth提供商配置CRUD操作
"""
from typing import Any, Dict, List, Optional, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from crud.base import CRUDBase
from models.oauth_provider import OAuthProviderConfig
from schemas.oauth_provider import OAuthProviderConfigCreate, OAuthProviderConfigUpdate

class CRUDOAuthProviderConfig(CRUDBase[OAuthProviderConfig, OAuthProviderConfigCreate, OAuthProviderConfigUpdate]):
    async def get_by_provider_name(self, db: AsyncSession, *, provider_name: str) -> Optional[OAuthProviderConfig]:
        """通过提供商名称获取配置"""
        statement = select(self.model).where(self.model.provider_name == provider_name)
        result = await db.execute(statement)
        return result.scalar_one_or_none()

    async def get_active_providers(self, db: AsyncSession, *, skip: int = 0, limit: int = 100) -> List[OAuthProviderConfig]:
        """获取所有激活的提供商配置"""
        statement = select(self.model).where(self.model.is_active == True).offset(skip).limit(limit)
        result = await db.execute(statement)
        return result.scalars().all()

oauth_provider_config = CRUDOAuthProviderConfig(OAuthProviderConfig)

async def get_oauth_provider(db: AsyncSession, provider_id: int) -> Optional[OAuthProviderConfig]:
    """
    根据ID获取OAuth提供商配置
    
    Args:
        db: 数据库会话
        provider_id: 提供商配置ID
        
    Returns:
        提供商配置对象，不存在则返回None
    """
    result = await db.execute(select(OAuthProviderConfig).where(OAuthProviderConfig.id == provider_id))
    return result.scalar_one_or_none()

async def get_oauth_provider_by_name(db: AsyncSession, provider_name: str) -> Optional[OAuthProviderConfig]:
    """
    根据提供商名称获取OAuth提供商配置
    
    Args:
        db: 数据库会话
        provider_name: 提供商名称
        
    Returns:
        提供商配置对象，不存在则返回None
    """
    result = await db.execute(select(OAuthProviderConfig).where(OAuthProviderConfig.provider_name == provider_name))
    return result.scalar_one_or_none()

async def get_active_oauth_providers(db: AsyncSession) -> List[OAuthProviderConfig]:
    """
    获取所有激活的OAuth提供商配置
    
    Args:
        db: 数据库会话
        
    Returns:
        激活的提供商配置列表
    """
    result = await db.execute(select(OAuthProviderConfig).where(OAuthProviderConfig.is_active == True))
    return result.scalars().all()

async def get_all_oauth_providers(db: AsyncSession) -> List[OAuthProviderConfig]:
    """
    获取所有OAuth提供商配置
    
    Args:
        db: 数据库会话
        
    Returns:
        所有提供商配置列表
    """
    result = await db.execute(select(OAuthProviderConfig))
    return result.scalars().all()

async def create_oauth_provider(db: AsyncSession, provider_data: OAuthProviderConfigCreate) -> OAuthProviderConfig:
    """
    创建新的OAuth提供商配置
    
    Args:
        db: 数据库会话
        provider_data: 提供商配置数据
        
    Returns:
        创建的提供商配置对象
    """
    db_provider = OAuthProviderConfig(**provider_data.model_dump())
    db.add(db_provider)
    await db.commit()
    await db.refresh(db_provider)
    return db_provider

async def update_oauth_provider(
    db: AsyncSession, 
    provider_id: int, 
    provider_data: Union[OAuthProviderConfigUpdate, Dict[str, Any]]
) -> Optional[OAuthProviderConfig]:
    """
    更新OAuth提供商配置
    
    Args:
        db: 数据库会话
        provider_id: 提供商配置ID
        provider_data: 要更新的数据
        
    Returns:
        更新后的提供商配置对象，不存在则返回None
    """
    db_provider = await get_oauth_provider(db, provider_id)
    if not db_provider:
        return None
    
    # 如果是Pydantic模型，转换为字典
    update_data = provider_data.model_dump(exclude_unset=True) if hasattr(provider_data, "model_dump") else provider_data
    
    for key, value in update_data.items():
        setattr(db_provider, key, value)
    
    await db.commit()
    await db.refresh(db_provider)
    return db_provider

async def delete_oauth_provider(db: AsyncSession, provider_id: int) -> bool:
    """
    删除OAuth提供商配置
    
    Args:
        db: 数据库会话
        provider_id: 提供商配置ID
        
    Returns:
        是否成功删除
    """
    db_provider = await get_oauth_provider(db, provider_id)
    if not db_provider:
        return False
    
    await db.delete(db_provider)
    await db.commit()
    return True

async def toggle_oauth_provider_status(db: AsyncSession, provider_id: int, is_active: bool) -> Optional[OAuthProviderConfig]:
    """
    切换OAuth提供商配置的激活状态
    
    Args:
        db: 数据库会话
        provider_id: 提供商配置ID
        is_active: 激活状态
        
    Returns:
        更新后的提供商配置对象，不存在则返回None
    """
    db_provider = await get_oauth_provider(db, provider_id)
    if not db_provider:
        return None
    
    db_provider.is_active = is_active
    await db.commit()
    await db.refresh(db_provider)
    return db_provider 
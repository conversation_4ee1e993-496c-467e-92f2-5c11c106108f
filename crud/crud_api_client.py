from typing import Any, Dict, List, Optional, Union
import secrets
import string
import uuid
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func
from sqlalchemy.orm import joinedload

from crud.base import CRUDBase
from models.api_client import APIClient
from schemas.api_client import APIClientCreate, APIClientUpdate
from core.security import get_password_hash

class CRUDAPIClient(CRUDBase[APIClient, APIClientCreate, APIClientUpdate]):
    """API客户端CRUD操作类"""
    
    async def create_with_owner(
        self, db: AsyncSession, *, obj_in: APIClientCreate, owner_id: int
    ) -> APIClient:
        """创建新的API客户端，并关联创建者"""
        # 生成client_id和client_secret
        client_id = self.generate_client_id()
        client_secret = self.generate_client_secret()
        
        # 准备数据库对象
        db_obj = APIClient(
            name=obj_in.name,
            description=obj_in.description,
            client_id=client_id,
            client_secret=get_password_hash(client_secret),  # 存储哈希后的密钥
            allowed_ips=obj_in.allowed_ips,
            scopes=obj_in.scopes,
            rate_limit=obj_in.rate_limit,
            is_active=obj_in.is_active,
            expires_at=obj_in.expires_at,
            created_by_id=owner_id,
        )
        
        # 保存到数据库
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        
        # 返回对象时附加明文密钥，这是唯一一次可以获取明文密钥
        setattr(db_obj, "_plain_secret", client_secret)
        
        return db_obj
    
    async def regenerate_client_secret(
        self, db: AsyncSession, *, client_id: int, commit: bool = True
    ) -> str:
        """重新生成客户端密钥"""
        # 生成新的client_secret
        new_secret = self.generate_client_secret()
        
        # 更新数据库
        await db.execute(
            update(APIClient)
            .where(APIClient.id == client_id)
            .values(client_secret=get_password_hash(new_secret))
        )
        
        if commit:
            await db.commit()
        
        return new_secret
    
    async def get_by_client_id(
        self, db: AsyncSession, *, client_id: str
    ) -> Optional[APIClient]:
        """根据client_id获取API客户端"""
        result = await db.execute(
            select(APIClient).where(APIClient.client_id == client_id)
        )
        return result.scalars().first()
    
    async def update_last_used(
        self, db: AsyncSession, *, client_id: str, commit: bool = True
    ) -> None:
        """更新上次使用时间和请求计数"""
        now = datetime.now()
        
        await db.execute(
            update(APIClient)
            .where(APIClient.client_id == client_id)
            .values(
                last_used_at=now,
                request_count=APIClient.request_count + 1
            )
        )
        
        if commit:
            await db.commit()
    
    async def validate_client_credentials(
        self, db: AsyncSession, *, client_id: str, client_secret: str
    ) -> Optional[APIClient]:
        """验证客户端凭据"""
        from core.security import verify_password
        
        # 获取客户端
        client = await self.get_by_client_id(db, client_id=client_id)
        
        # 验证客户端存在且激活
        if not client or not client.is_active:
            return None
        
        # 验证客户端未过期
        if client.expires_at and client.expires_at < datetime.now():
            return None
        
        # 验证密钥
        if not verify_password(client_secret, client.client_secret):
            return None
        
        return client
    
    async def get_multi_by_owner(
        self, db: AsyncSession, *, owner_id: int, skip: int = 0, limit: int = 100
    ) -> List[APIClient]:
        """获取指定用户创建的API客户端列表"""
        result = await db.execute(
            select(APIClient)
            .where(APIClient.created_by_id == owner_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    def generate_client_id(self) -> str:
        """生成唯一的客户端ID"""
        # 基于UUID生成，并移除连字符
        return str(uuid.uuid4()).replace("-", "")
    
    def generate_client_secret(self) -> str:
        """生成安全的客户端密钥"""
        # 生成32字节(64字符)的随机字符串
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(64))

# 创建单例实例
api_client = CRUDAPIClient(APIClient) 
from typing import List, Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from crud.base import CRUDBase
from models.role import Permission
from schemas.role import PermissionCreate, PermissionUpdate


class CRUDPermission(CRUDBase[Permission, PermissionCreate, PermissionUpdate]):
    """
    权限CRUD操作
    """
    
    async def get_by_name(self, db: AsyncSession, name: str) -> Optional[Permission]:
        """
        通过名称获取权限
        """
        return await self.get_by(db, name=name)
    
    async def get_role_permissions(self, db: AsyncSession, role_id: int) -> List[Permission]:
        """
        获取角色的所有权限
        """
        from models.role import Role
        query = select(Permission).join(Permission.roles).where(Role.id == role_id)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_user_permissions(self, db: AsyncSession, user_id: int) -> List[Permission]:
        """
        获取用户的所有权限
        """
        from models.user import User
        from models.role import Role
        
        # 这个查询会获取用户通过角色获得的所有权限
        query = (
            select(Permission)
            .join(Permission.roles)
            .join(Role.users)
            .where(User.id == user_id)
            .distinct()
        )
        
        result = await db.execute(query)
        return result.scalars().all()


permission = CRUDPermission(Permission) 
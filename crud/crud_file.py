from typing import Any, Dict, List, Optional, Union, Tuple
from uuid import UUID
from datetime import datetime

from sqlalchemy import select, func, or_, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.expression import true, false

from crud.base import CRUDBase
from models.file import File
from schemas.file import FileCreate, FileUpdate


class CRUDFile(CRUDBase[File, FileCreate, FileUpdate]):
    """文件CRUD操作"""
    
    async def get(self, db: AsyncSession, id: UUID) -> Optional[File]:
        """通过ID获取文件
        
        Args:
            db: 数据库会话
            id: 文件ID
            
        Returns:
            文件对象或None
        """
        query = select(self.model).where(self.model.id == id)
        result = await db.execute(query)
        return result.scalars().first()
    
    async def get_by_hash(self, db: AsyncSession, file_hash: str) -> Optional[File]:
        """通过文件哈希获取文件
        
        Args:
            db: 数据库会话
            file_hash: 文件哈希值
            
        Returns:
            文件对象或None
        """
        return await self.get_by(db, file_hash=file_hash)
    
    async def get_multi_with_filter(
        self, 
        db: AsyncSession, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        owner_id: Optional[int] = None,
        is_public: Optional[bool] = None,
        category: Optional[str] = None,
        status: Optional[str] = None,
        search: Optional[str] = None,
        storage_type: Optional[str] = None
    ) -> Tuple[List[File], int]:
        """获取文件列表
        
        Args:
            db: 数据库会话
            skip: 跳过数量
            limit: 限制数量
            owner_id: 所有者ID过滤
            is_public: 是否公开过滤
            category: 分类过滤
            status: 状态过滤
            search: 搜索关键词
            storage_type: 存储类型过滤 (local, minio)
            
        Returns:
            文件列表和总数
        """
        # 构建过滤条件
        conditions = []
        
        # 添加过滤条件
        if owner_id is not None:
            conditions.append(self.model.owner_id == owner_id)
        
        if is_public is not None:
            conditions.append(self.model.is_public == is_public)
        
        if category is not None:
            conditions.append(self.model.category == category)
        
        if status is not None:
            conditions.append(self.model.status == status)
        else:
            # 默认只显示活跃状态的文件
            conditions.append(self.model.status == "active")
        
        if storage_type is not None:
            conditions.append(self.model.storage_type == storage_type)
        
        # 搜索条件
        if search:
            search_term = f"%{search}%"
            search_condition = or_(
                self.model.filename.ilike(search_term),
                self.model.original_filename.ilike(search_term),
                self.model.description.ilike(search_term)
            )
            conditions.append(search_condition)
        
        # 组合所有条件
        query_filter = and_(*conditions) if conditions else true()
        
        # 计算总数
        count_query = select(func.count()).select_from(self.model).where(query_filter)
        count_result = await db.execute(count_query)
        total = count_result.scalar()
        
        # 查询数据
        query = (
            select(self.model)
            .where(query_filter)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        
        result = await db.execute(query)
        files = result.scalars().all()
        
        return files, total
    
    async def get_by_owner(
        self, db: AsyncSession, *, owner_id: int, skip: int = 0, limit: int = 100
    ) -> Tuple[List[File], int]:
        """获取用户的文件列表
        
        Args:
            db: 数据库会话
            owner_id: 所有者ID
            skip: 跳过数量
            limit: 限制数量
            
        Returns:
            文件列表和总数
        """
        return await self.get_multi_with_filter(db, skip=skip, limit=limit, owner_id=owner_id)
    
    async def get_public(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100, category: Optional[str] = None
    ) -> Tuple[List[File], int]:
        """获取公开文件列表
        
        Args:
            db: 数据库会话
            skip: 跳过数量
            limit: 限制数量
            category: 分类过滤
            
        Returns:
            文件列表和总数
        """
        return await self.get_multi_with_filter(
            db, skip=skip, limit=limit, is_public=True, category=category
        )
    
    async def remove(self, db: AsyncSession, *, id: UUID) -> bool:
        """删除文件（软删除，只修改状态）
        
        Args:
            db: 数据库会话
            id: 文件ID
            
        Returns:
            是否成功
        """
        # 获取文件对象
        file = await self.get(db, id)
        if not file:
            return False
        
        # 更新状态为已删除
        file.status = "deleted"
        file.updated_at = datetime.now(timezone.utc)
        
        # 提交更改
        db.add(file)
        await db.commit()
        
        return True
    
    async def hard_delete(self, db: AsyncSession, *, id: UUID) -> bool:
        """硬删除文件（从数据库中移除）
        
        Args:
            db: 数据库会话
            id: 文件ID
            
        Returns:
            是否成功
        """
        # 获取文件对象
        file = await self.get(db, id)
        if not file:
            return False
        
        # 从数据库中删除
        await db.delete(file)
        await db.commit()
        
        return True
    
    async def mark_as_archived(self, db: AsyncSession, *, id: UUID) -> Optional[File]:
        """将文件标记为已归档
        
        Args:
            db: 数据库会话
            id: 文件ID
            
        Returns:
            更新后的文件对象或None
        """
        # 获取文件对象
        file = await self.get(db, id)
        if not file:
            return None
        
        # 更新状态为已归档
        file.status = "archived"
        file.updated_at = datetime.now(timezone.utc)
        
        # 提交更改
        db.add(file)
        await db.commit()
        await db.refresh(file)
        
        return file


# 创建单例实例
file = CRUDFile(model=File) 
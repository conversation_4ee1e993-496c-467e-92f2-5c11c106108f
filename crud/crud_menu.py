from typing import List, Optional, Dict, Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from crud.base import CRUDBase
from models.menu import Menu
from schemas.menu import Menu<PERSON>reate, MenuUpdate

class CRUDMenu(CRUDBase[Menu, MenuCreate, MenuUpdate]):
    """菜单CRUD操作类"""
    
    async def get_menu_tree(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> List[Menu]:
        """
        获取菜单树结构
        先获取所有根菜单（parent_id为None），然后递归加载其子菜单
        """
        query = (
            select(self.model)
            .where(self.model.parent_id.is_(None))
            .offset(skip)
            .limit(limit)
            .options(selectinload(self.model.children))
            .order_by(self.model.sort_order)
        )
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_all_with_children(self, db: AsyncSession) -> List[Menu]:
        """
        获取所有菜单，包括子菜单
        """
        query = (
            select(self.model)
            .options(selectinload(self.model.children))
            .order_by(self.model.sort_order)
        )
        result = await db.execute(query)
        return result.scalars().all()

# 创建菜单CRUD实例
menu = CRUDMenu(Menu)
from typing import Any, Dict, List, Optional, Union
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import IntegrityError

from crud.base import CRUDBase
from models.user import User
from models.role import Role
from schemas.user import UserCreate, UserUpdate
from core.security import get_password_hash, verify_password


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    """
    用户CRUD操作
    """
    
    async def get_by_username(self, db: AsyncSession, username: str) -> Optional[User]:
        """
        通过用户名获取用户
        """
        return await self.get_by(db, username=username)
    
    async def get_by_email(self, db: AsyncSession, email: str) -> Optional[User]:
        """
        通过邮箱获取用户
        """
        return await self.get_by(db, email=email)
    
    async def get_by_phone(self, db: AsyncSession, phone: str) -> Optional[User]:
        """
        通过手机号获取用户
        """
        return await self.get_by(db, phone=phone)
    
    async def get_by_id_with_roles(self, db: AsyncSession, user_id: int) -> Optional[User]:
        """
        获取用户包括角色信息
        """
        query = select(User).options(selectinload(User.roles)).where(User.id == user_id)
        result = await db.execute(query)
        return result.scalars().first()
    
    async def get_multi_with_roles(
        self, 
        db: AsyncSession, 
        *, 
        skip: int = 0, 
        limit: int = 100,
    ) -> List[User]:
        """
        获取多个用户包括角色信息
        """
        query = select(User).options(selectinload(User.roles)).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    async def create(self, db: AsyncSession, *, obj_in: UserCreate, roles: Optional[List[str]] = None) -> User:
        """
        创建新用户
        """
        # 密码哈希处理
        db_obj = User(
            username=obj_in.username,
            email=obj_in.email,
            hashed_password=get_password_hash(obj_in.password),
            full_name=obj_in.full_name,
            phone=obj_in.phone,
            is_active=obj_in.is_active,
            avatar=obj_in.avatar,
            is_superuser=getattr(obj_in, "is_superuser", False),
        )
        
        # 先保存用户以获取用户ID
        db.add(db_obj)
        try:
            await db.flush()  # 执行数据库操作但不提交事务
        except IntegrityError as e:
            await db.rollback()
            raise ValueError(f"创建用户失败: {str(e)}")
        
        # 如果指定了角色，添加角色关联
        if roles and len(roles) > 0:
            # 查询角色
            role_objs = []
            for role_name in roles:
                query = select(Role).where(Role.name == role_name)
                result = await db.execute(query)
                role = result.scalars().first()
                if role:
                    role_objs.append(role)
            
            if role_objs:
                # 设置用户的角色，这样比直接添加到关联表更安全
                db_obj.roles = role_objs
        
        try:
            await db.commit()
            await db.refresh(db_obj)
        except IntegrityError as e:
            await db.rollback()
            raise ValueError(f"保存用户角色关联失败: {str(e)}")
            
        return db_obj
    
    async def update(
        self, 
        db: AsyncSession, 
        *, 
        db_obj: User, 
        obj_in: Union[UserUpdate, Dict[str, Any]],
        update_roles: Optional[List[str]] = None,
    ) -> User:
        """
        更新用户
        """
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        
        # 处理密码更新
        if "password" in update_data and update_data["password"]:
            hashed_password = get_password_hash(update_data["password"])
            del update_data["password"]
            update_data["hashed_password"] = hashed_password
        
        # 更新角色
        if update_roles is not None:
            # 查询角色
            role_objs = []
            for role_name in update_roles:
                query = select(Role).where(Role.name == role_name)
                result = await db.execute(query)
                role = result.scalars().first()
                if role:
                    role_objs.append(role)
            
            # 设置新角色
            db_obj.roles = role_objs
        
        # 调用父类更新方法
        return await super().update(db, db_obj=db_obj, obj_in=update_data)
    
    async def authenticate(self, db: AsyncSession, *, username: str, password: str) -> Optional[User]:
        """
        验证用户凭据
        """
        user = await self.get_by_username(db, username=username)
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user
    
    async def is_active(self, user: User) -> bool:
        """
        检查用户是否激活
        """
        return user.is_active
    
    async def is_superuser(self, user: User) -> bool:
        """
        检查用户是否是超级管理员
        """
        return user.is_superuser
    
    async def update_last_login(self, db: AsyncSession, *, user_id: int) -> Optional[User]:
        """
        更新用户最后登录时间
        """
        from datetime import datetime
        user = await self.get(db, id=user_id)
        if user:
            user.last_login = datetime.now()
            db.add(user)
            await db.commit()
            await db.refresh(user)
        return user
    
    async def has_permission(self, db: AsyncSession, *, user_id: int, permission: str) -> bool:
        """
        检查用户是否拥有指定权限
        """
        # 加载用户和角色信息
        query = select(User).options(
            selectinload(User.roles).selectinload(Role.permissions)
        ).where(User.id == user_id)
        
        result = await db.execute(query)
        user = result.scalars().first()
        
        if not user:
            return False
        
        # 超级管理员拥有所有权限
        if user.is_superuser:
            return True
        
        # 检查用户角色是否拥有该权限
        for role in user.roles:
            for perm in role.permissions:
                if perm.name == permission:
                    return True
        
        return False


user = CRUDUser(User) 
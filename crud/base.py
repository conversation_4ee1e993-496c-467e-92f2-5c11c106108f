from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy import select, update, delete, func
from sqlalchemy.ext.asyncio import AsyncSession

from db.session import Base

# 定义泛型类型变量
ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """
    基础CRUD操作类
    """
    
    def __init__(self, model: Type[ModelType]):
        """
        初始化CRUD操作类
        
        Args:
            model: SQLAlchemy模型类
        """
        self.model = model
    
    async def get(self, db: AsyncSession, id: int) -> Optional[ModelType]:
        """
        通过ID获取对象
        
        Args:
            db: 数据库会话
            id: 对象ID
            
        Returns:
            对象实例或None
        """
        query = select(self.model).where(self.model.id == id)
        result = await db.execute(query)
        return result.scalars().first()
    
    async def get_by(self, db: AsyncSession, **kwargs) -> Optional[ModelType]:
        """
        通过指定条件获取对象
        
        Args:
            db: 数据库会话
            **kwargs: 过滤条件
            
        Returns:
            对象实例或None
        """
        query = select(self.model)
        for key, value in kwargs.items():
            query = query.where(getattr(self.model, key) == value)
        result = await db.execute(query)
        return result.scalars().first()
    
    async def get_multi(
        self, 
        db: AsyncSession, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        order_by: Optional[str] = None,
        desc: bool = False
    ) -> List[ModelType]:
        """
        获取多个对象
        
        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的最大记录数
            order_by: 排序字段
            desc: 是否降序
            
        Returns:
            对象列表
        """
        query = select(self.model).offset(skip).limit(limit)
        
        if order_by:
            column = getattr(self.model, order_by)
            query = query.order_by(column.desc() if desc else column)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def get_multi_by(
        self, 
        db: AsyncSession, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        order_by: Optional[str] = None,
        desc: bool = False,
        **kwargs
    ) -> List[ModelType]:
        """
        通过指定条件获取多个对象
        
        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的最大记录数
            order_by: 排序字段
            desc: 是否降序
            **kwargs: 过滤条件
            
        Returns:
            对象列表
        """
        query = select(self.model).offset(skip).limit(limit)
        
        for key, value in kwargs.items():
            query = query.where(getattr(self.model, key) == value)
        
        if order_by:
            column = getattr(self.model, order_by)
            query = query.order_by(column.desc() if desc else column)
        
        result = await db.execute(query)
        return result.scalars().all()
    
    async def count(self, db: AsyncSession, **kwargs) -> int:
        """
        计算符合条件的记录数
        
        Args:
            db: 数据库会话
            **kwargs: 过滤条件
            
        Returns:
            记录数
        """
        query = select(func.count()).select_from(self.model)
        
        for key, value in kwargs.items():
            query = query.where(getattr(self.model, key) == value)
        
        result = await db.execute(query)
        return result.scalar()
    
    async def create(self, db: AsyncSession, *, obj_in: CreateSchemaType) -> ModelType:
        """
        创建对象
        
        Args:
            db: 数据库会话
            obj_in: 用于创建对象的数据
            
        Returns:
            创建的对象实例
        """
        obj_in_data = obj_in.model_dump()
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def update(
        self, 
        db: AsyncSession, 
        *, 
        db_obj: ModelType, 
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """
        更新对象
        
        Args:
            db: 数据库会话
            db_obj: 要更新的对象实例
            obj_in: 更新数据
            
        Returns:
            更新后的对象实例
        """
        obj_data = jsonable_encoder(db_obj)
        
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def remove(self, db: AsyncSession, *, id: int) -> Optional[ModelType]:
        """
        删除对象
        
        Args:
            db: 数据库会话
            id: 对象ID
            
        Returns:
            删除的对象或None
        """
        obj = await self.get(db, id)
        if obj:
            await db.delete(obj)
            await db.commit()
        return obj
    
    async def exists(self, db: AsyncSession, **kwargs) -> bool:
        """
        检查是否存在符合条件的记录
        
        Args:
            db: 数据库会话
            **kwargs: 过滤条件
            
        Returns:
            是否存在
        """
        query = select(self.model)
        for key, value in kwargs.items():
            query = query.where(getattr(self.model, key) == value)
        
        result = await db.execute(query)
        return result.first() is not None 
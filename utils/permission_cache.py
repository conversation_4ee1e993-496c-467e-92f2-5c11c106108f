"""
权限缓存系统 - 用于优化权限查询性能
此模块提供权限缓存功能，减少数据库查询，提高性能
"""
from typing import Set, List, Dict, Optional
import json
import asyncio
import logging
from datetime import timedelta

logger = logging.getLogger(__name__)

class PermissionCache:
    """权限缓存类"""
    
    def __init__(self, redis_cache):
        """初始化权限缓存"""
        self.redis = redis_cache
        self.user_perm_key_prefix = "user_permissions:"
        self.role_perm_key_prefix = "role_permissions:"
        self.expire_time = timedelta(hours=24)  # 缓存24小时
    
    async def get_user_permissions(self, user_id: int) -> Optional[Set[str]]:
        """
        获取用户权限（从缓存）
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户权限集合或None（如果缓存中不存在）
        """
        key = f"{self.user_perm_key_prefix}{user_id}"
        
        try:
            # 从Redis获取缓存的权限
            cached_data = await self.redis.get(key)
            if cached_data:
                permissions = json.loads(cached_data)
                return set(permissions)
        except Exception as e:
            logger.warning(f"从缓存获取用户权限失败: {str(e)}")
        
        return None
    
    async def set_user_permissions(self, user_id: int, permissions: Set[str]) -> bool:
        """
        设置用户权限缓存
        
        Args:
            user_id: 用户ID
            permissions: 权限集合
            
        Returns:
            是否成功设置缓存
        """
        key = f"{self.user_perm_key_prefix}{user_id}"
        
        try:
            # 将权限列表转换为JSON并存储到Redis
            await self.redis.set(
                key, 
                json.dumps(list(permissions)),
                expire=self.expire_time
            )
            return True
        except Exception as e:
            logger.warning(f"缓存用户权限失败: {str(e)}")
            return False
    
    async def invalidate_user_permissions(self, user_id: int) -> bool:
        """
        使用户权限缓存失效
        
        Args:
            user_id: 用户ID
            
        Returns:
            是否成功使缓存失效
        """
        key = f"{self.user_perm_key_prefix}{user_id}"
        
        try:
            await self.redis.delete(key)
            return True
        except Exception as e:
            logger.warning(f"使用户权限缓存失效失败: {str(e)}")
            return False
    
    async def get_role_permissions(self, role_id: int) -> Optional[Set[str]]:
        """
        获取角色权限（从缓存）
        
        Args:
            role_id: 角色ID
            
        Returns:
            角色权限集合或None（如果缓存中不存在）
        """
        key = f"{self.role_perm_key_prefix}{role_id}"
        
        try:
            # 从Redis获取缓存的权限
            cached_data = await self.redis.get(key)
            if cached_data:
                permissions = json.loads(cached_data)
                return set(permissions)
        except Exception as e:
            logger.warning(f"从缓存获取角色权限失败: {str(e)}")
        
        return None
    
    async def set_role_permissions(self, role_id: int, permissions: Set[str]) -> bool:
        """
        设置角色权限缓存
        
        Args:
            role_id: 角色ID
            permissions: 权限集合
            
        Returns:
            是否成功设置缓存
        """
        key = f"{self.role_perm_key_prefix}{role_id}"
        
        try:
            # 将权限列表转换为JSON并存储到Redis
            await self.redis.set(
                key, 
                json.dumps(list(permissions)),
                expire=self.expire_time
            )
            return True
        except Exception as e:
            logger.warning(f"缓存角色权限失败: {str(e)}")
            return False
    
    async def invalidate_role_permissions(self, role_id: int) -> bool:
        """
        使角色权限缓存失效
        
        Args:
            role_id: 角色ID
            
        Returns:
            是否成功使缓存失效
        """
        key = f"{self.role_perm_key_prefix}{role_id}"
        
        try:
            await self.redis.delete(key)
            return True
        except Exception as e:
            logger.warning(f"使角色权限缓存失效失败: {str(e)}")
            return False
    
    async def invalidate_all_permissions(self) -> bool:
        """
        使所有权限缓存失效
        
        Returns:
            是否成功使所有缓存失效
        """
        try:
            # 删除所有用户权限缓存
            user_keys = await self.redis.keys(f"{self.user_perm_key_prefix}*")
            if user_keys:
                await self.redis.delete(*user_keys)
            
            # 删除所有角色权限缓存
            role_keys = await self.redis.keys(f"{self.role_perm_key_prefix}*")
            if role_keys:
                await self.redis.delete(*role_keys)
            
            return True
        except Exception as e:
            logger.warning(f"使所有权限缓存失效失败: {str(e)}")
            return False

# 单例获取函数
_permission_cache = None
_cache_lock = asyncio.Lock()

async def get_permission_cache():
    """获取权限缓存实例（单例模式）"""
    global _permission_cache
    
    if _permission_cache is None:
        async with _cache_lock:
            if _permission_cache is None:
                # 导入在函数内部以避免循环导入
                from services.redis_service import RedisService
                
                redis_cache = await RedisService.get_cache()
                _permission_cache = PermissionCache(redis_cache)
    
    return _permission_cache 
from typing import Any, Dict, Optional
import json
import time
from uuid import uuid4

from redis.asyncio import Redis
from fastapi import Depends

from core.config import settings
from db.redis import get_redis, RedisCache

class SessionManager:
    """会话管理器，用于在Redis中存储和管理用户会话"""
    
    def __init__self.redis = redis
        self.cache = RedisCache(redis)
        self.prefix = settings.REDIS_SESSION_PREFIX
    
    async def create_session(
        self, 
        user_id: int, 
        data: Dict[str, Any] = None, 
        expire_days: int = None
    ) -> str:
        """
        创建用户会话
        
        Args:
            user_id: 用户ID
            data: 会话数据
            expire_days: 会话过期天数，默认使用REFRESH_TOKEN_EXPIRE_DAYS
            
        Returns:
            会话ID
        """
        # 生成会话ID
        session_id = str(uuid4())
        
        # 准备会话数据
        session_data = {
            "user_id": user_id,
            "created_at": int(time.time()),
            "last_active": int(time.time()),
        }
        
        # 合并额外数据
        if data:
            session_data.update(data)
        
        # 设置过期时间
        expire_seconds = (expire_days or settings.REFRESH_TOKEN_EXPIRE_DAYS) * 86400
        
        # 存储会话
        await self.cache.set(
            key=session_id,
            value=session_data,
            expire=expire_seconds,
            prefix=self.prefix
        )
        
        # 添加用户的会话索引，用于管理用户的所有会话
        user_sessions_key = f"user:{user_id}:sessions"
        await self.redis.sadd(user_sessions_key, session_id)
        await self.redis.expire(user_sessions_key, expire_seconds)
        
        return session_id
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话数据
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话数据，如果会话不存在则返回None
        """
        # 获取会话数据
        session_data = await self.cache.get(session_id, prefix=self.prefix)
        
        if session_data:
            # 更新最后活动时间
            session_data["last_active"] = int(time.time())
            
            # 获取剩余过期时间
            ttl = await self.cache.ttl(session_id, prefix=self.prefix)
            
            # 更新会话
            await self.cache.set(
                key=session_id,
                value=session_data,
                expire=ttl,
                prefix=self.prefix
            )
            
        return session_data
    
    async def update_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """
        更新会话数据
        
        Args:
            session_id: 会话ID
            data: 要更新的数据
            
        Returns:
            更新是否成功
        """
        # 获取会话数据
        session_data = await self.cache.get(session_id, prefix=self.prefix)
        
        if not session_data:
            return False
        
        # 更新数据
        session_data.update(data)
        session_data["last_active"] = int(time.time())
        
        # 获取剩余过期时间
        ttl = await self.cache.ttl(session_id, prefix=self.prefix)
        
        # 保存更新
        await self.cache.set(
            key=session_id,
            value=session_data,
            expire=ttl,
            prefix=self.prefix
        )
        
        return True
    
    async def delete_session(self, session_id: str) -> bool:
        """
        删除会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            删除是否成功
        """
        # 获取会话数据，以获取用户ID
        session_data = await self.cache.get(session_id, prefix=self.prefix)
        
        if not session_data:
            return False
        
        # 从用户会话集合中移除
        if "user_id" in session_data:
            user_id = session_data["user_id"]
            user_sessions_key = f"user:{user_id}:sessions"
            await self.redis.srem(user_sessions_key, session_id)
        
        # 删除会话
        deleted = await self.cache.delete(session_id, prefix=self.prefix)
        return deleted > 0
    
    async def get_user_sessions(self, user_id: int) -> Dict[str, Dict[str, Any]]:
        """
        获取用户的所有会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            会话ID到会话数据的映射
        """
        # 获取用户的所有会话ID
        user_sessions_key = f"user:{user_id}:sessions"
        session_ids = await self.redis.smembers(user_sessions_key)
        
        if not session_ids:
            return {}
        
        # 获取所有会话数据
        sessions = {}
        for session_id in session_ids:
            session_data = await self.cache.get(session_id, prefix=self.prefix)
            if session_data:
                sessions[session_id] = session_data
        
        return sessions
    
    async def delete_user_sessions(self, user_id: int, exclude_session_id: str = None) -> int:
        """
        删除用户的所有会话
        
        Args:
            user_id: 用户ID
            exclude_session_id: 排除的会话ID，此会话不会被删除
            
        Returns:
            删除的会话数量
        """
        # 获取用户的所有会话ID
        user_sessions_key = f"user:{user_id}:sessions"
        session_ids = await self.redis.smembers(user_sessions_key)
        
        if not session_ids:
            return 0
        
        # 删除会话
        deleted_count = 0
        for session_id in session_ids:
            if exclude_session_id and session_id == exclude_session_id:
                continue
            
            deleted = await self.cache.delete(session_id, prefix=self.prefix)
            if deleted > 0:
                deleted_count += 1
        
        # 更新用户会话集合
        if exclude_session_id:
            await self.redis.delete(user_sessions_key)
            await self.redis.sadd(user_sessions_key, exclude_session_id)
            
            # 重新设置过期时间
            ttl = await self.cache.ttl(exclude_session_id, prefix=self.prefix)
            if ttl > 0:
                await self.redis.expire(user_sessions_key, ttl)
        else:
            await self.redis.delete(user_sessions_key)
        
        return deleted_count

async def get_session_manager(# redis: Redis = Depends(get_redis) - 已在函数内部处理) -> SessionManager:
    """
    获取会话管理器的依赖函数
    
    使用方法:
    ```
    @router.post("/login")
    async def login(
        form_data: OAuth2PasswordRequestForm = Depends(),
        session_manager: SessionManager = Depends(get_session_manager)
    ):
        # 验证用户...
        session_id = await session_manager.create_session(user_id=user.id)
        return {"session_id": session_id}
    ```
    """
    return SessionManager(redis) :

    
        """
    
        """
    
        self.redis = redis
        self.cache = RedisCache(redis)
        self.prefix = settings.REDIS_SESSION_PREFIX
    
    async def create_session(
        self, 
        user_id: int, 
        data: Dict[str, Any] = None, 
        expire_days: int = None
    ) -> str:
        """
        创建用户会话
        
        Args:
            user_id: 用户ID
            data: 会话数据
            expire_days: 会话过期天数，默认使用REFRESH_TOKEN_EXPIRE_DAYS
            
        Returns:
            会话ID
        """
        # 生成会话ID
        session_id = str(uuid4())
        
        # 准备会话数据
        session_data = {
            "user_id": user_id,
            "created_at": int(time.time()),
            "last_active": int(time.time()),
        }
        
        # 合并额外数据
        if data:
            session_data.update(data)
        
        # 设置过期时间
        expire_seconds = (expire_days or settings.REFRESH_TOKEN_EXPIRE_DAYS) * 86400
        
        # 存储会话
        await self.cache.set(
            key=session_id,
            value=session_data,
            expire=expire_seconds,
            prefix=self.prefix
        )
        
        # 添加用户的会话索引，用于管理用户的所有会话
        user_sessions_key = f"user:{user_id}:sessions"
        await self.redis.sadd(user_sessions_key, session_id)
        await self.redis.expire(user_sessions_key, expire_seconds)
        
        return session_id
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话数据
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话数据，如果会话不存在则返回None
        """
        # 获取会话数据
        session_data = await self.cache.get(session_id, prefix=self.prefix)
        
        if session_data:
            # 更新最后活动时间
            session_data["last_active"] = int(time.time())
            
            # 获取剩余过期时间
            ttl = await self.cache.ttl(session_id, prefix=self.prefix)
            
            # 更新会话
            await self.cache.set(
                key=session_id,
                value=session_data,
                expire=ttl,
                prefix=self.prefix
            )
            
        return session_data
    
    async def update_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """
        更新会话数据
        
        Args:
            session_id: 会话ID
            data: 要更新的数据
            
        Returns:
            更新是否成功
        """
        # 获取会话数据
        session_data = await self.cache.get(session_id, prefix=self.prefix)
        
        if not session_data:
            return False
        
        # 更新数据
        session_data.update(data)
        session_data["last_active"] = int(time.time())
        
        # 获取剩余过期时间
        ttl = await self.cache.ttl(session_id, prefix=self.prefix)
        
        # 保存更新
        await self.cache.set(
            key=session_id,
            value=session_data,
            expire=ttl,
            prefix=self.prefix
        )
        
        return True
    
    async def delete_session(self, session_id: str) -> bool:
        """
        删除会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            删除是否成功
        """
        # 获取会话数据，以获取用户ID
        session_data = await self.cache.get(session_id, prefix=self.prefix)
        
        if not session_data:
            return False
        
        # 从用户会话集合中移除
        if "user_id" in session_data:
            user_id = session_data["user_id"]
            user_sessions_key = f"user:{user_id}:sessions"
            await self.redis.srem(user_sessions_key, session_id)
        
        # 删除会话
        deleted = await self.cache.delete(session_id, prefix=self.prefix)
        return deleted > 0
    
    async def get_user_sessions(self, user_id: int) -> Dict[str, Dict[str, Any]]:
        """
        获取用户的所有会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            会话ID到会话数据的映射
        """
        # 获取用户的所有会话ID
        user_sessions_key = f"user:{user_id}:sessions"
        session_ids = await self.redis.smembers(user_sessions_key)
        
        if not session_ids:
            return {}
        
        # 获取所有会话数据
        sessions = {}
        for session_id in session_ids:
            session_data = await self.cache.get(session_id, prefix=self.prefix)
            if session_data:
                sessions[session_id] = session_data
        
        return sessions
    
    async def delete_user_sessions(self, user_id: int, exclude_session_id: str = None) -> int:
        """
        删除用户的所有会话
        
        Args:
            user_id: 用户ID
            exclude_session_id: 排除的会话ID，此会话不会被删除
            
        Returns:
            删除的会话数量
        """
        # 获取用户的所有会话ID
        user_sessions_key = f"user:{user_id}:sessions"
        session_ids = await self.redis.smembers(user_sessions_key)
        
        if not session_ids:
            return 0
        
        # 删除会话
        deleted_count = 0
        for session_id in session_ids:
            if exclude_session_id and session_id == exclude_session_id:
                continue
            
            deleted = await self.cache.delete(session_id, prefix=self.prefix)
            if deleted > 0:
                deleted_count += 1
        
        # 更新用户会话集合
        if exclude_session_id:
            await self.redis.delete(user_sessions_key)
            await self.redis.sadd(user_sessions_key, exclude_session_id)
            
            # 重新设置过期时间
            ttl = await self.cache.ttl(exclude_session_id, prefix=self.prefix)
            if ttl > 0:
                await self.redis.expire(user_sessions_key, ttl)
        else:
            await self.redis.delete(user_sessions_key)
        
        return deleted_count

async def get_session_manager(# redis: Redis = Depends(get_redis) - 已在函数内部处理) -> SessionManager:
    """
    获取会话管理器的依赖函数
    
    使用方法:
    ```
    @router.post("/login")
    async def login(
        form_data: OAuth2PasswordRequestForm = Depends(),
        session_manager: SessionManager = Depends(get_session_manager)
    ):
        # 验证用户...
        session_id = await session_manager.create_session(user_id=user.id)
        return {"session_id": session_id}
    ```
    """
    return SessionManager(redis) 
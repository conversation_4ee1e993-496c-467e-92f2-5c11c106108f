"""
API认证工具，用于验证API客户端访问令牌并授权访问
"""
import logging
import ipaddress
from typing import Optional, List, Dict, Any, Callable, Union
from fastapi import Request, Response, HTTPException, status, Depends
from fastapi.security import OAuth2PasswordBearer
from jose import jwt, JWTError
from sqlalchemy.ext.asyncio import AsyncSession
from starlette.middleware.base import BaseHTTPMiddleware
from datetime import datetime, timedelta
import asyncio
import time
from cachetools import TTLCache

from crud.crud_api_client import api_client
from db.session import get_db
from core.config import settings
from db.redis import get_redis_pool, RedisCache
from utils.request_cache import cached_request

# 配置日志
logger = logging.getLogger(__name__)

# OAuth2密码承载
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/api-clients/token",
    scheme_name="API客户端凭证",
    auto_error=False, # 设置为False以允许中间件处理错误
)

# 缓存客户端验证结果，避免频繁数据库查询
# 参数: maxsize=1000 (最多缓存1000个客户端), ttl=300 (缓存5分钟)
client_cache = TTLCache(maxsize=1000, ttl=300)

# 添加IP限流缓存，用于防止恶意请求
ip_rate_limit_cache = TTLCache(maxsize=10000, ttl=60)  # 1分钟内的IP访问次数
ip_blacklist_cache = TTLCache(maxsize=1000, ttl=3600)  # IP临时黑名单，1小时有效

# 令牌黑名单内存缓存，提高检查速度
token_blacklist_memory = TTLCache(maxsize=10000, ttl=86400)  # 缓存1天

class APIClientAuth(BaseHTTPMiddleware):
    """
    API客户端认证中间件
    
    验证API请求中的访问令牌，并验证客户端权限
    """
    
    def __init__(self, app, exclude_paths: List[str] = None, scopes_map: Dict[str, List[str]] = None):
        """
        初始化中间件
        
        Args:
            app: FastAPI应用
            exclude_paths: 排除的路径列表（不需要认证）
            scopes_map: 路径到作用域的映射，用于权限控制
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or []
        self.scopes_map = scopes_map or {}
        # 保存数据库连接池，避免每次请求创建连接
        self.db_pool = None
        self.redis_pool = None
        self.redis_cache = None
        # 初始化令牌黑名单
        self.token_blacklist = set()
        # 需要延迟初始化，在第一次请求时完成
        self._initialized = False
        self._init_lock = asyncio.Lock()
        
        # IP访问限制配置
        self.max_requests_per_minute = getattr(settings, "API_RATE_LIMIT_MAX_REQUESTS", 60)  # 默认每分钟60次
        self.blacklist_threshold = getattr(settings, "API_RATE_LIMIT_BLACKLIST_THRESHOLD", 100)  # 默认100次触发黑名单
    
    async def _ensure_initialized(self):
        """确保中间件已初始化"""
        if self._initialized:
            return
        
        async with self._init_lock:
            if self._initialized:  # 双重检查锁定
                return
                
            # 获取Redis连接池
            try:
                self.redis_pool = get_redis_pool()
                if self.redis_pool:
                    # 测试Redis连接是否可用
                    redis_client = None
                    try:
                        from redis.asyncio import Redis
                        redis_client = Redis(connection_pool=self.redis_pool)
                        # 执行简单命令测试连接
                        await redis_client.ping()
                        
                        # 初始化Redis缓存
                        self.redis_cache = RedisCache(redis_client)
                        
                        # 从Redis加载现有黑名单
                        await self._load_blacklist_from_redis()
                        
                        logger.info("API认证中间件Redis连接成功初始化")
                    except Exception as redis_err:
                        # Redis连接失败，记录错误但允许继续使用内存缓存
                        logger.warning(f"Redis连接测试失败: {str(redis_err)}")
                        self.redis_pool = None
                        self.redis_cache = None
                    finally:
                        # 安全关闭测试用Redis客户端
                        if redis_client is not None:
                            try:
                                await redis_client.close()
                            except Exception as close_err:
                                logger.warning(f"关闭Redis测试客户端失败: {str(close_err)}")
                else:
                    logger.warning("Redis连接池不可用，API认证中间件将仅使用内存缓存")
            except Exception as e:
                logger.warning(f"Redis连接池初始化失败: {str(e)}")
                self.redis_pool = None
                self.redis_cache = None
            
            self._initialized = True
            logger.info("API认证中间件初始化完成")
    
    async def _load_blacklist_from_redis(self):
        """从Redis加载令牌黑名单到内存"""
        if not self.redis_pool:
            logger.debug("Redis连接池不可用，跳过黑名单加载")
            return
            
        redis_client = None
        try:
            from redis.asyncio import Redis
            redis_client = Redis(connection_pool=self.redis_pool)
            
            # 获取所有黑名单令牌
            keys = await redis_client.keys("token:blacklist:*")
            if not keys:
                logger.debug("Redis中没有找到黑名单令牌")
                return
                
            # 提取令牌值并添加到内存缓存
            loaded_count = 0
            for key in keys:
                try:
                    # 确保key是字符串
                    key_str = key.decode() if isinstance(key, bytes) else str(key)
                    token = key_str.replace("token:blacklist:", "")
                    
                    # 获取剩余TTL
                    ttl = await redis_client.ttl(key)
                    if ttl > 0:
                        token_blacklist_memory[token] = time.time() + ttl
                        loaded_count += 1
                except Exception as token_err:
                    logger.warning(f"处理黑名单令牌时出错: {str(token_err)}")
                    continue
                        
            logger.info(f"从Redis加载了 {loaded_count} 个黑名单令牌")
        except Exception as e:
            logger.error(f"从Redis加载黑名单失败: {str(e)}")
        finally:
            # 安全关闭Redis客户端
            if redis_client is not None:
                try:
                    await redis_client.close()
                except Exception as close_err:
                    logger.warning(f"关闭Redis客户端失败: {str(close_err)}")
    
    def _check_ip_rate_limit(self, client_ip: str) -> bool:
        """
        检查IP访问频率限制
        
        Args:
            client_ip: 客户端IP地址
            
        Returns:
            是否允许访问
        """
        # 检查IP是否在临时黑名单中
        if client_ip in ip_blacklist_cache:
            return False
            
        # 检查和更新访问计数
        current_time = int(time.time())
        cache_key = f"{client_ip}:{current_time // 60}"  # 按分钟划分
        
        # 获取当前计数
        current_count = ip_rate_limit_cache.get(cache_key, 0)
        
        # 更新计数
        ip_rate_limit_cache[cache_key] = current_count + 1
        
        # 检查是否超过阈值
        if current_count + 1 > self.max_requests_per_minute:
            # 记录警告
            logger.warning(f"IP {client_ip} 超过访问频率限制 ({self.max_requests_per_minute}/分钟)")
            
            # 检查是否应该添加到临时黑名单
            if current_count + 1 >= self.blacklist_threshold:
                # 添加到临时黑名单，1小时后自动移除
                ip_blacklist_cache[client_ip] = current_time
                logger.warning(f"IP {client_ip} 已添加到临时黑名单")
                
                # 异步记录到Redis持久化黑名单
                asyncio.create_task(self._add_ip_to_blacklist(client_ip))
                
            return False
            
        return True
        
    async def _add_ip_to_blacklist(self, client_ip: str, hours: int = 1):
        """
        将IP添加到Redis黑名单
        
        Args:
            client_ip: 客户端IP
            hours: 黑名单有效期（小时）
        """
        if not self.redis_pool:
            logger.debug(f"Redis连接池不可用，无法将IP {client_ip} 添加到黑名单")
            return
            
        redis_client = None
        try:
            from redis.asyncio import Redis
            redis_client = Redis(connection_pool=self.redis_pool)
            
            # 设置黑名单记录，默认1小时
            blacklist_key = f"ip:blacklist:{client_ip}"
            await redis_client.setex(blacklist_key, hours * 3600, "1")
            logger.info(f"IP {client_ip} 已添加到Redis黑名单，有效期 {hours} 小时")
        except Exception as e:
            logger.error(f"添加IP到Redis黑名单失败: {str(e)}")
        finally:
            # 安全关闭Redis客户端
            if redis_client is not None:
                try:
                    await redis_client.close()
                except Exception as close_err:
                    logger.warning(f"关闭Redis客户端失败: {str(close_err)}")
    
    @cached_request(ttl=60)  # 缓存1分钟
    async def _verify_token(self, token: str) -> Dict[str, Any]:
        """
        验证令牌有效性（使用缓存优化）
        
        Args:
            token: 访问令牌
            
        Returns:
            解码后的令牌载荷或错误信息
        """
        # 首先检查内存中的令牌黑名单（最快）
        if token in token_blacklist_memory:
            logger.debug(f"令牌在内存黑名单中: {token[:10]}...")
            return {"valid": False, "error": "invalid_token", "detail": "令牌已被撤销"}
            
        # 检查Redis中的黑名单（如果使用Redis）
        if self.redis_pool:
            redis_client = None
            try:
                from redis.asyncio import Redis
                redis_client = Redis(connection_pool=self.redis_pool)
                is_blacklisted = await redis_client.exists(f"token:blacklist:{token}")
                if is_blacklisted:
                    # 添加到内存黑名单
                    token_blacklist_memory[token] = time.time() + 86400  # 缓存1天
                    logger.debug(f"令牌在Redis黑名单中: {token[:10]}...")
                    return {"valid": False, "error": "invalid_token", "detail": "令牌已被撤销"}
            except Exception as redis_err:
                logger.warning(f"检查Redis令牌黑名单失败: {str(redis_err)}")
            finally:
                # 安全关闭Redis客户端
                if redis_client is not None:
                    try:
                        await redis_client.close()
                    except Exception as close_err:
                        logger.warning(f"关闭Redis客户端失败: {str(close_err)}")
        
        # 解码令牌
        try:
            payload = jwt.decode(
                token, 
                settings.JWT_SECRET, 
                algorithms=[settings.JWT_ALGORITHM]
            )
            # 检查令牌类型
            sub = payload.get("sub", "")
            if not sub.startswith("client:"):
                # 不是客户端令牌，可能是用户令牌
                return {"valid": False, "error": "not_client_token", "payload": payload}
                
            # 提取客户端ID和作用域
            client_id = payload.get("client_id")
            scopes = payload.get("scopes", [])
            
            if not client_id:
                return {"valid": False, "error": "missing_client_id", "detail": "令牌中没有客户端ID"}
                
            return {
                "valid": True,
                "client_id": client_id,
                "scopes": scopes,
                "payload": payload
            }
        except JWTError as e:
            logger.debug(f"JWT解码错误: {str(e)}")
            return {"valid": False, "error": "invalid_token", "detail": str(e)}
        except Exception as e:
            logger.error(f"验证令牌时发生未知错误: {str(e)}")
            return {"valid": False, "error": "unknown_error", "detail": "验证令牌时发生内部错误"}
    
    @cached_request(ttl=300)  # 缓存5分钟
    async def _get_client_info(self, client_id: str, db: AsyncSession) -> Dict[str, Any]:
        """
        获取客户端信息（使用缓存优化）
        
        Args:
            client_id: 客户端ID
            db: 数据库会话
            
        Returns:
            客户端信息或None
        """
        # 验证客户端存在且激活
        client = await api_client.get_by_client_id(db, client_id=client_id)
        
        if not client or not client.is_active:
            logger.debug(f"客户端不存在或未激活: {client_id}")
            return None
        
        # 验证客户端未过期
        if client.expires_at and client.expires_at < datetime.now():
            logger.debug(f"客户端已过期: {client_id}")
            return None
        
        # 客户端信息
        client_info = {
            "id": client.id,
            "client_id": client.client_id,
            "is_active": client.is_active,
            "allowed_ips": client.allowed_ips,
            "scopes": client.scopes,
            "expires_at": client.expires_at.timestamp() if client.expires_at else None,
        }
        
        return client_info
            
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        处理请求，验证API客户端认证
        
        Args:
            request: FastAPI请求对象
            call_next: 调用下一个中间件或路由处理函数
            
        Returns:
            响应对象
        """
        # 1. 确保中间件初始化完成
        try:
            await self._ensure_initialized()
        except Exception as init_err:
            logger.error(f"API认证中间件初始化失败: {str(init_err)}")
            return await call_next(request)
        
        # 2. 获取基本请求信息
        path = request.url.path
        client_ip = request.client.host if request.client else "unknown"
        
        # 3. 检查是否需要跳过认证
        if self._should_skip_auth(path):
            return await call_next(request)
            
        # 4. 检查IP访问频率限制
        if not self._check_ip_rate_limit(client_ip):
            return self._create_error_response(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail="请求过于频繁，请稍后再试"
            )
        
        # 5. 提取访问令牌
        token = self._extract_token(request)
        if not token:
            # 无令牌视为用户访问或允许匿名访问
            return await call_next(request)
        
        # 6. 验证令牌
        try:
            # 验证令牌
            token_result = await self._verify_token(token)
            
            # 处理无效令牌
            if not token_result["valid"]:
                # 如果是用户令牌而非API客户端令牌，交给其他处理程序
                if token_result.get("error") == "not_client_token":
                    return await call_next(request)
                
                # 其他无效情况
                return self._create_error_response(
                    status_code=status.HTTP_401_UNAUTHORIZED, 
                    detail=token_result.get("detail", "无效的令牌")
                )
            
            # 7. 提取客户端ID和作用域
            client_id = token_result["client_id"]
            scopes = token_result.get("scopes", [])
            
            # 8. 获取客户端信息
            client_info = await self._get_client_and_check_auth(client_id, client_ip, path)
            if isinstance(client_info, Response):
                # 如果返回的是响应对象，表示认证失败
                return client_info
            
            # 9. 设置请求状态并记录访问日志
            self._set_request_state(request, client_info, client_id, scopes)
            self._log_api_access(client_id, client_ip, request.method, path)
            
            # 10. 异步更新使用统计（不阻塞请求）
            self._schedule_usage_update(client_id)
            
            # 11. 继续处理请求
            return await call_next(request)
            
        except Exception as e:
            logger.error(f"API客户端认证错误: {str(e)}")
            # 发生错误时，仍然允许请求继续进行
            return await call_next(request)
            
    def _should_skip_auth(self, path: str) -> bool:
        """检查是否应该跳过认证"""
        return any(path.startswith(exclude_path) for exclude_path in self.exclude_paths)
        
    def _extract_token(self, request: Request) -> Optional[str]:
        """从请求中提取访问令牌"""
        # 尝试从Authorization头获取
        auth_header = request.headers.get("Authorization")
        if auth_header:
            parts = auth_header.split()
            if len(parts) == 2 and parts[0].lower() == "bearer":
                return parts[1]
        
        # 尝试从查询参数获取
        return request.query_params.get("access_token")
        
    def _create_error_response(self, status_code: int, detail: str) -> Response:
        """创建标准错误响应"""
        return Response(
            content=f'{{"detail": "{detail}"}}',
            status_code=status_code,
            media_type="application/json"
        )
        
    async def _get_client_and_check_auth(self, client_id: str, client_ip: str, path: str) -> Union[Dict[str, Any], Response]:
        """获取客户端信息并检查授权"""
        # 1. 获取数据库会话
        db = None
        try:
            async for session in get_db():
                db = session
                break
                
            if db is None:
                return self._create_error_response(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="服务器内部错误"
                )
        except Exception as db_err:
            logger.error(f"获取数据库会话失败: {str(db_err)}")
            return self._create_error_response(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="服务器内部错误"
            )
        
        # 2. 获取客户端信息
        try:
            client_info = await self._get_client_info(client_id, db)
        except Exception as client_err:
            logger.error(f"获取客户端信息失败: {str(client_err)}")
            return self._create_error_response(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="服务器内部错误"
            )
        
        # 3. 检查客户端是否存在且活跃
        if not client_info:
            return self._create_error_response(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="客户端未激活或不存在"
            )
        
        # 4. 检查客户端是否过期
        if client_info.get("expires_at") and client_info["expires_at"] < time.time():
            # 从缓存中删除过期客户端
            client_cache.pop(f"client:{client_id}", None)
            logger.debug(f"缓存的客户端已过期: {client_id}")
            return self._create_error_response(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="客户端已过期"
            )
        
        # 5. 验证IP地址限制
        allowed_ips = client_info.get("allowed_ips", [])
        if allowed_ips and not self._is_ip_allowed(client_ip, allowed_ips):
            logger.warning(f"IP地址不允许: {client_ip} 访问客户端 {client_id}")
            return self._create_error_response(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="IP地址未授权访问"
            )
        
        # 6. 验证路径权限
        required_scopes = self._get_required_scopes(path)
        client_scopes = client_info.get("scopes", [])
        if required_scopes and not self._has_required_scopes(client_scopes, required_scopes):
            logger.warning(f"客户端 {client_id} 没有所需的权限作用域")
            return self._create_error_response(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有足够的权限访问此资源"
            )
            
        return client_info
    
    def _set_request_state(self, request: Request, client_info: Dict[str, Any], client_id: str, scopes: List[str]) -> None:
        """设置请求状态"""
        request.state.api_client = client_info
        request.state.api_client_id = client_id
        request.state.api_scopes = scopes
    
    def _log_api_access(self, client_id: str, client_ip: str, method: str, path: str) -> None:
        """记录API访问日志"""
        request_path = f"{method} {path}"
        logger.info(f"API访问: 客户端={client_id}, IP={client_ip}, 路径={request_path}")
    
    def _schedule_usage_update(self, client_id: str) -> None:
        """安排异步更新使用统计"""
        try:
            asyncio.create_task(self._update_client_usage(client_id))
        except Exception as usage_err:
            logger.warning(f"创建更新客户端使用统计任务失败: {str(usage_err)}")
    
    async def _update_client_usage(self, client_id: str):
        """
        异步更新客户端使用统计
        
        Args:
            client_id: 客户端ID
        """
        try:
            async for db in get_db():
                await api_client.update_last_used(db, client_id=client_id)
                break
        except Exception as e:
            logger.error(f"更新客户端使用统计失败: {str(e)}")
    
    def _is_ip_allowed(self, client_ip: str, allowed_ips: List[str]) -> bool:
        """
        检查客户端IP是否在允许的IP范围内
        
        Args:
            client_ip: 客户端IP
            allowed_ips: 允许的IP列表，可以是具体IP或CIDR格式
            
        Returns:
            是否允许
        """
        # 检查是否为特殊情况：空列表或包含"*"表示不限制
        if not allowed_ips or "*" in allowed_ips:
            return True
            
        # 将客户端IP转换为IP地址对象
        try:
            ip_obj = ipaddress.ip_address(client_ip)
        except ValueError:
            logger.warning(f"无效的客户端IP地址: {client_ip}")
            return False
            
        # 检查是否在允许的IP列表中
        for allowed_ip in allowed_ips:
            try:
                # 尝试解析为网络（CIDR格式，如***********/24）
                if "/" in allowed_ip:
                    network = ipaddress.ip_network(allowed_ip, strict=False)
                    if ip_obj in network:
                        return True
                # 检查精确IP匹配
                elif ipaddress.ip_address(allowed_ip) == ip_obj:
                    return True
            except ValueError:
                logger.warning(f"无效的允许IP地址格式: {allowed_ip}")
                
        return False
    
    def _get_required_scopes(self, path: str) -> List[str]:
        """
        获取路径所需的权限作用域
        
        Args:
            path: 请求路径
            
        Returns:
            所需的作用域列表
        """
        # 首先检查精确匹配
        if path in self.scopes_map:
            return self.scopes_map[path]
            
        # 检查前缀匹配
        for prefix, scopes in self.scopes_map.items():
            if path.startswith(prefix):
                return scopes
                
        return []
    
    def _has_required_scopes(self, client_scopes: List[str], required_scopes: List[str]) -> bool:
        """
        检查客户端是否拥有所需的所有作用域
        
        Args:
            client_scopes: 客户端拥有的作用域
            required_scopes: 所需的作用域
            
        Returns:
            是否拥有所需的所有作用域
        """
        # 特殊情况：客户端拥有"*"表示拥有所有权限
        if "*" in client_scopes:
            return True
            
        # 检查是否拥有所有所需的作用域
        return all(scope in client_scopes for scope in required_scopes)


# 从请求中获取API客户端信息的依赖函数
async def get_api_client(request: Request) -> Optional[Dict[str, Any]]:
    """
    从请求中获取API客户端信息
    
    Args:
        request: 请求对象
        
    Returns:
        API客户端信息或None
    """
    return getattr(request.state, "api_client", None)


# 获取API客户端信息的依赖函数
def get_api_client_id(request: Request) -> Optional[str]:
    """
    从请求中获取API客户端ID
    
    Args:
        request: 请求对象
        
    Returns:
        API客户端ID或None
    """
    return getattr(request.state, "api_client_id", None)


# 要求API客户端具有特定权限的依赖函数
def require_api_scope(scope: str):
    """
    要求API客户端具有特定权限的依赖函数
    
    Args:
        scope: 所需的权限作用域
        
    Returns:
        依赖函数
    """
    async def check_scope(request: Request) -> bool:
        client_scopes = getattr(request.state, "api_scopes", [])
        
        # 如果客户端拥有"*"表示拥有所有权限
        if "*" in client_scopes:
            return True
            
        if scope not in client_scopes:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"没有所需的权限作用域: {scope}",
            )
        return True
        
    return check_scope


# 注册令牌到黑名单
async def blacklist_token(token: str, ttl: int = 86400):
    """
    将令牌添加到黑名单
    
    Args:
        token: 访问令牌
        ttl: 黑名单有效期（秒），默认24小时
    """
    if not token or not isinstance(token, str):
        logger.warning(f"无效的令牌类型: {type(token)}")
        return
        
    try:
        # 添加到内存缓存
        token_blacklist_memory[token] = time.time() + ttl
        
        # 添加到Redis
        redis_pool = get_redis_pool()
        if redis_pool:
            redis_client = None
            try:
                from redis.asyncio import Redis
                redis_client = Redis(connection_pool=redis_pool)
                # 使用setex命令添加令牌到黑名单
                await redis_client.setex(f"token:blacklist:{token}", ttl, "1")
                logger.debug(f"令牌已添加到Redis黑名单: {token[:8]}...")
            except Exception as redis_err:
                logger.error(f"Redis操作失败: {str(redis_err)}")
            finally:
                # 安全关闭Redis客户端
                if redis_client is not None:
                    try:
                        await redis_client.close()
                    except Exception as close_err:
                        logger.warning(f"关闭Redis客户端失败: {str(close_err)}")
    except Exception as e:
        logger.error(f"将令牌添加到黑名单失败: {str(e)}")


# 检查令牌是否在黑名单中
async def is_token_blacklisted(token: str) -> bool:
    """
    检查令牌是否在黑名单中
    
    Args:
        token: 令牌
        
    Returns:
        是否在黑名单中
    """
    # 首先检查内存缓存
    if token in token_blacklist_memory:
        return True
        
    # 然后检查Redis
    try:
        redis_pool = get_redis_pool()
        if redis_pool:
            redis_client = None
            try:
                from redis.asyncio import Redis
                redis_client = Redis(connection_pool=redis_pool)
                is_blacklisted = await redis_client.exists(f"token:blacklist:{token}")
                if is_blacklisted:
                    # 添加到内存缓存以提高后续访问速度
                    token_blacklist_memory[token] = time.time() + 86400
                    return True
            except Exception as redis_err:
                logger.error(f"检查Redis令牌黑名单失败: {str(redis_err)}")
            finally:
                # 安全关闭Redis客户端
                if redis_client is not None:
                    try:
                        await redis_client.close()
                    except Exception as close_err:
                        logger.warning(f"关闭Redis客户端失败: {str(close_err)}")
    except Exception as e:
        logger.error(f"检查令牌黑名单失败: {str(e)}")
        
    return False


# 设置API认证
def setup_api_auth(app, exclude_paths: List[str] = None, scopes_map: Dict[str, List[str]] = None):
    """
    为FastAPI应用设置API客户端认证
    
    Args:
        app: FastAPI应用
        exclude_paths: 排除的路径列表（不需要认证）
        scopes_map: 路径到作用域的映射，用于权限控制
    """
    # 默认排除认证和文档相关路径
    default_exclude_paths = [
        "/docs",
        "/redoc",
        "/openapi.json",
        "/health",
        "/system/info",
        f"{settings.API_V1_STR}/auth",
        f"{settings.API_V1_STR}/api-clients/token",
    ]
    
    exclude_paths = (exclude_paths or []) + default_exclude_paths
    
    # 添加中间件
    app.add_middleware(
        APIClientAuth,
        exclude_paths=exclude_paths,
        scopes_map=scopes_map or {},
    ) 
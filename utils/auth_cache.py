"""
认证缓存工具模块
提供用户信息和令牌验证的缓存功能
"""
from typing import Optional, Dict, Any
import json
from datetime import timedelta
from redis.asyncio import Redis
import logging

from db.redis import RedisCache
from core.config import settings


logger = logging.getLogger(__name__)

# 缓存前缀
USER_CACHE_PREFIX = "user:"
TOKEN_CACHE_PREFIX = "token:"


class AuthCache:
    """认证缓存工具类，用于缓存用户信息和令牌验证结果"""
    
    def __init__(self, redis_cache: Optional[RedisCache]):
        """
        初始化认证缓存工具
        
        Args:
            redis_cache: Redis缓存工具，可以为None表示不使用缓存
        """
        self.cache = redis_cache
        self.enabled = redis_cache is not None
    
    async def _safe_redis_operation(self, operation, *args, **kwargs):
        """
        安全执行Redis操作，处理异步上下文问题
        
        Args:
            operation: 要执行的Redis操作函数名称
            args: 位置参数
            kwargs: 关键字参数
            
        Returns:
            操作结果或False（如果出错）
        """
        if not self.enabled or not self.cache:
            return False
            
        try:
            redis_method = getattr(self.cache, operation)
            if redis_method and callable(redis_method):
                return await redis_method(*args, **kwargs)
            else:
                logger.error(f"Redis方法不存在: {operation}")
                return False
        except Exception as e:
            logger.error(f"Redis操作 {operation} 失败: {str(e)}")
            return False
    
    async def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        获取缓存的用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户信息，如果缓存未命中则返回None
        """
        if not self.enabled:
            return None
            
        try:
            return await self._safe_redis_operation('get', f"{user_id}", prefix=USER_CACHE_PREFIX)
        except Exception as e:
            logger.error(f"获取用户缓存失败: {str(e)}")
            return None
    
    async def set_user(self, user_id: int, user_data: Dict[str, Any], expire: int = None) -> bool:
        """
        缓存用户信息
        
        Args:
            user_id: 用户ID
            user_data: 用户信息
            expire: 过期时间(秒)，默认使用配置的过期时间
            
        Returns:
            是否成功缓存
        """
        if not self.enabled:
            return False
            
        try:
            # 用户缓存默认30分钟
            expire = expire or 30 * 60
            return await self._safe_redis_operation('set', f"{user_id}", user_data, expire, prefix=USER_CACHE_PREFIX)
        except Exception as e:
            logger.error(f"缓存用户信息失败: {str(e)}")
            return False
    
    async def delete_user(self, user_id: int) -> bool:
        """
        删除用户缓存
        
        Args:
            user_id: 用户ID
            
        Returns:
            是否成功删除
        """
        if not self.enabled:
            return True
            
        try:
            return await self._safe_redis_operation('delete', f"{user_id}", prefix=USER_CACHE_PREFIX) > 0
        except Exception as e:
            logger.error(f"删除用户缓存失败: {str(e)}")
            return False
    
    async def get_token_data(self, token: str) -> Optional[Dict[str, Any]]:
        """
        获取令牌验证结果
        
        Args:
            token: JWT令牌
            
        Returns:
            令牌数据，如果缓存未命中则返回None
        """
        if not self.enabled:
            return None
            
        try:
            # 使用令牌的前8位作为缓存键后缀，避免完整令牌泄露
            token_key = token[:8] + "_" + str(hash(token) % 1000000)
            return await self._safe_redis_operation('get', token_key, prefix=TOKEN_CACHE_PREFIX)
        except Exception as e:
            logger.error(f"获取令牌缓存失败: {str(e)}")
            return None
    
    async def set_token_data(self, token: str, token_data: Dict[str, Any], expire: int = None) -> bool:
        """
        缓存令牌验证结果
        
        Args:
            token: JWT令牌
            token_data: 令牌数据
            expire: 过期时间(秒)，默认使用令牌自身的过期时间
            
        Returns:
            是否成功缓存
        """
        if not self.enabled:
            return False
            
        try:
            # 令牌缓存使用令牌自身的过期时间 - 1分钟（避免边缘过期问题）
            if expire is None:
                expire = settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60 - 60
                if expire < 60:  # 确保至少有60秒的缓存时间
                    expire = 60
            
            # 使用令牌的前8位作为缓存键后缀，避免完整令牌泄露
            token_key = token[:8] + "_" + str(hash(token) % 1000000)
            return await self._safe_redis_operation('set', token_key, token_data, expire, prefix=TOKEN_CACHE_PREFIX)
        except Exception as e:
            logger.error(f"缓存令牌数据失败: {str(e)}")
            return False
    
    async def invalidate_token(self, token: str) -> bool:
        """
        使令牌缓存失效
        
        Args:
            token: JWT令牌
            
        Returns:
            是否成功删除
        """
        if not self.enabled:
            return True
            
        try:
            token_key = token[:8] + "_" + str(hash(token) % 1000000)
            return await self._safe_redis_operation('delete', token_key, prefix=TOKEN_CACHE_PREFIX) > 0
        except Exception as e:
            logger.error(f"删除令牌缓存失败: {str(e)}")
            return False 
from typing import Dict, Any, Optional, Tu<PERSON>
import logging
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends, HTTPException, status, Header

from models.user import User
from schemas.token import TokenPayload
from core.config import settings
from core.security import verify_password, generate_tokens, decode_token
from core.exceptions import AuthenticationException
from db.session import get_db
from utils.user_util import find_user_by_identity, get_user_by_id
from utils.session import SessionManager, get_session_manager

# 日志配置
logger = logging.getLogger(__name__)

async def authenticate_user(
    db: AsyncSession,
    username: str,
    password: str
) -> Optional[User]:
    """
    验证用户身份
    
    Args:
        db: 数据库会话
        username: 用户名/邮箱/手机号
        password: 密码
        
    Returns:
        验证通过的用户对象，验证失败则返回None
    """
    user = await find_user_by_identity(db, username)
    if not user:
        return None
    
    if not verify_password(password, user.hashed_password):
        return None
    
    return user

async def get_current_user_from_token(
    db: AsyncSession,
    token: str
) -> User:
    """
    从令牌获取当前用户
    
    Args:
        db: 数据库会话
        token: JWT令牌
        
    Returns:
        当前用户对象
        
    Raises:
        AuthenticationException: 认证失败
    """
    try:
        # 解码令牌
        payload = decode_token(token)
        
        # 检查令牌类型
        token_type = payload.get("type")
        if token_type != "access":
            raise AuthenticationException("无效的令牌类型")
        
        # 获取用户ID
        token_data = TokenPayload(**payload)
        
        if token_data.sub is None:
            raise AuthenticationException("无效的令牌数据")
        
        # 获取用户
        user_id = int(token_data.sub)
        user = await get_user_by_id(db, user_id)
        
        if user is None:
            raise AuthenticationException("用户不存在")
        
        if not user.is_active:
            raise AuthenticationException("用户已被禁用")
        
        return user
    
    except (ValueError, AuthenticationException) as e:
        raise AuthenticationException(str(e))
    except Exception as e:
        logger.error(f"获取当前用户错误: {str(e)}")
        raise AuthenticationException("无效的认证凭据")

async def get_current_active_user(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(get_token_from_header)
) -> User:
    """
    获取当前活跃用户
    
    Args:
        db: 数据库会话
        token: JWT令牌
        
    Returns:
        当前活跃用户对象
        
    Raises:
        AuthenticationException: 认证失败
    """
    return await get_current_user_from_token(db, token)

async def get_token_from_header(
    authorization: str = Header(None)
) -> str:
    """
    从请求头获取令牌
    
    Args:
        authorization: Authorization请求头
        
    Returns:
        JWT令牌
        
    Raises:
        AuthenticationException: 认证失败
    """
    if not authorization:
        raise AuthenticationException("未提供认证凭据")
    
    scheme, _, token = authorization.partition(" ")
    if scheme.lower() != "bearer":
        raise AuthenticationException("认证方案无效")
    
    if not token:
        raise AuthenticationException("令牌为空")
    
    return token

async def create_auth_tokens(
    user: User,
    session_manager: SessionManager,
    additional_data: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    创建认证令牌
    
    Args:
        user: 用户对象
        session_manager: 会话管理器
        additional_data: 要添加到令牌的额外数据
        
    Returns:
        包含访问令牌、刷新令牌和会话ID的字典
    """
    # 准备令牌数据
    token_data = {
        "username": user.username,
    }
    
    # 添加额外数据
    if additional_data:
        token_data.update(additional_data)
    
    # 生成令牌
    access_token, refresh_token = generate_tokens(
        subject=user.id,
        additional_data=token_data
    )
    
    # 创建会话
    session_data = {
        "username": user.username,
        "access_token": access_token,
    }
    session_id = await session_manager.create_session(
        user_id=user.id,
        data=session_data
    )
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "session_id": session_id,
        "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    }

async def refresh_auth_tokens(
    db: AsyncSession,
    refresh_token: str,
    session_manager: SessionManager
) -> Dict[str, Any]:
    """
    刷新认证令牌
    
    Args:
        db: 数据库会话
        refresh_token: 刷新令牌
        session_manager: 会话管理器
        
    Returns:
        包含新的访问令牌、刷新令牌和会话ID的字典
        
    Raises:
        AuthenticationException: 认证失败
    """
    try:
        # 解码令牌
        payload = decode_token(refresh_token)
        
        # 检查令牌类型
        token_type = payload.get("type")
        if token_type != "refresh":
            raise AuthenticationException("无效的令牌类型")
        
        # 获取用户ID
        sub = payload.get("sub")
        if not sub:
            raise AuthenticationException("无效的令牌数据")
        
        # 获取用户
        user_id = int(sub)
        user = await get_user_by_id(db, user_id)
        
        if user is None:
            raise AuthenticationException("用户不存在")
        
        if not user.is_active:
            raise AuthenticationException("用户已被禁用")
        
        # 准备令牌数据
        token_data = {
            "username": user.username,
        }
        
        # 生成新令牌
        access_token, refresh_token = generate_tokens(
            subject=user.id,
            additional_data=token_data
        )
        
        # 创建会话（并删除旧会话）
        await session_manager.delete_user_sessions(user_id)
        
        session_data = {
            "username": user.username,
            "access_token": access_token,
        }
        session_id = await session_manager.create_session(
            user_id=user.id,
            data=session_data
        )
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "session_id": session_id,
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        }
    
    except (ValueError, AuthenticationException) as e:
        raise AuthenticationException(str(e))
    except Exception as e:
        logger.error(f"刷新令牌错误: {str(e)}")
        raise AuthenticationException("无效的刷新令牌") 
"""
上传文件适配器
提供FastAPI的UploadFile类的扩展功能
"""
from fastapi import UploadFile
import io
import os
import tempfile
import shutil
from typing import BinaryIO, Optional, Union, Any

class UploadFileAdapter:
    """
    对FastAPI的UploadFile对象进行扩展，提供更多文件操作方法
    """
    
    def __init__(self, upload_file: UploadFile):
        """初始化适配器"""
        self.upload_file = upload_file
        self.temp_file: Optional[BinaryIO] = None
        self.temp_file_path: Optional[str] = None
        self._size: Optional[int] = None
        self._content: Optional[bytes] = None
        self._buffer: Optional[io.BytesIO] = None
    
    async def _ensure_content(self) -> bytes:
        """确保内容已加载"""
        if self._content is None:
            await self.upload_file.seek(0)
            self._content = await self.upload_file.read()
            # 同时准备一个BytesIO缓冲区
            self._buffer = io.BytesIO(self._content)
        return self._content
    
    async def to_tempfile(self) -> str:
        """
        将上传文件转换为临时文件，返回临时文件路径
        """
        if self.temp_file_path:
            return self.temp_file_path
            
        # 确保内容已加载
        content = await self._ensure_content()
        
        # 创建临时文件
        self.temp_file = tempfile.NamedTemporaryFile(delete=False)
        self.temp_file_path = self.temp_file.name
        
        # 写入内容
        self.temp_file.write(content)
        self.temp_file.close()
        
        return self.temp_file_path
    
    async def tell(self) -> int:
        """
        获取当前文件位置
        """
        # 确保缓冲区已准备好
        await self._ensure_content()
        
        # 从BytesIO缓冲区获取位置
        return self._buffer.tell()
    
    async def seek(self, offset: int, whence: int = 0) -> None:
        """
        设置文件指针位置
        """
        # 确保缓冲区已准备好
        await self._ensure_content()
        
        # 在BytesIO缓冲区上执行seek
        self._buffer.seek(offset, whence)
    
    async def read(self, size: int = -1) -> bytes:
        """
        读取文件内容
        """
        # 确保缓冲区已准备好
        await self._ensure_content()
        
        # 从BytesIO缓冲区读取
        return self._buffer.read(size)
    
    async def get_size(self) -> int:
        """
        获取文件大小
        """
        if self._size is not None:
            return self._size
        
        # 确保内容已加载
        content = await self._ensure_content()
        self._size = len(content)
        
        return self._size
    
    # 实现buffer协议兼容性
    def __getbuffer__(self, *args, **kwargs):
        """支持内存视图操作"""
        if not self._buffer:
            # 如果调用异步函数前尚未准备好，则需要同步获取内容
            # 这不是最佳做法，但在同步环境中可能会被调用
            import asyncio
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self._ensure_content())
        
        return self._buffer.__getbuffer__(*args, **kwargs)
    
    def read(self, size: int = -1) -> bytes:
        """同步读取方法，兼容非异步环境"""
        if not self._buffer:
            # 同步准备缓冲区
            import asyncio
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self._ensure_content())
        
        return self._buffer.read(size)
    
    def seek(self, offset: int, whence: int = 0) -> int:
        """同步seek方法，兼容非异步环境"""
        if not self._buffer:
            # 同步准备缓冲区
            import asyncio
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self._ensure_content())
        
        return self._buffer.seek(offset, whence)
    
    def tell(self) -> int:
        """同步tell方法，兼容非异步环境"""
        if not self._buffer:
            # 同步准备缓冲区
            import asyncio
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self._ensure_content())
        
        return self._buffer.tell()
    
    def __del__(self):
        """清理临时文件"""
        try:
            if self.temp_file_path and os.path.exists(self.temp_file_path):
                os.unlink(self.temp_file_path)
        except:
            pass

async def adapt_upload_file(upload_file: UploadFile) -> UploadFileAdapter:
    """
    创建UploadFile适配器
    """
    adapter = UploadFileAdapter(upload_file)
    # 预加载内容以避免后续问题
    await adapter._ensure_content()
    return adapter

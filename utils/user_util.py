from typing import Optional, Union, Dict, Any, List
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends, HTTPException, status

from models.user import User
from models.role import Role
from schemas.user import UserCreate
from core.config import settings
from core.security import get_password_hash, generate_random_password
from core.exceptions import DuplicateException, NotFoundException
from db.session import get_db

# 日志配置
logger = logging.getLogger(__name__)

async def get_user_by_email(
    db: AsyncSession, 
    email: str
) -> Optional[User]:
    """
    通过邮箱获取用户
    
    Args:
        db: 数据库会话
        email: 邮箱
        
    Returns:
        用户对象，如果不存在则返回None
    """
    user = await db.query(User).filter(User.email == email).first()
    return user

async def get_user_by_phone(
    db: AsyncSession, 
    phone: str
) -> Optional[User]:
    """
    通过手机号获取用户
    
    Args:
        db: 数据库会话
        phone: 手机号
        
    Returns:
        用户对象，如果不存在则返回None
    """
    user = await db.query(User).filter(User.phone == phone).first()
    return user

async def get_user_by_username(
    db: AsyncSession, 
    username: str
) -> Optional[User]:
    """
    通过用户名获取用户
    
    Args:
        db: 数据库会话
        username: 用户名
        
    Returns:
        用户对象，如果不存在则返回None
    """
    user = await db.query(User).filter(User.username == username).first()
    return user

async def get_user_by_id(
    db: AsyncSession, 
    user_id: int
) -> Optional[User]:
    """
    通过ID获取用户
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        用户对象，如果不存在则返回None
    """
    user = await db.query(User).filter(User.id == user_id).first()
    return user

async def find_user_by_identity(
    db: AsyncSession,
    identity: str
) -> Optional[User]:
    """
    通过身份标识(邮箱/手机号/用户名)查找用户
    
    Args:
        db: 数据库会话
        identity: 身份标识(邮箱/手机号/用户名)
        
    Returns:
        用户对象，如果不存在则返回None
    """
    # 先尝试邮箱查询
    user = await get_user_by_email(db, identity)
    if user:
        return user
    
    # 再尝试手机号查询
    user = await get_user_by_phone(db, identity)
    if user:
        return user
    
    # 最后尝试用户名查询
    user = await get_user_by_username(db, identity)
    return user

async def create_user(
    db: AsyncSession,
    user_in: Union[UserCreate, Dict[str, Any]],
    auto_commit: bool = True
) -> User:
    """
    创建新用户
    
    Args:
        db: 数据库会话
        user_in: 用户创建数据
        auto_commit: 是否自动提交事务
        
    Returns:
        创建的用户对象
        
    Raises:
        DuplicateException: 用户已存在
    """
    # 转换为字典
    if isinstance(user_in, UserCreate):
        user_data = user_in.dict(exclude_unset=True)
    else:
        user_data = user_in.copy()
    
    # 检查是否已存在
    if "email" in user_data and user_data["email"]:
        existing_user = await get_user_by_email(db, user_data["email"])
        if existing_user:
            raise DuplicateException(f"邮箱 {user_data['email']} 已被使用")
    
    if "phone" in user_data and user_data["phone"]:
        existing_user = await get_user_by_phone(db, user_data["phone"])
        if existing_user:
            raise DuplicateException(f"手机号 {user_data['phone']} 已被使用")
    
    if "username" in user_data and user_data["username"]:
        existing_user = await get_user_by_username(db, user_data["username"])
        if existing_user:
            raise DuplicateException(f"用户名 {user_data['username']} 已被使用")
    
    # 处理密码
    if "password" in user_data:
        hashed_password = get_password_hash(user_data["password"])
        user_data["hashed_password"] = hashed_password
        del user_data["password"]
    else:
        # 生成随机密码
        random_pass = generate_random_password()
        user_data["hashed_password"] = get_password_hash(random_pass)
    
    # 添加默认角色
    default_role_name = settings.DEFAULT_ROLE
    default_role = await db.query(Role).filter(Role.name == default_role_name).first()
    
    # 创建用户
    user = User(**user_data)
    
    if default_role:
        user.roles = [default_role]
    
    db.add(user)
    
    if auto_commit:
        await db.commit()
        await db.refresh(user)
    
    return user

async def get_user_roles(
    db: AsyncSession,
    user: User
) -> List[Role]:
    """
    获取用户的角色
    
    Args:
        db: 数据库会话
        user: 用户对象
        
    Returns:
        角色列表
    """
    if user.roles:
        return user.roles
    
    # 如果未加载角色，则重新查询
    await db.refresh(user, ["roles"])
    return user.roles

async def is_user_admin(
    db: AsyncSession,
    user: User
) -> bool:
    """
    检查用户是否为管理员
    
    Args:
        db: 数据库会话
        user: 用户对象
        
    Returns:
        是否为管理员
    """
    roles = await get_user_roles(db, user)
    return any(role.name == "admin" for role in roles)

async def get_default_role(
    db: AsyncSession
) -> Optional[Role]:
    """
    获取默认角色
    
    Args:
        db: 数据库会话
        
    Returns:
        默认角色对象，如果不存在则返回None
    """
    default_role_name = settings.DEFAULT_ROLE
    role = await db.query(Role).filter(Role.name == default_role_name).first()
    return role 
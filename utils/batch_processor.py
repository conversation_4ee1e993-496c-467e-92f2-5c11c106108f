import asyncio
from typing import List, Callable, Dict, Any, TypeVar, Generic, Optional
import logging

T = TypeVar('T')
R = TypeVar('R')

logger = logging.getLogger(__name__)

class BatchProcessor(Generic[T, R]):
    """
    异步请求批处理器
    用于聚合多个异步请求为批量处理，减少资源消耗
    """
    
    def __init__(
        self, 
        processor_func: Callable[[List[T]], asyncio.Future[List[R]]], 
        max_batch_size: int = 100,
        max_wait_time: float = 0.05
    ):
        """初始化批处理器
        
        Args:
            processor_func: 批处理函数，接收项目列表并返回结果列表
            max_batch_size: 最大批处理大小
            max_wait_time: 最大等待时间(秒)
        """
        self.processor_func = processor_func
        self.max_batch_size = max_batch_size
        self.max_wait_time = max_wait_time
        self.queue: List[Dict[str, Any]] = []
        self.event = asyncio.Event()
        self.lock = asyncio.Lock()
        self.processing = False
        
    async def process_item(self, item: T) -> R:
        """处理单个项目
        
        Args:
            item: 要处理的项目
            
        Returns:
            处理结果
        """
        future = asyncio.Future()
        
        queue_item = {
            "item": item,
            "future": future
        }
        
        # 添加到队列
        async with self.lock:
            self.queue.append(queue_item)
            
            # 如果队列已满，立即触发处理
            if len(self.queue) >= self.max_batch_size:
                if not self.processing:
                    self.processing = True
                    asyncio.create_task(self._process_batch())
            # 否则设置事件，延迟处理
            elif not self.event.is_set():
                self.event.set()
                asyncio.create_task(self._wait_and_process())
        
        # 等待结果
        return await future
        
    async def _wait_and_process(self):
        """等待一段时间后处理队列"""
        await asyncio.sleep(self.max_wait_time)
        
        async with self.lock:
            self.event.clear()
            if self.queue and not self.processing:
                self.processing = True
                asyncio.create_task(self._process_batch())
    
    async def _process_batch(self):
        """处理当前队列中的所有项目"""
        async with self.lock:
            # 获取当前队列
            current_batch = self.queue.copy()
            self.queue.clear()
            self.processing = False
        
        if not current_batch:
            return
            
        # 提取项目和对应的future
        items = [item["item"] for item in current_batch]
        futures = [item["future"] for item in current_batch]
        
        try:
            # 调用处理函数
            results = await self.processor_func(items)
            
            # 设置结果
            for i, result in enumerate(results):
                if i < len(futures):
                    if not futures[i].done():
                        futures[i].set_result(result)
        except Exception as e:
            logger.error(f"批处理失败: {str(e)}")
            # 设置异常
            for future in futures:
                if not future.done():
                    future.set_exception(e) 
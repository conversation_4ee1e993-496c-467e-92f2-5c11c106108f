"""
访问日志中间件
"""
import time
import json
from typing import Callable, Dict, List, Optional
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from utils.logger import access_logger
from core.config import settings

class AccessLogMiddleware(BaseHTTPMiddleware):
    """
    访问日志中间件，记录所有请求的基本信息
    """
    
    def __init__(
        self,
        app: ASGIApp,
        exclude_paths: List[str] = None,
        slow_request_threshold: float = 1.0,
    ) -> None:
        """
        初始化中间件
        
        Args:
            app: FastAPI应用实例
            exclude_paths: 排除的路径列表，这些路径的请求不会被记录
            slow_request_threshold: 慢请求阈值(秒)，超过该时间会记录为警告
        """
        super().__init__(app)
        self.exclude_paths = exclude_paths or []
        self.slow_request_threshold = slow_request_threshold
        
    async def dispatch(
        self, request: Request, call_next: Callable
    ) -> Response:
        """
        处理请求并记录访问日志
        
        Args:
            request: 请求对象
            call_next: 调用下一个中间件的函数
            
        Returns:
            响应对象
        """
        # 获取请求开始时间
        start_time = time.time()
        
        # 检查是否需要忽略此路径
        path = request.url.path
        if any(path.startswith(exclude_path) for exclude_path in self.exclude_paths):
            return await call_next(request)
        
        # 获取请求信息
        method = request.method
        client_host = request.client.host if request.client else "-"
        query_params = str(request.query_params) if request.query_params else ""
        url = f"{request.url.path}{f'?{query_params}' if query_params else ''}"
        
        # 获取请求头信息（过滤敏感信息）
        headers = {}
        for key, value in request.headers.items():
            # 排除敏感头信息如密码、令牌等
            if key.lower() not in ["authorization", "cookie", "password", "token", "secret", "api-key"]:
                headers[key] = value
                
        # 尝试获取用户标识（如果已认证）
        user_id = "-"
        try:
            if hasattr(request.state, "user_id"):
                user_id = str(request.state.user_id)
            elif hasattr(request.state, "api_client_id"):
                user_id = f"api:{request.state.api_client_id}"
        except Exception:
            pass
            
        # 获取用户代理信息
        user_agent = request.headers.get("user-agent", "-")
        
        # 获取引用来源
        referer = request.headers.get("referer", "-")
        
        # 记录请求开始信息（低级别）
        extra = {
            "client_addr": client_host,
            "request": f"{method} {url}",
            "status_code": 0,
            "user_id": user_id,
            "user_agent": user_agent,
            "referer": referer
        }
        
        # 执行请求处理
        try:
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录请求完成信息
            extra["status_code"] = response.status_code
            extra["process_time"] = f"{process_time:.4f}s"
            
            # 添加响应头（排除敏感信息）
            response_headers = {}
            for key, value in response.headers.items():
                if key.lower() not in ["set-cookie", "authorization"]:
                    response_headers[key] = value
            extra["response_headers"] = response_headers
            
            # 根据状态码决定日志级别
            if response.status_code >= 500:
                access_logger.error(
                    f"{method} {url} - {response.status_code} - {process_time:.4f}s",
                    extra=extra
                )
            elif response.status_code >= 400:
                access_logger.warning(
                    f"{method} {url} - {response.status_code} - {process_time:.4f}s",
                    extra=extra
                )
            else:
                # 检查是否为慢请求
                if process_time > self.slow_request_threshold:
                    access_logger.warning(
                        f"慢请求: {method} {url} - {response.status_code} - {process_time:.4f}s",
                        extra=extra
                    )
                else:
                    access_logger.info(
                        f"{method} {url} - {response.status_code} - {process_time:.4f}s",
                        extra=extra
                    )
                
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录异常信息
            extra["status_code"] = 500
            extra["process_time"] = f"{process_time:.4f}s"
            extra["error"] = str(e)
            
            access_logger.error(
                f"{method} {url} - 500 - {process_time:.4f}s - 异常: {str(e)}",
                extra=extra,
                exc_info=True
            )
            raise


def setup_access_logging(app, exclude_paths: List[str] = None, slow_request_threshold: float = None):
    """
    为FastAPI应用设置访问日志
    
    Args:
        app: FastAPI应用实例
        exclude_paths: 排除的路径列表
        slow_request_threshold: 慢请求阈值(秒)，默认从配置中获取
    """
    # 默认排除静态文件和健康检查路径
    default_exclude_paths = [
        "/static/",
        "/favicon.ico",
        "/health",
    ]
    
    exclude_paths = (exclude_paths or []) + default_exclude_paths
    
    # 从配置中获取慢请求阈值，默认为1秒
    if slow_request_threshold is None:
        slow_request_threshold = getattr(settings, "SLOW_REQUEST_THRESHOLD", 1.0)
    
    # 添加中间件
    app.add_middleware(
        AccessLogMiddleware,
        exclude_paths=exclude_paths,
        slow_request_threshold=slow_request_threshold,
    ) 
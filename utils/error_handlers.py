"""
错误处理工具函数
"""
from typing import Any, Dict, Optional, Callable, TypeVar, Awaitable
from functools import wraps
from fastapi import HTTPException
import logging

# 类型变量，用于泛型函数
T = TypeVar('T')

def handle_errors(
    success_code: str = "00000",
    success_msg: str = "操作成功",
    error_code: str = "B0001",
    error_msg: str = "操作失败",
    log_errors: bool = True
) -> Callable:
    """
    处理异步函数错误的装饰器
    
    Args:
        success_code: 成功时的返回码
        success_msg: 成功时的消息模板，可以使用 {result} 占位符
        error_code: 错误时的返回码
        error_msg: 错误时的消息模板，可以使用 {error} 占位符
        log_errors: 是否记录错误日志
        
    Returns:
        装饰后的函数
    """
    def decorator(func: Callable[..., Awaitable[T]]):
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Dict[str, Any]:
            try:
                result = await func(*args, **kwargs)
                
                # 处理成功响应
                response = {
                    "code": success_code,
                    "msg": success_msg.format(result=result) if '{result}' in success_msg else success_msg,
                    "data": result if result is not None else {}
                }
                
                # 如果结果是字典且包含code和msg，则使用这些值
                if isinstance(result, dict):
                    response.update({
                        k: v for k, v in result.items() 
                        if k in ["code", "msg", "data"] and v is not None
                    })
                
                return response
                
            except HTTPException as e:
                # 重新抛出HTTP异常，让FastAPI处理
                raise e
                
            except Exception as e:
                # 记录错误日志
                if log_errors:
                    logger = logging.getLogger(func.__module__)
                    logger.error(
                        f"Error in {func.__name__}: {str(e)}", 
                        exc_info=True
                    )
                
                # 返回错误响应
                return {
                    "code": error_code,
                    "msg": error_msg.format(error=str(e)) if '{error}' in error_msg else error_msg,
                    "data": None
                }
        return wrapper
    return decorator

# 短信验证码相关的错误处理
sms_code_error_handler = handle_errors(
    success_code="00000",
    success_msg="验证码已发送",
    error_code="B0001",
    error_msg="发送验证码失败: {error}",
    log_errors=True
)

# 用户认证相关的错误处理
auth_error_handler = handle_errors(
    success_code="00000",
    success_msg="操作成功",
    error_code="A0001",
    error_msg="认证失败: {error}",
    log_errors=True
)

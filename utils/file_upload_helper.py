"""
文件上传辅助工具
帮助处理文件上传过程中的各种操作
"""
import os
import tempfile
import uuid
import logging
from typing import BinaryIO, Tuple, Optional
from fastapi import UploadFile
import aiofiles
from pathlib import Path

# 配置日志
logger = logging.getLogger(__name__)

async def process_upload_to_file(
    upload_file: UploadFile,
    temp_dir: str = "temp"
) -> Tuple[str, int]:
    """
    处理上传文件，保存为临时文件
    
    Args:
        upload_file: FastAPI的上传文件对象
        temp_dir: 临时目录路径
        
    Returns:
        (临时文件路径, 文件大小)元组
    """
    # 确保临时目录存在
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir, exist_ok=True)
        
    # 生成临时文件路径
    temp_id = uuid.uuid4().hex
    filename = upload_file.filename or f"upload_{temp_id}"
    safe_filename = Path(filename).name  # 只保留文件名部分，排除路径
    temp_file_path = os.path.join(temp_dir, f"{temp_id}_{safe_filename}")
    
    # 读取上传文件内容并写入临时文件
    try:
        # 使用aiofiles进行异步文件读写
        await upload_file.seek(0)  # 重置文件指针到开始
        
        # 分块读取并写入临时文件
        file_size = 0
        async with aiofiles.open(temp_file_path, "wb") as out_file:
            while True:
                chunk = await upload_file.read(8192)  # 8KB块
                if not chunk:
                    break
                await out_file.write(chunk)
                file_size += len(chunk)
                
        logger.info(f"已保存上传文件到临时文件: {temp_file_path}, 大小: {file_size} 字节")
        return temp_file_path, file_size
    except Exception as e:
        # 如果出现错误，尝试清理临时文件
        logger.error(f"保存上传文件失败: {str(e)}")
        if os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except:
                pass
        raise

def clean_temp_file(temp_file_path: str) -> bool:
    """
    清理临时文件
    
    Args:
        temp_file_path: 临时文件路径
        
    Returns:
        是否成功清理
    """
    if not temp_file_path or not os.path.exists(temp_file_path):
        return False
        
    try:
        os.unlink(temp_file_path)
        logger.info(f"已清理临时文件: {temp_file_path}")
        return True
    except Exception as e:
        logger.warning(f"清理临时文件失败: {str(e)}")
        return False

def get_file_handle(temp_file_path: str) -> Optional[BinaryIO]:
    """
    获取文件句柄
    
    Args:
        temp_file_path: 临时文件路径
        
    Returns:
        文件句柄或None
    """
    if not os.path.exists(temp_file_path):
        return None
        
    try:
        return open(temp_file_path, "rb")
    except Exception as e:
        logger.error(f"打开文件失败: {str(e)}")
        return None 
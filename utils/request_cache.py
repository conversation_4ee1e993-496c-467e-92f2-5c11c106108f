import asyncio
import time
import logging
import hashlib
import json
from typing import Dict, Any, Optional, Callable, Union, TypeVar
from functools import wraps
from datetime import datetime
# 移除TTLCache导入，使用自定义缓存实现

logger = logging.getLogger(__name__)

T = TypeVar('T')

class APIRequestCache:
    """API请求缓存，避免重复请求相同数据"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 300):
        """初始化请求缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl: 缓存有效期(秒)
        """
        # 使用简单的字典缓存，手动管理TTL
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.locks: Dict[str, asyncio.Lock] = {}
        self.max_size = max_size
        self.default_ttl = ttl
        self.lock_cleanup_task = None
        self.start_lock_cleanup()
    
    def start_lock_cleanup(self):
        """启动锁清理任务"""
        async def cleanup_locks():
            while True:
                await asyncio.sleep(60)  # 每分钟执行一次
                self._cleanup_expired_locks()
        
        # 检查是否在事件循环中
        try:
            loop = asyncio.get_running_loop()
            self.lock_cleanup_task = asyncio.create_task(cleanup_locks())
        except RuntimeError:
            # 不在事件循环中，只记录警告不抛出异常
            logger.warning("未在运行中的事件循环内，跳过锁清理任务创建")
    
    def _cleanup_expired_locks(self):
        """清理过期的锁"""
        current_keys = set(self.cache.keys())
        lock_keys = set(self.locks.keys())

        # 移除不在缓存中的锁
        for key in lock_keys - current_keys:
            self.locks.pop(key, None)

    def _cleanup_expired_cache(self):
        """清理过期的缓存条目"""
        current_time = time.time()
        expired_keys = []

        for key, entry in self.cache.items():
            if isinstance(entry, dict) and current_time >= entry.get("expires", 0):
                expired_keys.append(key)

        for key in expired_keys:
            self.cache.pop(key, None)

    def _cleanup_old_cache(self):
        """清理旧的缓存条目以控制内存使用"""
        if len(self.cache) < self.max_size:
            return

        # 按创建时间排序，删除最旧的条目
        items = [(key, entry.get("created_at", 0)) for key, entry in self.cache.items()
                if isinstance(entry, dict)]
        items.sort(key=lambda x: x[1])

        # 删除最旧的25%条目
        num_to_delete = max(1, len(items) // 4)
        for i in range(num_to_delete):
            if i < len(items):
                self.cache.pop(items[i][0], None)
            
    def _get_lock(self, key: str) -> asyncio.Lock:
        """获取指定键的锁
        
        Args:
            key: 缓存键
            
        Returns:
            对应的锁对象
        """
        if key not in self.locks:
            self.locks[key] = asyncio.Lock()
        return self.locks[key]
    
    def _compute_key(self, func_name: str, *args, **kwargs) -> str:
        """计算缓存键
        
        Args:
            func_name: 函数名称
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            缓存键字符串
        """
        # 将参数转换为统一格式
        key_dict = {
            "func": func_name,
            "args": args,
            "kwargs": kwargs
        }
        
        # 使用JSON序列化并计算哈希值
        key_str = json.dumps(key_dict, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
        
    async def get_or_execute(
        self, 
        func: Callable[..., Union[T, asyncio.Future[T]]], 
        *args, 
        ttl: Optional[int] = None,
        **kwargs
    ) -> T:
        """获取缓存结果或执行函数
        
        Args:
            func: 要执行的函数
            *args: 位置参数
            ttl: 特定的缓存有效期(秒)
            **kwargs: 关键字参数
            
        Returns:
            函数执行结果
        """
        # 计算缓存键
        cache_key = self._compute_key(func.__qualname__, *args, **kwargs)
        
        # 检查缓存
        cached_entry = self.cache.get(cache_key)
        if cached_entry is not None:
            # 检查是否过期
            if time.time() < cached_entry.get("expires", 0):
                return cached_entry["data"]
            else:
                # 删除过期缓存
                self.cache.pop(cache_key, None)

        # 获取锁，避免相同请求并发执行
        lock = self._get_lock(cache_key)
        async with lock:
            # 再次检查缓存(可能在等待锁期间被其它协程填充)
            cached_entry = self.cache.get(cache_key)
            if cached_entry is not None:
                # 检查是否过期
                if time.time() < cached_entry.get("expires", 0):
                    return cached_entry["data"]
                else:
                    # 删除过期缓存
                    self.cache.pop(cache_key, None)
            
            # 执行函数
            start_time = time.time()
            try:
                # 检查函数是否是异步函数
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                    # 如果返回的是协程对象，等待其完成
                    if asyncio.iscoroutine(result):
                        result = await result
                    
                # 存储结果到缓存
                try:
                    # 清理过期缓存以控制内存使用
                    self._cleanup_expired_cache()

                    # 如果缓存太大，删除一些旧条目
                    if len(self.cache) >= self.max_size:
                        self._cleanup_old_cache()

                    # 计算过期时间
                    cache_ttl = ttl if ttl is not None else self.default_ttl
                    expires_at = time.time() + cache_ttl

                    # 存储到缓存
                    self.cache[cache_key] = {
                        "data": result,
                        "expires": expires_at,
                        "created_at": time.time()
                    }
                except Exception as cache_err:
                    logger.warning(f"缓存存储失败: {str(cache_err)}")
                    # 即使缓存失败，也要返回结果
                    pass
                    
                return result
            finally:
                duration = time.time() - start_time
                logger.debug(f"函数 {func.__qualname__} 执行耗时: {duration:.4f}秒")

# 缓存装饰器
def cached_request(ttl: Optional[int] = None):
    """缓存API请求的装饰器
    
    Args:
        ttl: 缓存有效期(秒)
    """
    # 创建一个共享的缓存实例
    cache = APIRequestCache()
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await cache.get_or_execute(func, *args, ttl=ttl, **kwargs)
        return wrapper
    return decorator 
import json
import random
import logging
import asyncio
from typing import Dict, Optional, Any, List, Union
import urllib.parse

# 阿里云SDK
from alibabacloud_dysmsapi20170525.client import Client as Dysmsapi20170525Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysmsapi_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
# 添加凭据相关SDK
from alibabacloud_credentials.client import Client as CredentialClient

from core.config import settings
from auth.backends.sms import sms_config, load_sms_config

# 日志记录器
logger = logging.getLogger(__name__)

async def send_sms_code(
    phone_number: str, 
    code: str, 
    template_param: Optional[Union[Dict, str]] = None,
    template_code: Optional[str] = None,
    sign_name: Optional[str] = None,
    access_key_id: Optional[str] = None,
    access_key_secret: Optional[str] = None
) -> Dict[str, Any]:
    """
    使用阿里云SMS发送验证码
    
    Args:
        phone_number: 手机号
        code: 验证码
        template_param: 短信模板参数，可以是字典或JSON字符串
        template_code: 短信模板代码（可选，优先使用数据库配置）
        sign_name: 短信签名（可选，优先使用数据库配置）
        access_key_id: 阿里云访问密钥ID（可选，优先使用数据库配置）
        access_key_secret: 阿里云访问密钥密码（可选，优先使用数据库配置）
        
    Returns:
        阿里云API的响应结果
    """
    log_context = f"[阿里云短信][{phone_number}]"
    try:
        # 记录函数调用
        logger.info(f"{log_context} 准备发送短信验证码")
        
        # 从数据库加载最新的短信配置
        await load_sms_config()
        
        # 使用数据库配置覆盖传入的参数
        if not template_code:
            template_code = sms_config.get("template_code")
        if not sign_name:
            sign_name = sms_config.get("sign_name")
        if not access_key_id:
            access_key_id = sms_config.get("access_key")
        if not access_key_secret:
            access_key_secret = sms_config.get("secret_key")
            
        # 确保所有必需的参数都有值
        if not all([template_code, sign_name, access_key_id, access_key_secret]):
            missing_params = []
            if not template_code: missing_params.append("template_code")
            if not sign_name: missing_params.append("sign_name")
            if not access_key_id: missing_params.append("access_key")
            if not access_key_secret: missing_params.append("secret_key")
            
            error_msg = f"短信配置参数不完整: 缺少 {', '.join(missing_params)}"
            logger.error(f"{log_context} {error_msg}")
            return {"Code": "Error", "Message": error_msg}
        
        # 如果未提供短信模板参数，则使用默认参数
        if not template_param:
            template_param = {"code": code}
        
        # 处理模板参数编码
        if isinstance(template_param, dict):
            template_param_str = json.dumps(template_param, ensure_ascii=True)
        else:
            template_param_str = template_param
            
        logger.debug(f"{log_context} 模板参数: {template_param_str}")
        
        # 构建客户端配置
        config = open_api_models.Config(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret
        )
        
        # 设置超时时间和端点
        config.connect_timeout = 5.0  # 连接超时5秒
        config.read_timeout = 10.0    # 读取超时10秒
        config.endpoint = "dysmsapi.aliyuncs.com"
        
        # 创建客户端
        client = Dysmsapi20170525Client(config)
        
        # 设置运行时选项
        runtime_options = util_models.RuntimeOptions()
        runtime_options.connect_timeout = 5000  # 毫秒
        runtime_options.read_timeout = 10000    # 毫秒
        runtime_options.retry_times = 0         # 不自动重试
        
        # 构建请求对象
        request = dysmsapi_models.SendSmsRequest(
            phone_numbers=phone_number,
            sign_name=sign_name,
            template_code=template_code,
            template_param=template_param_str
        )
        
        # 记录关键参数
        logger.debug(f"{log_context} 请求参数: SignName={sign_name}, TemplateCode={template_code}")
        
        # 发送请求
        response = client.send_sms_with_options(request, runtime_options)
        
        # 转换为字典
        result = {
            "RequestId": response.body.request_id,
            "Code": response.body.code,
            "Message": response.body.message,
            "BizId": response.body.biz_id
        }
        
        if result.get("Code") == "OK":
            logger.info(f"{log_context} 短信发送成功: RequestId={result.get('RequestId')}")
        else:
            logger.error(f"{log_context} 短信发送失败: Code={result.get('Code')}, Message={result.get('Message')}")
            
        return result
            
    except Exception as e:
        error_msg = str(e)
        logger.error(f"{log_context} 短信发送异常: {error_msg}", exc_info=True)
        
        # 获取阿里云诊断建议
        recommend = getattr(e, 'data', {}).get('Recommend', '')
        if recommend:
            logger.error(f"{log_context} 阿里云诊断建议: {recommend}")
            
        return {
            "Code": "Error",
            "Message": error_msg,
            "Recommend": recommend
        }

async def send_sms_with_credential(
    phone_numbers: str,
    sign_name: str,
    template_code: str,
    template_param: Union[Dict, str],
) -> Dict[str, Any]:
    """
    使用安全凭据方式发送短信（基于阿里云最新SDK示例）
    
    Args:
        phone_numbers: 手机号（多个用逗号分隔）
        sign_name: 短信签名
        template_code: 短信模板代码
        template_param: 短信模板参数，可以是字典或JSON字符串
        
    Returns:
        阿里云API的响应结果
    """
    log_context = f"[阿里云短信][{phone_numbers}]"
    logger.info(f"{log_context} 使用安全凭据方式发送短信")
    
    try:
        # 检查签名是否包含中文，如果包含则预先处理
        original_sign_name = sign_name
        contains_chinese = False
        for char in sign_name:
            if ord(char) > 127:
                contains_chinese = True
                break
                
        if contains_chinese:
            # 对中文签名进行URL编码
            sign_name = urllib.parse.quote(sign_name)
            logger.debug(f"{log_context} 签名包含中文，已进行URL编码: {sign_name}")
        
        # 不再使用安全凭据初始化客户端，改为从配置中获取AK/SK
        from auth.backends.sms import sms_config
        access_key = sms_config.get("access_key")
        secret_key = sms_config.get("secret_key")
        
        if not access_key or not secret_key:
            logger.error(f"{log_context} 缺少必要的AK/SK配置")
            return {
                "Code": "Error",
                "Message": "缺少必要的阿里云访问凭据配置"
            }
            
        # 使用AK/SK方式创建客户端，避免ECS元数据服务连接问题
        config = open_api_models.Config(
            access_key_id=access_key,
            access_key_secret=secret_key
        )
        config.endpoint = "dysmsapi.aliyuncs.com"
        client = Dysmsapi20170525Client(config)
        
        # 处理模板参数
        if isinstance(template_param, dict):
            template_param_str = json.dumps(template_param, ensure_ascii=True)
        else:
            template_param_str = template_param
        
        # 构建请求
        send_request = dysmsapi_models.SendSmsRequest(
            sign_name=sign_name,
            template_code=template_code,
            phone_numbers=phone_numbers,
            template_param=template_param_str
        )
        
        # 设置运行时选项
        runtime = util_models.RuntimeOptions()
        
        try:
        # 发送请求
            response = client.send_sms_with_options(send_request, runtime)
            
            # 转换为字典返回
            result = {
                "RequestId": response.body.request_id,
                "Code": response.body.code,
                "Message": response.body.message,
                "BizId": response.body.biz_id
            }
            
            if result.get("Code") == "OK":
                logger.info(f"{log_context} 短信发送成功: RequestId={result.get('RequestId')}")
            else:
                logger.error(f"{log_context} 短信发送失败: Code={result.get('Code')}, Message={result.get('Message')}")
                
                # 如果是签名相关错误，尝试使用临时ASCII签名
                if contains_chinese and ("InvalidSignName" in result.get("Code", "") or "签名" in result.get("Message", "")):
                    logger.warning(f"{log_context} 签名错误，尝试使用临时ASCII签名")
                    
                    # 生成临时签名
                    temp_sign = template_code.split('_')[0] if '_' in template_code else 'SMS'
                    
                    # 创建新请求
                    retry_request = dysmsapi_models.SendSmsRequest(
                        sign_name=temp_sign,
                        template_code=template_code,
                        phone_numbers=phone_numbers,
                        template_param=template_param_str
                    )
                    
                    # 再次发送
                    retry_response = client.send_sms_with_options(retry_request, runtime)
                    
                    # 返回结果
                    retry_result = {
                        "RequestId": retry_response.body.request_id,
                        "Code": retry_response.body.code,
                        "Message": retry_response.body.message,
                        "BizId": retry_response.body.biz_id
                    }
                    
                    if retry_result.get("Code") == "OK":
                        logger.warning(f"{log_context} 使用临时ASCII签名发送成功，但建议修复签名问题")
                    else:
                        logger.error(f"{log_context} 使用临时ASCII签名也失败: Code={retry_result.get('Code')}")
                    
                    return retry_result
                
            return result
            
        except Exception as error:
            # 如果出现编码错误，尝试使用临时ASCII签名
            if contains_chinese and ("latin-1" in str(error) or "codec can't encode" in str(error)):
                logger.warning(f"{log_context} 编码错误，尝试使用临时ASCII签名: {str(error)}")
                
                try:
                    # 使用模板代码的前缀作为临时签名
                    temp_sign = template_code.split('_')[0] if '_' in template_code else 'SMS'
                    
                    # 创建新请求
                    retry_request = dysmsapi_models.SendSmsRequest(
                        sign_name=temp_sign,
                        template_code=template_code,
                        phone_numbers=phone_numbers,
                        template_param=template_param_str
                    )
                    
                    # 再次发送
                    retry_response = client.send_sms_with_options(retry_request, runtime)
                    
                    # 返回结果
                    retry_result = {
                        "RequestId": retry_response.body.request_id,
                        "Code": retry_response.body.code,
                        "Message": retry_response.body.message,
                        "BizId": retry_response.body.biz_id
                    }
                    
                    if retry_result.get("Code") == "OK":
                        logger.warning(f"{log_context} 使用临时ASCII签名发送成功，但建议修复签名问题")
                    else:
                        logger.error(f"{log_context} 使用临时ASCII签名也失败: Code={retry_result.get('Code')}")
                    
                    return retry_result
                    
                except Exception as retry_error:
                    # 记录重试错误
                    logger.error(f"{log_context} 使用临时ASCII签名时出错: {str(retry_error)}")
            
            # 获取阿里云建议的诊断信息
            error_message = getattr(error, 'message', str(error))
            recommend = getattr(error, 'data', {}).get("Recommend", "")
            
            logger.error(f"{log_context} 发送短信失败: {error_message}")
            if recommend:
                logger.error(f"{log_context} 阿里云诊断建议: {recommend}")
                
            return {
                "Code": "Error",
                "Message": error_message,
                "Recommend": recommend
            }
    except Exception as e:
        logger.error(f"{log_context} 短信发送过程发生异常: {str(e)}", exc_info=True)
        return {"Code": "Error", "Message": str(e)}

async def generate_random_code(length: int = 6) -> str:
    """
    生成指定长度的随机数字验证码
    
    Args:
        length: 验证码长度
        
    Returns:
        随机数字验证码
    """
    return ''.join(random.choices('0123456789', k=length)) 
"""
OpenAPI工具模块，提供自定义路由类和文档生成工具
"""
from fastapi.routing import APIRoute
from fastapi import FastAPI
import hashlib
from core.config import settings
import logging
from typing import Dict, List, Any

logger = logging.getLogger("app")

class CustomRoute(APIRoute):
    """自定义API路由，用于修改操作ID生成方式"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
    
    def get_operation_id(self):
        """生成唯一的操作ID，防止冲突"""
        # 获取HTTP方法
        method = list(self.methods)[0].lower() if self.methods else "get"
        
        # 获取函数名
        endpoint_callable = self.endpoint
        if hasattr(endpoint_callable, "__name__"):
            func_name = endpoint_callable.__name__
        elif hasattr(endpoint_callable, "func"):
            # 处理 functools.partial 对象
            func_name = endpoint_callable.func.__name__
        else:
            # 如果无法获取函数名，使用哈希值
            func_name = f"endpoint_{id(endpoint_callable) % 1000}"
            
        # 获取模块名，用于进一步区分
        if hasattr(endpoint_callable, "__module__"):
            module_name = endpoint_callable.__module__.split(".")[-1]
        elif hasattr(endpoint_callable, "func") and hasattr(endpoint_callable.func, "__module__"):
            module_name = endpoint_callable.func.__module__.split(".")[-1]
        else:
            module_name = "unknown"
        
        # 清理路径，用于ID生成
        path = self.path
        # 移除API前缀
        if settings.API_V1_STR and path.startswith(settings.API_V1_STR):
            path = path[len(settings.API_V1_STR):]
        # 移除前导斜杠
        path = path.lstrip("/")
        # 替换路径中的特殊字符
        path = path.replace("/", "_").replace("{", "").replace("}", "").replace("-", "_")
        
        # 简化操作ID生成逻辑
        operation_id = f"{func_name}_{method}"
        
        # 如果存在有意义的模块名，添加到操作ID中
        if module_name not in ("endpoints", "unknown"):
            operation_id = f"{module_name}_{operation_id}"
            
        # 确保操作ID是唯一的
        if path and not path.isdigit():
            operation_id = f"{operation_id}_{path}"
        
        # 如果ID太长，使用哈希缩短但保持唯一性
        if len(operation_id) > 80:
            hash_obj = hashlib.md5(operation_id.encode())
            hash_id = hash_obj.hexdigest()[:8]
            operation_id = f"{func_name}_{hash_id}_{method}"
        
        # 确保操作ID是唯一且符合规范的 - 删除特殊字符
        operation_id = operation_id.replace(".", "_").replace("-", "_")
        
        return operation_id


def use_custom_openapi_generator(app: FastAPI):
    """
    配置FastAPI应用使用自定义OpenAPI文档生成器
    
    Args:
        app: FastAPI应用实例
    """
    from fastapi.openapi.utils import get_openapi
    import json
    
    def custom_openapi():
        """生成自定义OpenAPI文档"""
        # 如果希望每次都重新生成，可以注释掉下面的缓存逻辑
        if app.openapi_schema:
            return app.openapi_schema
            
        # 获取必要参数
        title = app.title
        version = app.version
        description = app.description
        routes = app.routes
        
        # 生成基础OpenAPI文档
        openapi_schema = get_openapi(
            title=title,
            version=version,
            description=description,
            routes=routes,
            openapi_version="3.0.3",  # 使用最新的OpenAPI 3.0版本
        )
        
        # 确保使用正确的OpenAPI版本
        openapi_schema["openapi"] = "3.0.3"
        
        # 设置服务器信息
        openapi_schema["servers"] = [
            {"url": "/", "description": "当前服务器"}
        ]
        
        # 提取所有使用的标签，并进行分组
        used_tags = set()
        for path in openapi_schema["paths"].values():
            for operation in path.values():
                if "tags" in operation:
                    for tag in operation["tags"]:
                        used_tags.add(tag)
        
        # 构建标签层次结构
        tag_hierarchy = {}
        for tag in used_tags:
            parts = tag.split("/")
            if len(parts) > 1:
                # 有子分组的标签
                parent = parts[0]
                child = parts[1]
                if parent not in tag_hierarchy:
                    tag_hierarchy[parent] = []
                tag_hierarchy[parent].append(child)
            else:
                # 没有子分组的标签
                if tag not in tag_hierarchy:
                    tag_hierarchy[tag] = []
        
        # 创建标准化的标签列表，保持顺序
        standardized_tags = []
        
        # 主要业务标签（按自定义顺序）
        main_tags_order = ["用户管理", "角色管理", "权限管理", "菜单管理", "系统配置", "认证"]
        for tag in main_tags_order:
            if tag in tag_hierarchy:
                # 添加主标签
                standardized_tags.append({
                    "name": tag,
                    "description": f"{tag}相关操作"
                })
                
                # 添加子标签（如果有）
                for child in tag_hierarchy[tag]:
                    full_tag = f"{tag}/{child}"
                    if full_tag in used_tags:
                        standardized_tags.append({
                            "name": full_tag,
                            "description": f"{tag}-{child}"
                        })
        
        # 添加其他未明确指定顺序的标签
        for tag in used_tags:
            found = False
            for existing in standardized_tags:
                if existing["name"] == tag:
                    found = True
                    break
            
            if not found:
                standardized_tags.append({
                    "name": tag,
                    "description": tag
                })
        
        # 设置规范化的标签
        openapi_schema["tags"] = standardized_tags
        
        # 确保组件对象存在
        if "components" not in openapi_schema:
            openapi_schema["components"] = {}
        
        # 添加安全定义
        openapi_schema["components"]["securitySchemes"] = {
            "bearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT",
                "description": "JWT认证，在请求头中使用Bearer token"
            },
            "cookieAuth": {
                "type": "apiKey",
                "in": "cookie",
                "name": "fastapiusers_auth",
                "description": "Cookie认证，用于Web应用场景"
            }
        }
        
        # 设置全局安全要求 - 同时支持JWT和Cookie认证
        openapi_schema["security"] = [{"bearerAuth": []}]
        
        # 遍历所有API路径，应用适当的安全机制
        # 注意：登录相关接口不需要认证，其他都需要
        for path, path_item in openapi_schema["paths"].items():
            for method, operation in path_item.items():
                # 登录接口不需要认证
                if (
                    ("/auth/jwt/login" in path and method.lower() == "post") or 
                    ("/auth/cookie/login" in path and method.lower() == "post") or
                    ("/auth/flexible-login" in path and method.lower() == "post") or
                    ("/auth/sms/login" in path and method.lower() == "post") or
                    ("/auth/cas/login" in path and method.lower() == "post") or
                    ("/auth/oauth/login" in path and method.lower() == "post") or
                    ("/auth/register" in path and method.lower() == "post") or
                    ("/auth/reset-password" in path and method.lower() == "post") or
                    ("/auth/send-verification-code" in path and method.lower() == "post")
                ):
                    # 如果有security字段，删除它，表示不需要认证
                    if "security" in operation:
                        del operation["security"]
                else:
                    # 其他接口默认使用JWT认证
                    operation["security"] = [{"bearerAuth": []}]
        
        # 清理schemas部分，格式化为OpenAPI 3.0规范
        if "schemas" in openapi_schema["components"]:
            for schema_name, schema in openapi_schema["components"]["schemas"].items():
                # 修复nullable字段
                if "nullable" in schema:
                    if schema["nullable"] is True and "type" in schema:
                        original_type = schema["type"]
                        if isinstance(original_type, str):
                            schema["type"] = [original_type, "null"]
                    # 删除nullable字段
                    del schema["nullable"]
                
                # 移除废弃的format
                if "properties" in schema:
                    for prop_name, prop in schema["properties"].items():
                        if "format" in prop and prop["format"] in ["date-time", "password"]:
                            # date-time是合法的，保留
                            if prop["format"] != "date-time":
                                del prop["format"]
        
        # 重新整理路径，确保相关端点分组在一起
        sorted_paths = {}
        path_groups = {}
        
        # 将路径按标签分组
        for path, path_data in openapi_schema["paths"].items():
            # 检查第一个操作的标签
            first_op = next(iter(path_data.values()))
            path_tag = first_op.get("tags", ["其他"])[0]
            
            if path_tag not in path_groups:
                path_groups[path_tag] = []
            
            path_groups[path_tag].append((path, path_data))
        
        # 按标签排序并重新生成paths对象
        for tag in sorted(path_groups.keys()):
            for path, path_data in path_groups[tag]:
                sorted_paths[path] = path_data
        
        # 使用排序后的路径
        openapi_schema["paths"] = sorted_paths
        
        # 缓存结果
        app.openapi_schema = openapi_schema
        return openapi_schema
    
    # 替换FastAPI的openapi方法
    app.openapi = custom_openapi


def setup_custom_route_class(app: FastAPI):
    """
    为FastAPI应用设置自定义路由类
    
    Args:
        app: FastAPI应用实例
    """
    app.router.route_class = CustomRoute 
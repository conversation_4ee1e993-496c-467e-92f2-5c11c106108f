"""
审计日志中间件 - 用于记录API操作并保存到数据库
"""
import time
import json
import logging
import asyncio
from typing import Callable, Dict, Any, Optional, List
from datetime import datetime
from fastapi import Request, Response

from core.config import settings
from db.session import get_db
from crud.crud_audit_log import create_audit_log
from schemas.audit_log import AuditLogCreate

# 配置日志
logger = logging.getLogger(__name__)

# 创建全局队列和锁
_audit_queue = asyncio.Queue()
_queue_running = False
_queue_lock = asyncio.Lock()

# 跳过审计的路径列表
DEFAULT_SKIP_PATHS = [
    "/docs",
    "/redoc",
    "/openapi.json",
    "/static/",
    "/files/",
    "/api/v1/health",
]

# 敏感字段列表，将被过滤
SENSITIVE_FIELDS = [
    "password", 
    "token", 
    "secret", 
    "key", 
    "auth", 
    "credential", 
    "access_key", 
    "secret_key"
]

class AuditLogMiddleware:
    """审计日志中间件，记录API请求和响应信息"""
    
    def __init__(
        self,
        app,
        exclude_paths: List[str] = None,
        exclude_namespaces: List[str] = None,
        include_request_body: bool = True,
        filter_sensitive_data: bool = True,
    ):
        """
        初始化中间件
        
        Args:
            app: FastAPI应用实例
            exclude_paths: 排除的路径列表，这些路径的请求不会被记录
            exclude_namespaces: 排除的命名空间列表，以这些前缀开头的路径不会被记录
            include_request_body: 是否包含请求体内容
            filter_sensitive_data: 是否过滤敏感数据
        """
        self.app = app
        self.exclude_paths = exclude_paths or DEFAULT_SKIP_PATHS
        self.exclude_namespaces = exclude_namespaces or []
        self.include_request_body = include_request_body
        self.filter_sensitive_data = filter_sensitive_data
        
        # 启动队列处理器
        self._start_queue_processor()
    
    async def __call__(self, scope: Dict, receive: Callable, send: Callable):
        """处理请求并记录审计日志"""
        if scope["type"] != "http":
            return await self.app(scope, receive, send)
        
        path = scope.get("path", "")
        
        # 检查是否在排除路径中
        if any(path.startswith(ex_path) for ex_path in self.exclude_paths):
            return await self.app(scope, receive, send)
            
        # 检查是否在排除命名空间中
        if any(path.startswith(namespace) for namespace in self.exclude_namespaces):
            return await self.app(scope, receive, send)
        
        # 开始计时
        start_time = time.time()
        
        # 提取请求信息
        method = scope.get("method", "")
        client = scope.get("client", ("", ""))[0] if scope.get("client") else None
        
        # 尝试获取headers
        headers = {k.decode(): v.decode() for k, v in scope.get("headers", [])}
        user_agent = headers.get("user-agent", "")
        
        # 尝试获取查询参数
        query_string = scope.get("query_string", b"").decode()
        query_params = {}
        if query_string:
            try:
                from urllib.parse import parse_qs
                query_params = {k: v[0] if len(v) == 1 else v for k, v in parse_qs(query_string).items()}
            except Exception as e:
                logger.warning(f"解析查询参数失败: {str(e)}")
        
        # 初始化变量
        request_body = None
        response_status = 200
        user_id = None
        username = None
        resource = "unknown"
        resource_id = None
        action = self._determine_action(method, path)
        
        # 自定义接收函数，用于捕获请求体
        async def receive_wrapper():
            nonlocal request_body
            
            message = await receive()
            
            if message["type"] == "http.request" and self.include_request_body:
                body = message.get("body", b"")
                if body:
                    try:
                        request_body = json.loads(body.decode())
                        if self.filter_sensitive_data:
                            request_body = self._filter_sensitive_data(request_body)
                    except Exception as e:
                        logger.debug(f"解析请求体失败: {str(e)}")
            
            return message
        
        # 自定义发送函数，用于捕获响应状态码
        async def send_wrapper(message):
            nonlocal response_status
            
            if message["type"] == "http.response.start":
                response_status = message.get("status", 200)
            
            await send(message)
        
        # 处理请求
        try:
            # 调用下一个中间件或应用处理请求
            await self.app(scope, receive_wrapper, send_wrapper)
            success = response_status < 400
        except Exception as e:
            logger.error(f"请求处理异常: {str(e)}")
            response_status = 500
            success = False
            raise
        finally:
            # 计算执行时间
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 尝试提取用户信息
            try:
                authorization = headers.get("authorization", "")
                if authorization and authorization.startswith("Bearer "):
                    from api.deps import get_token_from_authorization, decode_token
                    token = authorization.split(" ")[1]
                    if token:
                        payload = decode_token(token)
                        user_id = int(payload.get("sub")) if payload.get("sub") else None
                        # 如果有机会从token中获取username，也可以在这里提取
            except Exception as e:
                logger.debug(f"从令牌获取用户ID失败: {str(e)}")
            
            # 提取资源类型和ID
            resource, resource_id = self._extract_resource_info(path)
            
            # 创建审计日志记录
            log_data = AuditLogCreate(
                user_id=user_id,
                username=username,
                action=action,
                resource=resource,
                resource_id=resource_id,
                method=method,
                path=path,
                query_params=query_params,
                request_body=request_body,
                response_code=response_status,
                ip_address=client,
                user_agent=user_agent,
                execution_time=execution_time,
                message=f"{method} {path} - {response_status}",
                success=success,
            )
            
            # 将日志加入队列
            await self._add_to_queue(log_data)
    
    def _determine_action(self, method: str, path: str) -> str:
        """根据HTTP方法确定操作类型"""
        method = method.upper()
        
        if method == "GET":
            return "read"
        elif method == "POST":
            return "create"
        elif method == "PUT" or method == "PATCH":
            return "update"
        elif method == "DELETE":
            return "delete"
        else:
            return "other"
    
    def _extract_resource_info(self, path: str) -> tuple:
        """从路径中提取资源类型和ID"""
        parts = path.strip("/").split("/")
        
        # 跳过api/v1前缀
        if len(parts) >= 2 and parts[0] == "api" and parts[1].startswith("v"):
            parts = parts[2:]
        
        if not parts:
            return "unknown", None
        
        # 第一个部分通常是资源类型
        resource = parts[0]
        
        # 如果有第二个部分，且是数字，可能是资源ID
        resource_id = None
        if len(parts) > 1:
            try:
                # 尝试将第二部分解析为ID
                if parts[1].isdigit():
                    resource_id = parts[1]
                # 检查是否是UUID格式
                elif len(parts[1]) == 36 and parts[1].count("-") == 4:
                    resource_id = parts[1]
            except:
                pass
        
        return resource, resource_id
    
    def _filter_sensitive_data(self, data: Dict) -> Dict:
        """过滤请求体中的敏感数据"""
        if not isinstance(data, dict):
            return data
        
        filtered_data = data.copy()
        
        for key in filtered_data:
            key_lower = key.lower()
            
            # 检查是否是敏感字段
            if any(sensitive in key_lower for sensitive in SENSITIVE_FIELDS):
                filtered_data[key] = "***FILTERED***"
            
            # 递归处理嵌套字典
            elif isinstance(filtered_data[key], dict):
                filtered_data[key] = self._filter_sensitive_data(filtered_data[key])
            
            # 递归处理嵌套列表
            elif isinstance(filtered_data[key], list):
                filtered_data[key] = [
                    self._filter_sensitive_data(item) if isinstance(item, dict) else item
                    for item in filtered_data[key]
                ]
        
        return filtered_data
    
    async def _add_to_queue(self, log_data: AuditLogCreate):
        """将审计日志添加到队列"""
        try:
            await _audit_queue.put(log_data)
        except Exception as e:
            logger.error(f"添加审计日志到队列失败: {str(e)}")
    
    def _start_queue_processor(self):
        """启动队列处理任务"""
        global _queue_running
        
        async def process_queue():
            global _queue_running
            _queue_running = True
            
            try:
                while _queue_running:
                    # 等待队列中的日志
                    try:
                        # 批量处理，一次最多处理10条日志
                        batch = []
                        for _ in range(10):
                            try:
                                # 不阻塞，快速获取日志
                                log_data = _audit_queue.get_nowait()
                                batch.append(log_data)
                                _audit_queue.task_done()
                            except asyncio.QueueEmpty:
                                break
                        
                        if not batch:
                            # 如果队列为空，等待一些时间
                            await asyncio.sleep(1)
                            continue
                        
                        # 批量处理日志
                        async for db in get_db():
                            for log_data in batch:
                                try:
                                    # 兼容Pydantic V1和V2
                                    model_data = {}
                                    try:
                                        # 尝试使用Pydantic V2的model_dump方法
                                        model_data = log_data.model_dump()
                                    except AttributeError:
                                        # 回退到Pydantic V1的dict方法
                                        model_data = log_data.dict()
                                        
                                    await create_audit_log(
                                        db=db,
                                        **model_data
                                    )
                                except Exception as e:
                                    logger.error(f"保存审计日志到数据库失败: {str(e)}")
                    
                    except Exception as e:
                        logger.error(f"处理审计日志队列时出错: {str(e)}")
                        await asyncio.sleep(1)  # 出错时暂停一下
            
            finally:
                _queue_running = False
        
        # 启动异步任务
        asyncio.create_task(process_queue())
    
    @staticmethod
    async def stop_queue_processor():
        """停止队列处理任务"""
        global _queue_running
        async with _queue_lock:
            _queue_running = False
            
            # 等待队列清空
            if not _audit_queue.empty():
                logger.info("等待审计日志队列清空...")
                await _audit_queue.join()
            
            logger.info("审计日志队列处理器已停止")

# 为应用添加审计日志
def setup_audit_logging(app):
    """为FastAPI应用添加审计日志中间件"""
    app.add_middleware(
        AuditLogMiddleware,
        exclude_paths=DEFAULT_SKIP_PATHS,
        exclude_namespaces=["/static/", "/files/", "/ws/"],
        include_request_body=True,
        filter_sensitive_data=True,
    )
    
    # 添加关闭时的清理工作
    @app.on_event("shutdown")
    async def shutdown_audit_logging():
        """应用关闭时停止审计日志处理"""
        await AuditLogMiddleware.stop_queue_processor() 
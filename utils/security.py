"""
安全相关工具函数
"""
from fastapi import Request
from core.config import settings
import logging
import base64
import time
import os

logger = logging.getLogger("app")

# 缓存认证结果，避免频繁验证（60秒内有效）
_auth_cache = {}

async def check_swagger_auth(request: Request) -> bool:
    """
    检查Swagger文档的访问认证
    
    支持多种认证方式：
    1. HTTP基本认证 (Authorization头)
    2. 查询参数令牌 (access_token参数)
    3. IP地址白名单
    4. 开发环境自动通过
    
    Args:
        request: 请求对象
        
    Returns:
        bool: 认证是否通过
    """
    # 获取客户端IP
    client_host = request.client.host if request.client else None
    
    # 检查缓存
    cache_key = f"{client_host}_{request.url.path}"
    if cache_key in _auth_cache:
        cached_result, timestamp = _auth_cache[cache_key]
        # 缓存60秒有效
        if time.time() - timestamp < 60:
            return cached_result

    # 方法0：开发环境直接通过
    env = os.getenv("ENV", "").lower()
    if env in ("dev", "development", "local", "test"):
        logger.debug(f"Swagger文档访问：开发环境自动通过 (IP: {client_host})")
        _cache_result(cache_key, True)
        return True

    # 方法1：基本认证（用户名密码）
    auth_header = request.headers.get("Authorization")
    if auth_header:
        try:
            scheme, credentials = auth_header.split()
            if scheme.lower() == "basic":
                decoded = base64.b64decode(credentials).decode("utf-8")
                username, password = decoded.split(":")
                # 检查用户名和密码
                swagger_user = os.getenv("SWAGGER_USER", "admin")
                swagger_password = os.getenv("SWAGGER_PASSWORD", "swagger_secure_password")
                if username == swagger_user and password == swagger_password:
                    logger.info(f"Swagger文档访问：基本认证成功 (用户: {username}, IP: {client_host})")
                    _cache_result(cache_key, True)
                    return True
                else:
                    logger.warning(f"Swagger文档访问：基本认证失败 (用户: {username}, IP: {client_host})")
        except Exception as e:
            logger.warning(f"Swagger文档访问：认证解析错误 ({str(e)}, IP: {client_host})")
    
    # 方法2：接受查询参数中的访问令牌
    token = request.query_params.get("access_token")
    if token:
        # 从环境变量获取令牌或使用默认值
        valid_token = os.getenv("SWAGGER_TOKEN", "swagger_secret_token")
        if token == valid_token:
            logger.info(f"Swagger文档访问：令牌认证成功 (IP: {client_host})")
            _cache_result(cache_key, True)
            return True
        else:
            logger.warning(f"Swagger文档访问：令牌认证失败 (IP: {client_host})")
        
    # 方法3：检查是否在允许的IP范围内
    allowed_ips = os.getenv("SWAGGER_ALLOWED_IPS", "127.0.0.1,::1").split(",")
    allowed_ips = [ip.strip() for ip in allowed_ips]
    
    # 添加本地开发机器的IP地址
    allowed_ips.extend(["127.0.0.1", "::1", "localhost"])
    
    if client_host in allowed_ips:
        logger.info(f"Swagger文档访问：IP白名单通过 (IP: {client_host})")
        _cache_result(cache_key, True)
        return True
    
    # 所有认证方式都失败
    logger.warning(f"Swagger文档访问：所有认证方式均失败 (IP: {client_host})")
    _cache_result(cache_key, False)
    return False


def _cache_result(key: str, result: bool):
    """
    缓存认证结果
    
    Args:
        key: 缓存键名
        result: 认证结果
    """
    _auth_cache[key] = (result, time.time())
    
    # 清理过期缓存
    now = time.time()
    expired_keys = [k for k, (_, t) in _auth_cache.items() if now - t > 3600]  # 1小时后过期
    for k in expired_keys:
        del _auth_cache[k] 
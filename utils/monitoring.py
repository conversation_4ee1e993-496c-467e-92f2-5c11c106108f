"""
系统监控工具 - 用于监控系统性能和API请求
"""
import time
import logging
import functools
from typing import Callable, Dict, Any, Optional
from datetime import datetime
import traceback
import json
import inspect
import asyncio
from fastapi import Request, Response

# 配置日志
logger = logging.getLogger(__name__)

# 性能计时装饰器
def timer(func):
    """测量函数执行时间的装饰器"""
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            logger.info(f"函数 {func.__name__} 执行时间: {execution_time:.4f}秒")
            
            # 记录慢查询
            if execution_time > 1.0:  # 超过1秒视为慢查询
                logger.warning(f"慢查询: {func.__name__} 执行时间: {execution_time:.4f}秒")
                
            return result
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            logger.error(f"函数 {func.__name__} 执行失败，用时: {execution_time:.4f}秒，错误: {str(e)}")
            raise
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            logger.info(f"函数 {func.__name__} 执行时间: {execution_time:.4f}秒")
            
            # 记录慢查询
            if execution_time > 1.0:  # 超过1秒视为慢查询
                logger.warning(f"慢查询: {func.__name__} 执行时间: {execution_time:.4f}秒")
                
            return result
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            logger.error(f"函数 {func.__name__} 执行失败，用时: {execution_time:.4f}秒，错误: {str(e)}")
            raise
    
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper

# 请求日志中间件
class RequestLogMiddleware:
    """记录请求详情的中间件"""
    
    def __init__(self, app):
        """初始化中间件"""
        self.app = app
    
    async def __call__(self, scope, receive, send):
        """处理请求"""
        if scope["type"] != "http":
            return await self.app(scope, receive, send)
        
        start_time = time.time()
        
        # 提取请求信息
        request_id = f"{int(time.time() * 1000)}-{hash(scope.get('client', ('', ''))[0])}"
        method = scope.get("method", "")
        path = scope.get("path", "")
        client = scope.get("client", ("", ""))[0]
        
        # 记录请求开始
        logger.info(f"Request-{request_id}: {method} {path} from {client}")
        
        # 调用下一个中间件或应用
        response_started = False
        response_status = 0
        
        async def wrapped_send(message):
            nonlocal response_started, response_status
            
            if message["type"] == "http.response.start":
                response_started = True
                response_status = message.get("status", 0)
            
            await send(message)
        
        try:
            await self.app(scope, receive, wrapped_send)
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 记录请求完成
            logger.info(f"Response-{request_id}: {method} {path} - {response_status} in {execution_time:.4f}s")
            
            # 记录慢请求
            if execution_time > 1.0:  # 超过1秒视为慢请求
                logger.warning(f"慢请求: {method} {path} - {response_status} in {execution_time:.4f}s")
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 记录请求异常
            logger.error(f"Error-{request_id}: {method} {path} - Exception in {execution_time:.4f}s: {str(e)}")
            traceback.print_exc()
            
            # 继续抛出异常，让异常处理器处理
            raise

# 性能统计类
class PerformanceStats:
    """性能统计类，用于收集和分析性能数据"""
    
    def __init__(self, redis_client=None, max_records=1000):
        """
        初始化性能统计
        
        Args:
            redis_client: Redis客户端，用于存储统计数据
            max_records: 内存中保存的最大记录数
        """
        self.stats = {}
        self.redis_client = redis_client
        self.max_records = max_records
        self.redis_key_prefix = "perf_stats:"
    
    async def record_request(self, endpoint: str, method: str, status_code: int, 
                           duration: float, user_id: Optional[int] = None):
        """
        记录请求统计
        
        Args:
            endpoint: API端点路径
            method: HTTP方法
            status_code: 响应状态码
            duration: 请求处理时间（秒）
            user_id: 用户ID（如果有）
        """
        # 生成请求的唯一键
        key = f"{method}:{endpoint}"
        
        # 获取当前时间
        now = datetime.now().isoformat()
        
        # 创建统计记录
        record = {
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "duration": duration,
            "timestamp": now,
            "user_id": user_id
        }
        
        # 添加到内存统计
        if key not in self.stats:
            self.stats[key] = {
                "count": 0,
                "total_duration": 0,
                "avg_duration": 0,
                "min_duration": float('inf'),
                "max_duration": 0,
                "status_codes": {},
                "recent_requests": []
            }
        
        # 更新统计信息
        self.stats[key]["count"] += 1
        self.stats[key]["total_duration"] += duration
        self.stats[key]["avg_duration"] = self.stats[key]["total_duration"] / self.stats[key]["count"]
        self.stats[key]["min_duration"] = min(self.stats[key]["min_duration"], duration)
        self.stats[key]["max_duration"] = max(self.stats[key]["max_duration"], duration)
        
        # 更新状态码统计
        str_status = str(status_code)
        if str_status not in self.stats[key]["status_codes"]:
            self.stats[key]["status_codes"][str_status] = 0
        self.stats[key]["status_codes"][str_status] += 1
        
        # 添加到最近请求列表
        self.stats[key]["recent_requests"].append(record)
        
        # 如果超过最大记录数，移除最旧的记录
        if len(self.stats[key]["recent_requests"]) > self.max_records:
            self.stats[key]["recent_requests"].pop(0)
        
        # 如果配置了Redis，保存到Redis
        if self.redis_client:
            try:
                # 保存请求记录
                redis_key = f"{self.redis_key_prefix}{key}:{now}"
                await self.redis_client.set(redis_key, json.dumps(record))
                await self.redis_client.expire(redis_key, 60 * 60 * 24)  # 24小时过期
                
                # 更新统计信息
                stats_key = f"{self.redis_key_prefix}stats:{key}"
                stats_data = {
                    "count": self.stats[key]["count"],
                    "avg_duration": self.stats[key]["avg_duration"],
                    "min_duration": self.stats[key]["min_duration"],
                    "max_duration": self.stats[key]["max_duration"],
                    "status_codes": json.dumps(self.stats[key]["status_codes"]),
                    "last_updated": now
                }
                await self.redis_client.hset(stats_key, mapping=stats_data)
                await self.redis_client.expire(stats_key, 60 * 60 * 24 * 7)  # 7天过期
            except Exception as e:
                logger.warning(f"保存性能统计到Redis失败: {str(e)}")
    
    def get_stats(self, endpoint: Optional[str] = None):
        """
        获取统计信息
        
        Args:
            endpoint: 要获取的特定端点，为None时返回所有端点的统计
            
        Returns:
            统计信息字典
        """
        if endpoint:
            return {k: v for k, v in self.stats.items() if endpoint in k}
        return self.stats
    
    def get_slow_endpoints(self, threshold: float = 1.0):
        """
        获取慢端点列表
        
        Args:
            threshold: 判断为慢端点的阈值（秒）
            
        Returns:
            慢端点统计列表
        """
        return {k: v for k, v in self.stats.items() if v["avg_duration"] > threshold}
    
    def get_error_endpoints(self):
        """
        获取有错误的端点列表（状态码 >= 400）
        
        Returns:
            有错误的端点统计列表
        """
        return {
            k: v for k, v in self.stats.items() 
            if any(int(code) >= 400 for code in v["status_codes"].keys())
        }

# 单例获取函数
_perf_stats = None
_stats_lock = asyncio.Lock()

async def get_performance_stats():
    """获取性能统计实例（单例模式）"""
    global _perf_stats
    
    if _perf_stats is None:
        async with _stats_lock:
            if _perf_stats is None:
                # 导入在函数内部以避免循环导入
                try:
                    from services.redis_service import RedisService
                    redis_client = await RedisService.get_client()  # 获取Redis客户端而非缓存实例
                except Exception as e:
                    logger.warning(f"无法获取Redis客户端，使用内存统计: {str(e)}")
                    redis_client = None
                
                _perf_stats = PerformanceStats(redis_client=redis_client)
    
    return _perf_stats

# 性能监控中间件
class PerformanceMiddleware:
    """性能监控中间件，记录API请求的性能数据"""
    
    def __init__(self, app):
        """初始化中间件"""
        self.app = app
    
    async def __call__(self, scope, receive, send):
        """处理请求"""
        if scope["type"] != "http":
            return await self.app(scope, receive, send)
        
        start_time = time.time()
        
        # 提取请求信息
        method = scope.get("method", "")
        path = scope.get("path", "")
        
        # 初始化响应信息
        response_status = 0
        user_id = None
        
        # 自定义发送函数以获取响应状态码
        async def wrapped_send(message):
            nonlocal response_status
            
            if message["type"] == "http.response.start":
                response_status = message.get("status", 0)
            
            await send(message)
        
        # 调用下一个中间件或应用
        try:
            await self.app(scope, receive, wrapped_send)
        finally:
            # 计算请求处理时间
            end_time = time.time()
            duration = end_time - start_time
            
            # 记录性能统计
            try:
                # 获取性能统计实例
                stats = await get_performance_stats()
                
                # 尝试从授权中获取用户ID
                if "authorization" in scope.get("headers", []):
                    # 导入在函数内部以避免循环导入
                    try:
                        from api.deps import get_token_from_authorization, decode_token
                        authorization = next((v.decode() for k, v in scope["headers"] if k.decode().lower() == "authorization"), None)
                        if authorization:
                            token = authorization.split(" ")[1] if len(authorization.split(" ")) > 1 else None
                            if token:
                                payload = decode_token(token)
                                user_id = int(payload.get("sub")) if payload.get("sub") else None
                    except Exception as e:
                        logger.debug(f"从令牌获取用户ID失败: {str(e)}")
                
                await stats.record_request(
                    endpoint=path,
                    method=method,
                    status_code=response_status,
                    duration=duration,
                    user_id=user_id
                )
            except Exception as e:
                logger.warning(f"记录性能统计失败: {str(e)}")

# 为应用添加监控
def setup_monitoring(app):
    """为FastAPI应用添加监控中间件"""
    app.add_middleware(RequestLogMiddleware)
    app.add_middleware(PerformanceMiddleware)
    
    # 添加监控统计接口
    from fastapi import APIRouter, Depends
    from auth.dependencies import has_permission
    
    router = APIRouter(prefix="/api/v1/monitoring", tags=["监控"])
    
    @router.get("/stats")
    async def get_stats(current_user=Depends(has_permission("monitoring:read"))):
        """获取性能统计"""
        stats = await get_performance_stats()
        return stats.get_stats()
    
    @router.get("/slow-endpoints")
    async def get_slow_endpoints(
        threshold: float = 0.5,
        current_user=Depends(has_permission("monitoring:read"))
    ):
        """获取慢端点列表"""
        stats = await get_performance_stats()
        return stats.get_slow_endpoints(threshold)
    
    @router.get("/error-endpoints")
    async def get_error_endpoints(current_user=Depends(has_permission("monitoring:read"))):
        """获取有错误的端点列表"""
        stats = await get_performance_stats()
        return stats.get_error_endpoints()
    
    app.include_router(router)
    
    return app 
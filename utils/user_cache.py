"""
用户缓存工具模块
提供用户数据的缓存操作
"""
from typing import Dict, List, Optional, Any
import logging
from datetime import timedelta
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from utils.auth_cache import AuthCache
from db.redis import RedisCache
from models.user import User
from schemas.user import User as UserSchema

logger = logging.getLogger(__name__)


class UserCache:
    """用户缓存工具类，提供用户数据的缓存操作"""
    
    def __init__(self, redis_cache: Optional[RedisCache]):
        """
        初始化用户缓存工具
        
        Args:
            redis_cache: Redis缓存工具，可能为None表示不使用缓存
        """
        self.cache = redis_cache
        self.enabled = redis_cache is not None
        self.auth_cache = AuthCache(redis_cache)
        
        # 缓存前缀
        self.USER_DETAIL_PREFIX = "user:detail:"
        self.USER_ROLES_PREFIX = "user:roles:"
        self.USER_PERMS_PREFIX = "user:permissions:"
        self.USER_MENU_PREFIX = "user:menus:"
    
    async def get_user_detail(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        获取缓存的用户详情
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户详情数据，如果缓存未命中则返回None
        """
        if not self.enabled:
            return None
            
        try:
            return await self.cache.get(f"{user_id}", prefix=self.USER_DETAIL_PREFIX)
        except Exception as e:
            logger.error(f"获取用户详情缓存失败: {str(e)}")
            return None
    
    async def set_user_detail(self, user_id: int, user_data: Dict[str, Any], expire: int = None) -> bool:
        """
        缓存用户详情数据
        
        Args:
            user_id: 用户ID
            user_data: 用户详情数据
            expire: 过期时间(秒)，默认30分钟
            
        Returns:
            是否成功缓存
        """
        if not self.enabled:
            return False
            
        try:
            # 用户详情缓存默认30分钟
            expire = expire or 30 * 60
            return await self.cache.set(f"{user_id}", user_data, expire, prefix=self.USER_DETAIL_PREFIX)
        except Exception as e:
            logger.error(f"缓存用户详情失败: {str(e)}")
            return False
    
    async def get_user_roles(self, user_id: int) -> Optional[List[str]]:
        """
        获取缓存的用户角色列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户角色列表，如果缓存未命中则返回None
        """
        if not self.enabled:
            return None
            
        try:
            return await self.cache.get(f"{user_id}", prefix=self.USER_ROLES_PREFIX)
        except Exception as e:
            logger.error(f"获取用户角色缓存失败: {str(e)}")
            return None
    
    async def set_user_roles(self, user_id: int, roles: List[str], expire: int = None) -> bool:
        """
        缓存用户角色列表
        
        Args:
            user_id: 用户ID
            roles: 角色列表
            expire: 过期时间(秒)，默认60分钟
            
        Returns:
            是否成功缓存
        """
        if not self.enabled:
            return False
            
        try:
            # 角色缓存默认60分钟
            expire = expire or 60 * 60
            return await self.cache.set(f"{user_id}", roles, expire, prefix=self.USER_ROLES_PREFIX)
        except Exception as e:
            logger.error(f"缓存用户角色失败: {str(e)}")
            return False
    
    async def get_user_permissions(self, user_id: int) -> Optional[List[str]]:
        """
        获取缓存的用户权限列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户权限列表，如果缓存未命中则返回None
        """
        if not self.enabled:
            return None
            
        try:
            return await self.cache.get(f"{user_id}", prefix=self.USER_PERMS_PREFIX)
        except Exception as e:
            logger.error(f"获取用户权限缓存失败: {str(e)}")
            return None
    
    async def set_user_permissions(self, user_id: int, permissions: List[str], expire: int = None) -> bool:
        """
        缓存用户权限列表
        
        Args:
            user_id: 用户ID
            permissions: 权限列表
            expire: 过期时间(秒)，默认60分钟
            
        Returns:
            是否成功缓存
        """
        if not self.enabled:
            return False
            
        try:
            # 权限缓存默认60分钟
            expire = expire or 60 * 60
            return await self.cache.set(f"{user_id}", permissions, expire, prefix=self.USER_PERMS_PREFIX)
        except Exception as e:
            logger.error(f"缓存用户权限失败: {str(e)}")
            return False
    
    async def cache_user_all_data(self, user: User, expire: int = None) -> bool:
        """
        缓存用户的所有数据（基本信息、角色、权限）
        
        Args:
            user: 用户对象
            expire: 过期时间(秒)，默认30分钟
            
        Returns:
            是否成功缓存
        """
        if not self.enabled:
            return False
            
        try:
            # 默认缓存30分钟
            expire = expire or 30 * 60
            
            # 序列化用户基本信息
            user_dict = {
                "id": user.id,
                "email": user.email,
                "username": user.username,
                "full_name": user.full_name,
                "phone": user.phone,
                "is_active": user.is_active,
                "is_verified": user.is_verified,
                "is_superuser": user.is_superuser,
                "avatar": user.avatar,
                "last_login": user.last_login.isoformat() if user.last_login else None
            }
            
            # 缓存基本信息 - 确保auth_cache不是None
            if self.auth_cache and self.auth_cache.enabled:
                try:
                    result = await self.auth_cache.set_user(user.id, user_dict, expire)
                    if not result:
                        logger.warning(f"缓存用户基本信息失败: user_id={user.id}")
                except Exception as auth_err:
                    logger.error(f"调用auth_cache.set_user时出错: {str(auth_err)}")
            
            # 缓存用户详情
            try:
                result = await self.set_user_detail(user.id, user_dict, expire)
                if not result:
                    logger.warning(f"缓存用户详情失败: user_id={user.id}")
            except Exception as detail_err:
                logger.error(f"调用set_user_detail时出错: {str(detail_err)}")
            
            # 缓存角色
            if hasattr(user, 'roles') and user.roles is not None:
                try:
                    roles = [role.name for role in user.roles]
                    result = await self.set_user_roles(user.id, roles, expire)
                    if not result:
                        logger.warning(f"缓存用户角色失败: user_id={user.id}")
                    
                    # 缓存权限
                    permissions = []
                    for role in user.roles:
                        if hasattr(role, 'permissions') and role.permissions is not None:
                            for perm in role.permissions:
                                if perm.name not in permissions:
                                    permissions.append(perm.name)
                    
                    result = await self.set_user_permissions(user.id, permissions, expire)
                    if not result:
                        logger.warning(f"缓存用户权限失败: user_id={user.id}")
                except Exception as role_err:
                    logger.error(f"缓存用户角色和权限时出错: {str(role_err)}")
            
            return True
        except Exception as e:
            logger.error(f"缓存用户所有数据失败: {str(e)}")
            return False
    
    async def invalidate_user_cache(self, user_id: int) -> bool:
        """
        使用户所有缓存失效
        
        Args:
            user_id: 用户ID
            
        Returns:
            是否成功删除所有缓存
        """
        if not self.enabled:
            return True
            
        try:
            # 删除基本缓存
            if self.auth_cache and self.auth_cache.enabled:
                try:
                    await self.auth_cache.delete_user(user_id)
                except Exception as auth_err:
                    logger.error(f"删除auth_cache用户缓存失败: {str(auth_err)}")
            
            # 删除详情缓存
            try:
                await self.cache.delete(f"{user_id}", prefix=self.USER_DETAIL_PREFIX)
            except Exception as detail_err:
                logger.error(f"删除用户详情缓存失败: {str(detail_err)}")
            
            # 删除角色缓存
            try:
                await self.cache.delete(f"{user_id}", prefix=self.USER_ROLES_PREFIX)
            except Exception as role_err:
                logger.error(f"删除用户角色缓存失败: {str(role_err)}")
            
            # 删除权限缓存
            try:
                await self.cache.delete(f"{user_id}", prefix=self.USER_PERMS_PREFIX)
            except Exception as perm_err:
                logger.error(f"删除用户权限缓存失败: {str(perm_err)}")
            
            # 删除菜单缓存
            try:
                await self.cache.delete(f"{user_id}", prefix=self.USER_MENU_PREFIX)
            except Exception as menu_err:
                logger.error(f"删除用户菜单缓存失败: {str(menu_err)}")
            
            return True
        except Exception as e:
            logger.error(f"使用户缓存失效失败: {str(e)}")
            return False 
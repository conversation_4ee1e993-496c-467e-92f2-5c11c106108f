
"""
文件API认证辅助模块
提供文件API特定的认证工具函数
"""
from fastapi import Depends, HTTPException, status, Request, Header
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
import jwt

from models.user import User
from db.session import get_db
from core.config import settings

async def get_file_api_user(
    authorization: Optional[str] = Header(None, description="Bearer令牌"),
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """
    从Authorization头中获取用户信息，专用于文件API
    
    Args:
        authorization: Authorization头内容
        db: 数据库会话
        
    Returns:
        用户对象或None
        
    Raises:
        HTTPException: 认证失败时
    """
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证令牌",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    try:
        # 解析Authorization头
        scheme, token = authorization.split()
        if scheme.lower() != "bearer":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="认证格式错误，应为Bearer令牌",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # 解码令牌
        try:
            payload = jwt.decode(
                token, 
                settings.JWT_SECRET, 
                algorithms=[settings.JWT_ALGORITHM]
            )
            user_id = payload.get("sub")
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="无效的令牌内容",
                    headers={"WWW-Authenticate": "Bearer"}
                )
        except jwt.PyJWTError as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"无效的令牌: {str(e)}",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # 查询用户
        from sqlalchemy import select
        query = select(User).where(User.id == int(user_id))
        result = await db.execute(query)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="找不到用户",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户已被禁用",
                headers={"WWW-Authenticate": "Bearer"}
            )
            
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"认证处理错误: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"}
        )

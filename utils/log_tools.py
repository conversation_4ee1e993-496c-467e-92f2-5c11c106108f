"""
日志维护工具模块

提供日志文件清理、压缩和分析功能
"""
import os
import glob
import time
import shutil
import zipfile
import datetime
import logging
from pathlib import Path

from core.config import settings

logger = logging.getLogger("app.log_tools")

def clean_old_logs(log_dir=None, days=None, dry_run=False):
    """
    清理超过指定天数的日志文件
    
    Args:
        log_dir: 日志目录，默认使用settings.LOG_DIR
        days: 保留天数，默认使用settings.LOG_BACKUP_COUNT
        dry_run: 是否只模拟执行，不实际删除
        
    Returns:
        删除的文件数量和总大小
    """
    log_dir = log_dir or settings.LOG_DIR
    days = days or settings.LOG_BACKUP_COUNT
    
    if not os.path.exists(log_dir):
        logger.warning(f"日志目录不存在: {log_dir}")
        return 0, 0
    
    now = time.time()
    cutoff = now - (days * 86400)  # 86400秒 = 1天
    deleted_count = 0
    deleted_size = 0
    
    # 查找所有日志文件
    log_files = []
    log_files.extend(glob.glob(os.path.join(log_dir, "*.log")))
    log_files.extend(glob.glob(os.path.join(log_dir, "*.log.*")))
    
    for file_path in log_files:
        # 忽略主日志文件
        if file_path.endswith("app.log") or file_path.endswith("access.log") or file_path.endswith("error.log"):
            continue
        
        file_stat = os.stat(file_path)
        if file_stat.st_mtime < cutoff:
            deleted_size += file_stat.st_size
            if dry_run:
                logger.info(f"[模拟] 将删除日志文件: {file_path}")
            else:
                try:
                    os.remove(file_path)
                    logger.info(f"已删除过期日志文件: {file_path}")
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"删除日志文件失败: {file_path}, 错误: {str(e)}")
    
    if deleted_count > 0 or dry_run:
        size_in_mb = deleted_size / (1024 * 1024)
        action = "将删除" if dry_run else "已删除"
        logger.info(f"{action} {deleted_count} 个日志文件, 共 {size_in_mb:.2f} MB")
    
    return deleted_count, deleted_size

def archive_logs(log_dir=None, days=None, archive_dir=None):
    """
    将旧日志归档为zip文件
    
    Args:
        log_dir: 日志目录，默认使用settings.LOG_DIR
        days: 归档超过指定天数的日志，默认为7天
        archive_dir: 归档目录，默认为log_dir/archives
        
    Returns:
        归档的文件数量和归档文件路径
    """
    log_dir = log_dir or settings.LOG_DIR
    days = days or 7
    archive_dir = archive_dir or os.path.join(log_dir, "archives")
    
    if not os.path.exists(log_dir):
        logger.warning(f"日志目录不存在: {log_dir}")
        return 0, None
    
    # 创建归档目录
    if not os.path.exists(archive_dir):
        os.makedirs(archive_dir)
    
    now = time.time()
    cutoff = now - (days * 86400)
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    archive_file = os.path.join(archive_dir, f"logs_{today}.zip")
    
    archived_count = 0
    
    # 查找要归档的日志文件
    log_files = []
    log_files.extend(glob.glob(os.path.join(log_dir, "*.log.*")))
    
    # 过滤出要归档的文件
    to_archive = []
    for file_path in log_files:
        # 跳过主日志文件
        if file_path.endswith("app.log") or file_path.endswith("access.log") or file_path.endswith("error.log"):
            continue
            
        file_stat = os.stat(file_path)
        if file_stat.st_mtime < cutoff:
            to_archive.append(file_path)
    
    if not to_archive:
        logger.info("没有找到需要归档的日志文件")
        return 0, None
    
    # 创建归档文件
    try:
        with zipfile.ZipFile(archive_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in to_archive:
                arcname = os.path.basename(file_path)
                zipf.write(file_path, arcname)
                archived_count += 1
                
                # 归档后删除原文件
                try:
                    os.remove(file_path)
                except Exception as e:
                    logger.error(f"删除已归档的日志文件失败: {file_path}, 错误: {str(e)}")
        
        logger.info(f"已将 {archived_count} 个日志文件归档到: {archive_file}")
        return archived_count, archive_file
    except Exception as e:
        logger.error(f"创建归档文件失败: {str(e)}")
        return 0, None

def analyze_log_sizes(log_dir=None):
    """
    分析日志目录中所有日志文件的大小
    
    Args:
        log_dir: 日志目录，默认使用settings.LOG_DIR
        
    Returns:
        字典，包含日志统计信息
    """
    log_dir = log_dir or settings.LOG_DIR
    
    if not os.path.exists(log_dir):
        logger.warning(f"日志目录不存在: {log_dir}")
        return {}
    
    # 查找所有日志文件
    log_files = []
    log_files.extend(glob.glob(os.path.join(log_dir, "*.log")))
    log_files.extend(glob.glob(os.path.join(log_dir, "*.log.*")))
    
    stats = {
        "total_size_bytes": 0,
        "total_size_mb": 0,
        "file_count": len(log_files),
        "oldest_file": None,
        "newest_file": None,
        "largest_file": None,
        "largest_file_size": 0,
        "files": []
    }
    
    oldest_time = float('inf')
    newest_time = 0
    
    for file_path in log_files:
        file_stat = os.stat(file_path)
        file_size = file_stat.st_size
        file_mtime = file_stat.st_mtime
        
        # 累计总大小
        stats["total_size_bytes"] += file_size
        
        # 记录文件信息
        file_info = {
            "path": file_path,
            "name": os.path.basename(file_path),
            "size_bytes": file_size,
            "size_mb": file_size / (1024 * 1024),
            "mtime": datetime.datetime.fromtimestamp(file_mtime).strftime("%Y-%m-%d %H:%M:%S")
        }
        stats["files"].append(file_info)
        
        # 最大文件
        if file_size > stats["largest_file_size"]:
            stats["largest_file_size"] = file_size
            stats["largest_file"] = file_path
        
        # 最旧/最新文件
        if file_mtime < oldest_time:
            oldest_time = file_mtime
            stats["oldest_file"] = file_path
        
        if file_mtime > newest_time:
            newest_time = file_mtime
            stats["newest_file"] = file_path
    
    # 总大小转换为MB
    stats["total_size_mb"] = stats["total_size_bytes"] / (1024 * 1024)
    
    return stats

def log_rotate_manually():
    """
    手动执行日志轮转
    
    当日志系统未正常轮转时，可以调用此函数手动执行轮转
    """
    log_dir = settings.LOG_DIR
    
    # 主要日志文件
    main_logs = [
        os.path.join(log_dir, "app.log"),
        os.path.join(log_dir, "access.log"),
        os.path.join(log_dir, "error.log")
    ]
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
    
    for log_file in main_logs:
        if os.path.exists(log_file) and os.path.getsize(log_file) > 0:
            backup_name = f"{log_file}.{timestamp}"
            try:
                # 创建备份
                shutil.copy2(log_file, backup_name)
                
                # 清空原文件
                with open(log_file, 'w') as f:
                    pass
                
                logger.info(f"已手动轮转日志文件: {log_file} -> {backup_name}")
            except Exception as e:
                logger.error(f"手动轮转日志文件失败: {log_file}, 错误: {str(e)}")
    
    # 清理过期日志
    clean_old_logs()

if __name__ == "__main__":
    # 当直接运行脚本时，执行以下操作
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    
    # 分析日志大小
    stats = analyze_log_sizes()
    print(f"当前日志文件数: {stats['file_count']}")
    print(f"日志总大小: {stats['total_size_mb']:.2f} MB")
    if stats.get('largest_file'):
        print(f"最大日志文件: {os.path.basename(stats['largest_file'])} ({stats['largest_file_size'] / (1024*1024):.2f} MB)")
    
    # 执行清理
    cleaned, size = clean_old_logs(dry_run=False)
    print(f"已清理 {cleaned} 个过期日志文件, 共 {size / (1024 * 1024):.2f} MB")
    
    # 执行归档
    archived, archive_path = archive_logs()
    if archived > 0:
        print(f"已归档 {archived} 个日志文件到: {archive_path}")
    else:
        print("没有日志文件需要归档")
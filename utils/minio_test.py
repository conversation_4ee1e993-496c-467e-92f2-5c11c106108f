"""
MinIO连接测试脚本
用于测试MinIO连接和基本操作
"""
import os
import sys
from minio import Minio
from minio.error import S3Error

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath("."))
from core.config import settings

def test_minio_connection():
    """测试MinIO连接是否正常"""
    try:
        # 构建MinIO端点
        endpoint = f"{settings.MINIO_ENDPOINT}:{settings.MINIO_API_PORT}"
        
        # 打印连接信息
        print(f"正在测试MinIO连接...")
        print(f"端点: {endpoint}")
        print(f"访问密钥: {settings.MINIO_ACCESS_KEY}")
        print(f"存储桶: {settings.MINIO_BUCKET}")
        print(f"使用SSL: {settings.MINIO_USE_SSL}")
        
        # 创建MinIO客户端
        client = Minio(
            endpoint=endpoint,
            access_key=settings.MINIO_ACCESS_KEY,
            secret_key=settings.MINIO_SECRET_KEY,
            region=settings.MINIO_REGION,
            secure=settings.MINIO_USE_SSL
        )
        
        # 检查存储桶是否存在
        bucket_exists = client.bucket_exists(settings.MINIO_BUCKET)
        if bucket_exists:
            print(f"存储桶 '{settings.MINIO_BUCKET}' 已存在")
        else:
            print(f"存储桶 '{settings.MINIO_BUCKET}' 不存在，尝试创建...")
            client.make_bucket(settings.MINIO_BUCKET, location=settings.MINIO_REGION)
            print(f"已创建存储桶 '{settings.MINIO_BUCKET}'")
        
        # 测试上传文件
        test_file = "test.txt"
        with open(test_file, "w") as f:
            f.write("这是一个测试文件，用于测试MinIO连接")
        
        # 上传文件
        client.fput_object(
            bucket_name=settings.MINIO_BUCKET,
            object_name="test.txt",
            file_path=test_file,
            content_type="text/plain"
        )
        print(f"已成功上传测试文件到 {settings.MINIO_BUCKET}/test.txt")
        
        # 获取预签名URL
        url = client.presigned_get_object(
            bucket_name=settings.MINIO_BUCKET,
            object_name="test.txt",
        )
        print(f"预签名访问URL: {url}")
        
        print("MinIO连接测试成功!")
        return True
    except S3Error as e:
        print(f"MinIO错误: {e}")
        return False
    except Exception as e:
        print(f"连接测试失败: {e}")
        return False

if __name__ == "__main__":
    test_minio_connection() 
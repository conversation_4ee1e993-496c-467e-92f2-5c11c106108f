import time
import asyncio
import logging
import psutil
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json
import os

logger = logging.getLogger(__name__)

class SystemMetricsCollector:
    """系统指标收集器，用于监控应用运行状态"""
    
    def __init__(
        self, 
        metrics_dir: str = "logs/metrics",
        collection_interval: int = 60,
        retention_days: int = 7,
        include_process_metrics: bool = True
    ):
        """初始化指标收集器
        
        Args:
            metrics_dir: 指标存储目录
            collection_interval: 收集间隔(秒)
            retention_days: 指标保留天数
            include_process_metrics: 是否收集进程指标
        """
        self.metrics_dir = metrics_dir
        self.collection_interval = max(10, collection_interval)
        self.retention_days = max(1, retention_days)
        self.include_process_metrics = include_process_metrics
        self.process = psutil.Process()
        self.is_running = False
        self.collection_task = None
        self.start_time = time.time()
        
        # 确保目录存在
        os.makedirs(metrics_dir, exist_ok=True)
        
    def start(self):
        """启动指标收集"""
        if not self.is_running:
            self.is_running = True
            self.collection_task = asyncio.create_task(self._collect_metrics_task())
            logger.info(f"系统指标收集已启动 (间隔: {self.collection_interval}秒)")
    
    def stop(self):
        """停止指标收集"""
        if self.is_running and self.collection_task:
            self.is_running = False
            self.collection_task.cancel()
            logger.info("系统指标收集已停止")
    
    async def _collect_metrics_task(self):
        """指标收集任务"""
        try:
            while self.is_running:
                try:
                    metrics = self.collect_current_metrics()
                    await self._save_metrics(metrics)
                    
                    # 定期清理旧指标
                    if int(time.time()) % 3600 < self.collection_interval:  # 大约每小时执行一次
                        await self._cleanup_old_metrics()
                except Exception as e:
                    logger.error(f"收集指标时出错: {str(e)}")
                
                await asyncio.sleep(self.collection_interval)
        except asyncio.CancelledError:
            logger.debug("指标收集任务已取消")
    
    def collect_current_metrics(self) -> Dict[str, Any]:
        """收集当前系统指标
        
        Returns:
            包含各项指标的字典
        """
        # 系统指标
        cpu_percent = psutil.cpu_percent(interval=0.5)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "system": {
                "cpu_percent": cpu_percent,
                "memory_total_mb": memory.total / (1024 * 1024),
                "memory_available_mb": memory.available / (1024 * 1024),
                "memory_percent": memory.percent,
                "disk_total_gb": disk.total / (1024 * 1024 * 1024),
                "disk_free_gb": disk.free / (1024 * 1024 * 1024),
                "disk_percent": disk.percent,
            },
            "app": {
                "uptime_seconds": int(time.time() - self.start_time)
            }
        }
        
        # 进程指标(如果启用)
        if self.include_process_metrics:
            try:
                process_metrics = {
                    "cpu_percent": self.process.cpu_percent(interval=0.1),
                    "memory_rss_mb": self.process.memory_info().rss / (1024 * 1024),
                    "memory_vms_mb": self.process.memory_info().vms / (1024 * 1024),
                    "threads": self.process.num_threads(),
                    "open_files": len(self.process.open_files()),
                    "connections": len(self.process.connections())
                }
                metrics["process"] = process_metrics
            except Exception as e:
                logger.warning(f"收集进程指标时出错: {str(e)}")
        
        return metrics
    
    async def _save_metrics(self, metrics: Dict[str, Any]):
        """保存指标到文件
        
        Args:
            metrics: 要保存的指标
        """
        # 使用日期作为文件名
        date_str = datetime.now().strftime("%Y-%m-%d")
        file_path = os.path.join(self.metrics_dir, f"metrics_{date_str}.jsonl")
        
        try:
            # 使用异步IO写入文件
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self._append_to_file(file_path, json.dumps(metrics))
            )
        except Exception as e:
            logger.error(f"保存指标失败: {str(e)}")
    
    def _append_to_file(self, file_path: str, content: str):
        """追加内容到文件
        
        Args:
            file_path: 文件路径
            content: 要追加的内容
        """
        with open(file_path, "a") as f:
            f.write(content + "\n")
    
    async def _cleanup_old_metrics(self):
        """清理旧指标文件"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.retention_days)
            
            # 列出所有指标文件
            loop = asyncio.get_event_loop()
            files = await loop.run_in_executor(
                None,
                lambda: os.listdir(self.metrics_dir)
            )
            
            # 识别和删除旧文件
            for filename in files:
                if not filename.startswith("metrics_"):
                    continue
                    
                try:
                    # 从文件名提取日期
                    date_str = filename.replace("metrics_", "").replace(".jsonl", "")
                    file_date = datetime.strptime(date_str, "%Y-%m-%d")
                    
                    # 如果早于截止日期，删除它
                    if file_date < cutoff_date:
                        file_path = os.path.join(self.metrics_dir, filename)
                        await loop.run_in_executor(None, os.remove, file_path)
                        logger.debug(f"已删除旧指标文件: {filename}")
                except Exception:
                    continue
                    
        except Exception as e:
            logger.error(f"清理旧指标文件时出错: {str(e)}")
    
    async def get_recent_metrics(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取最近的指标数据
        
        Args:
            hours: 获取最近多少小时的数据
            
        Returns:
            指标数据列表
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        results = []
        
        try:
            # 列出所有指标文件
            loop = asyncio.get_event_loop()
            files = await loop.run_in_executor(
                None,
                lambda: sorted(os.listdir(self.metrics_dir))
            )
            
            # 查找相关文件并读取
            for filename in files:
                if not filename.startswith("metrics_"):
                    continue
                    
                file_path = os.path.join(self.metrics_dir, filename)
                
                # 读取文件内容
                content = await loop.run_in_executor(
                    None,
                    lambda: self._read_file(file_path)
                )
                
                # 解析每行
                for line in content.strip().split("\n"):
                    if not line:
                        continue
                        
                    try:
                        metric = json.loads(line)
                        timestamp = datetime.fromisoformat(metric["timestamp"])
                        
                        # 只包含截止时间之后的数据
                        if timestamp >= cutoff_time:
                            results.append(metric)
                    except Exception:
                        continue
            
            # 按时间排序
            results.sort(key=lambda x: x["timestamp"])
            
            return results
        except Exception as e:
            logger.error(f"获取最近指标数据时出错: {str(e)}")
            return []
    
    def _read_file(self, file_path: str) -> str:
        """读取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容
        """
        try:
            with open(file_path, "r") as f:
                return f.read()
        except Exception:
            return ""

# 创建单例实例
metrics_collector = SystemMetricsCollector() 
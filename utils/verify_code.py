from typing import Tuple, Optional
import random
import string
import io
import base64
from datetime import datetime, timedelta
from PIL import Image, ImageDraw, ImageFont, ImageFilter
import logging

from redis.asyncio import Redis
from fastapi import Depends

from core.config import settings
from core.security import generate_random_code
from db.redis import get_redis, RedisCache

# 日志配置
logger = logging.getLogger(__name__)

class VerifyCodeManager:
    """验证码管理器，用于生成和验证各类验证码"""
    
    def __init__(self):
        redis = redis
        self.cache = RedisCache(redis)
        self.prefix = settings.REDIS_VERIFY_CODE_PREFIX
    
    async def generate_image_code(
        self, 
        key: str,
        length: int = 4,
        width: int = 120,
        height: int = 36,
        expire_minutes: int = 5
    ) -> Tuple[str, str]:
        """
        生成图形验证码
        
        Args:
            key: 验证码键，用于存储和验证
            length: 验证码长度
            width: 图片宽度
            height: 图片高度
            expire_minutes: 过期时间(分钟)
            
        Returns:
            (code, base64_image): 验证码和Base64编码的图片
        """
        # 生成随机验证码
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
        
        # 创建图片
        image = Image.new('RGB', (width, height), (255, 255, 255))
        draw = ImageDraw.Draw(image)
        
        # 绘制干扰线
        for i in range(5):
            start = (random.randint(0, width), random.randint(0, height))
            end = (random.randint(0, width), random.randint(0, height))
            draw.line([start, end], fill=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))
        
        # 绘制干扰点
        for i in range(50):
            draw.point((random.randint(0, width), random.randint(0, height)), fill=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))
        
        # 绘制验证码
        # 尝试加载自定义字体，如果失败则使用默认字体
        try:
            font = ImageFont.truetype('arial.ttf', 22)
        except IOError:
            try:
                # 尝试其他常见字体
                font = ImageFont.truetype('DejaVuSans.ttf', 22)
            except IOError:
                # 使用默认字体
                font = ImageFont.load_default()
        
        # 计算验证码文字位置
        text_width = width / length
        for i, char in enumerate(code):
            # 随机颜色和位置
            color = (random.randint(0, 100), random.randint(0, 100), random.randint(0, 100))
            position = (text_width * i + 10, (height - 22) / 2 + random.randint(-5, 5))
            draw.text(position, char, font=font, fill=color)
        
        # 应用滤镜
        image = image.filter(ImageFilter.BLUR)
        
        # 转换为Base64
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        base64_image = base64.b64encode(buffer.getvalue()).decode()
        
        # 存储验证码
        await self.cache.set(
            key=key,
            value=code.lower(),  # 统一使用小写存储
            expire=expire_minutes * 60,
            prefix=self.prefix
        )
        
        return code, f"data:image/png;base64,{base64_image}"
    
    async def generate_sms_code(
        self,
        phone: str,
        length: int = 6,
        expire_minutes: int = None
    ) -> str:
        """
        生成短信验证码
        
        Args:
            phone: 手机号
            length: 验证码长度
            expire_minutes: 过期时间(分钟)，默认使用配置
            
        Returns:
            验证码
        """
        # 生成随机验证码
        code = generate_random_code(length)
        
        # 默认过期时间
        if expire_minutes is None:
            expire_minutes = settings.SMS_CODE_EXPIRE_MINUTES
        
        # 存储验证码
        key = f"sms:{phone}"
        await self.cache.set(
            key=key,
            value=code,
            expire=expire_minutes * 60,
            prefix=self.prefix
        )
        
        return code
    
    async def verify_code(
        self,
        key: str,
        code: str,
        delete_on_success: bool = True
    ) -> bool:
        """
        验证验证码
        
        Args:
            key: 验证码键
            code: 要验证的验证码
            delete_on_success: 验证成功后是否删除验证码
            
        Returns:
            验证是否成功
        """
        if not code:
            return False
        
        # 获取存储的验证码
        stored_code = await self.cache.get(key, prefix=self.prefix)
        
        if not stored_code:
            return False
        
        # 比较验证码（不区分大小写）
        is_valid = stored_code.lower() == code.lower()
        
        # 验证成功后删除
        if is_valid and delete_on_success:
            await self.cache.delete(key, prefix=self.prefix)
        
        return is_valid
    
    async def verify_sms_code(
        self,
        phone: str,
        code: str,
        delete_on_success: bool = True
    ) -> bool:
        """
        验证短信验证码
        
        Args:
            phone: 手机号
            code: 要验证的验证码
            delete_on_success: 验证成功后是否删除验证码
            
        Returns:
            验证是否成功
        """
        key = f"sms:{phone}"
        return await self.verify_code(key, code, delete_on_success)
    
    async def delete_code(self, key: str) -> bool:
        """
        删除验证码
        
        Args:
            key: 验证码键
            
        Returns:
            是否成功删除
        """
        deleted = await self.cache.delete(key, prefix=self.prefix)
        return deleted > 0

async def get_verify_code_manager(# redis: Redis = Depends(get_redis) - 已在函数内部处理) -> VerifyCodeManager:
    """
    获取验证码管理器的依赖函数
    
    使用方法:
    ```
    @router.get("/captcha")
    async def get_captcha(
        verify_code: VerifyCodeManager = Depends(get_verify_code_manager)
    ):
        key = f"captcha:{uuid.uuid4()}"
        code, image = await verify_code.generate_image_code(key)
        return {"key": key, "image": image}
    ```
    """
    return VerifyCodeManager(redis) :

    
        """
    
        """
    
        self.redis = redis
        self.cache = RedisCache(redis)
        self.prefix = settings.REDIS_VERIFY_CODE_PREFIX
    
    async def generate_image_code(
        self, 
        key: str,
        length: int = 4,
        width: int = 120,
        height: int = 36,
        expire_minutes: int = 5
    ) -> Tuple[str, str]:
        """
        生成图形验证码
        
        Args:
            key: 验证码键，用于存储和验证
            length: 验证码长度
            width: 图片宽度
            height: 图片高度
            expire_minutes: 过期时间(分钟)
            
        Returns:
            (code, base64_image): 验证码和Base64编码的图片
        """
        # 生成随机验证码
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
        
        # 创建图片
        image = Image.new('RGB', (width, height), (255, 255, 255))
        draw = ImageDraw.Draw(image)
        
        # 绘制干扰线
        for i in range(5):
            start = (random.randint(0, width), random.randint(0, height))
            end = (random.randint(0, width), random.randint(0, height))
            draw.line([start, end], fill=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))
        
        # 绘制干扰点
        for i in range(50):
            draw.point((random.randint(0, width), random.randint(0, height)), fill=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))
        
        # 绘制验证码
        # 尝试加载自定义字体，如果失败则使用默认字体
        try:
            font = ImageFont.truetype('arial.ttf', 22)
        except IOError:
            try:
                # 尝试其他常见字体
                font = ImageFont.truetype('DejaVuSans.ttf', 22)
            except IOError:
                # 使用默认字体
                font = ImageFont.load_default()
        
        # 计算验证码文字位置
        text_width = width / length
        for i, char in enumerate(code):
            # 随机颜色和位置
            color = (random.randint(0, 100), random.randint(0, 100), random.randint(0, 100))
            position = (text_width * i + 10, (height - 22) / 2 + random.randint(-5, 5))
            draw.text(position, char, font=font, fill=color)
        
        # 应用滤镜
        image = image.filter(ImageFilter.BLUR)
        
        # 转换为Base64
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        base64_image = base64.b64encode(buffer.getvalue()).decode()
        
        # 存储验证码
        await self.cache.set(
            key=key,
            value=code.lower(),  # 统一使用小写存储
            expire=expire_minutes * 60,
            prefix=self.prefix
        )
        
        return code, f"data:image/png;base64,{base64_image}"
    
    async def generate_sms_code(
        self,
        phone: str,
        length: int = 6,
        expire_minutes: int = None
    ) -> str:
        """
        生成短信验证码
        
        Args:
            phone: 手机号
            length: 验证码长度
            expire_minutes: 过期时间(分钟)，默认使用配置
            
        Returns:
            验证码
        """
        # 生成随机验证码
        code = generate_random_code(length)
        
        # 默认过期时间
        if expire_minutes is None:
            expire_minutes = settings.SMS_CODE_EXPIRE_MINUTES
        
        # 存储验证码
        key = f"sms:{phone}"
        await self.cache.set(
            key=key,
            value=code,
            expire=expire_minutes * 60,
            prefix=self.prefix
        )
        
        return code
    
    async def verify_code(
        self,
        key: str,
        code: str,
        delete_on_success: bool = True
    ) -> bool:
        """
        验证验证码
        
        Args:
            key: 验证码键
            code: 要验证的验证码
            delete_on_success: 验证成功后是否删除验证码
            
        Returns:
            验证是否成功
        """
        if not code:
            return False
        
        # 获取存储的验证码
        stored_code = await self.cache.get(key, prefix=self.prefix)
        
        if not stored_code:
            return False
        
        # 比较验证码（不区分大小写）
        is_valid = stored_code.lower() == code.lower()
        
        # 验证成功后删除
        if is_valid and delete_on_success:
            await self.cache.delete(key, prefix=self.prefix)
        
        return is_valid
    
    async def verify_sms_code(
        self,
        phone: str,
        code: str,
        delete_on_success: bool = True
    ) -> bool:
        """
        验证短信验证码
        
        Args:
            phone: 手机号
            code: 要验证的验证码
            delete_on_success: 验证成功后是否删除验证码
            
        Returns:
            验证是否成功
        """
        key = f"sms:{phone}"
        return await self.verify_code(key, code, delete_on_success)
    
    async def delete_code(self, key: str) -> bool:
        """
        删除验证码
        
        Args:
            key: 验证码键
            
        Returns:
            是否成功删除
        """
        deleted = await self.cache.delete(key, prefix=self.prefix)
        return deleted > 0

async def get_verify_code_manager(# redis: Redis = Depends(get_redis) - 已在函数内部处理) -> VerifyCodeManager:
    """
    获取验证码管理器的依赖函数
    
    使用方法:
    ```
    @router.get("/captcha")
    async def get_captcha(
        verify_code: VerifyCodeManager = Depends(get_verify_code_manager)
    ):
        key = f"captcha:{uuid.uuid4()}"
        code, image = await verify_code.generate_image_code(key)
        return {"key": key, "image": image}
    ```
    """
    return VerifyCodeManager(redis) 
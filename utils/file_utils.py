"""
文件处理工具
"""
import os
import shutil
import asyncio
import logging
from typing import BinaryIO, Optional
from pathlib import Path
from fastapi import UploadFile

# 配置日志
logger = logging.getLogger(__name__)

async def save_upload_file_temp(upload_file: UploadFile, dest_path: str) -> str:
    """
    异步保存上传的文件到指定路径
    
    Args:
        upload_file: FastAPI上传文件对象
        dest_path: 目标保存路径
        
    Returns:
        保存文件的路径
    """
    try:
        # 确保目标目录存在
        os.makedirs(os.path.dirname(os.path.abspath(dest_path)), exist_ok=True)
        
        # 使用异步方式写入文件
        async def _save_file_helper():
            with open(dest_path, "wb") as buffer:
                # 读取文件内容并写入，每次4MB
                chunk_size = 4 * 1024 * 1024  # 4MB
                
                while True:
                    chunk = await upload_file.read(chunk_size)
                    if not chunk:
                        break
                    buffer.write(chunk)
        
        await _save_file_helper()
        return dest_path
    
    except Exception as e:
        logger.error(f"保存上传文件失败: {str(e)}")
        raise

def create_temp_dir(base_dir: Optional[str] = None) -> str:
    """
    创建临时目录
    
    Args:
        base_dir: 基础目录，如果为None则使用系统临时目录
        
    Returns:
        创建的临时目录路径
    """
    import tempfile
    
    if base_dir:
        os.makedirs(base_dir, exist_ok=True)
        temp_dir = tempfile.mkdtemp(dir=base_dir)
    else:
        temp_dir = tempfile.mkdtemp()
    
    return temp_dir

def get_file_size(file_path: str) -> int:
    """
    获取文件大小
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件大小（字节）
    """
    return os.path.getsize(file_path)

def get_mime_type(file_path: str) -> str:
    """
    获取文件MIME类型
    
    Args:
        file_path: 文件路径
        
    Returns:
        MIME类型
    """
    import mimetypes
    mime_type, _ = mimetypes.guess_type(file_path)
    return mime_type or "application/octet-stream" 
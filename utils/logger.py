"""
日志配置模块

提供统一的日志配置，支持控制台输出和文件输出，
并支持日志文件轮转、大小限制和保留时间限制。
"""
import os
import logging
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
import time
import datetime
from pathlib import Path

from core.config import settings

# 创建日志目录
def ensure_log_dir(log_dir=None):
    """确保日志目录存在"""
    log_dir = log_dir or settings.LOG_DIR
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    return log_dir

# 日志文件路径
LOG_DIR = ensure_log_dir()
APP_LOG_FILE = os.path.join(LOG_DIR, "app.log")
ACCESS_LOG_FILE = os.path.join(LOG_DIR, "access.log")
ERROR_LOG_FILE = os.path.join(LOG_DIR, "error.log")

# 日志格式
DETAILED_FORMAT = "%(asctime)s - %(levelname)s - %(name)s - [%(filename)s:%(lineno)d] - %(message)s"
SIMPLE_FORMAT = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
ACCESS_FORMAT = "%(asctime)s - %(levelname)s - [%(client_addr)s] - %(request)s - %(status_code)d - %(message)s"

# 日志大小限制和备份数
MAX_BYTES = settings.LOG_MAX_SIZE_MB * 1024 * 1024  # 将MB转换为字节
BACKUP_COUNT = settings.LOG_BACKUP_COUNT

def get_logger(name="app", level=None):
    """
    获取配置好的日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别，如果为None则从settings获取
        
    Returns:
        配置好的日志记录器
    """
    # 使用settings中的日志级别
    if level is None:
        level = getattr(logging, settings.LOG_LEVEL)
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 清除现有处理器
    if logger.handlers:
        logger.handlers.clear()
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(SIMPLE_FORMAT))
    logger.addHandler(console_handler)
    
    # 文件处理器 - 按大小轮转
    file_handler = RotatingFileHandler(
        APP_LOG_FILE,
        mode="a",
        maxBytes=MAX_BYTES,
        backupCount=BACKUP_COUNT,
        encoding="utf-8"
    )
    file_handler.setFormatter(logging.Formatter(DETAILED_FORMAT))
    logger.addHandler(file_handler)
    
    # 文件处理器 - 按时间轮转（每天轮转）
    time_handler = TimedRotatingFileHandler(
        APP_LOG_FILE + ".daily",
        when="midnight",
        interval=1,  # 每天
        backupCount=BACKUP_COUNT,
        encoding="utf-8",
        atTime=datetime.time(0, 0, 0)  # 午夜
    )
    time_handler.setFormatter(logging.Formatter(DETAILED_FORMAT))
    logger.addHandler(time_handler)
    
    # 错误日志单独记录
    error_handler = RotatingFileHandler(
        ERROR_LOG_FILE,
        mode="a",
        maxBytes=MAX_BYTES,
        backupCount=BACKUP_COUNT,
        encoding="utf-8"
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(logging.Formatter(DETAILED_FORMAT))
    logger.addHandler(error_handler)
    
    return logger

def get_access_logger():
    """
    获取访问日志记录器
    
    Returns:
        配置好的访问日志记录器
    """
    logger = logging.getLogger("access")
    logger.setLevel(logging.INFO)
    
    # 清除现有处理器
    if logger.handlers:
        logger.handlers.clear()
    
    # 文件处理器 - 按大小轮转
    file_handler = RotatingFileHandler(
        ACCESS_LOG_FILE,
        mode="a",
        maxBytes=MAX_BYTES,
        backupCount=BACKUP_COUNT,
        encoding="utf-8"
    )
    file_handler.setFormatter(logging.Formatter(ACCESS_FORMAT))
    logger.addHandler(file_handler)
    
    return logger

def init_logging():
    """
    初始化全局日志配置
    """
    # 设置根日志记录器的级别
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL))
    
    # 配置第三方库的日志级别
    # SQLAlchemy日志
    logging.getLogger("sqlalchemy.engine").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)
    
    # 设置其他库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    
    # 返回应用主日志记录器
    return get_logger("app")

# 导出应用主日志记录器
app_logger = init_logging()
access_logger = get_access_logger() 
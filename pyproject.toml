[project]
name = "admin-app"
version = "0.1.0"
description = "Admin server application core"
authors = [
    {name = "Your Name",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10,<3.12"
dependencies = [
    # 数据处理
    "polars>=0.19.13",            # 高性能数据处理
    "pyarrow>=14.0.1",            # 数据交换格式
    "pandas>=2.2.0",              # 兼容性和数据处理
    
    # 文件处理
    "openpyxl>=3.1.2",            # Excel文件处理
    "xlsxwriter>=3.1.0",          # Excel文件导出
    "python-multipart>=0.0.6",    # 文件上传支持
    
    # 数据库连接
    "connectorx>=0.3.2",          # 数据库连接
    "sqlalchemy>=2.0.0",          # ORM
    "alembic>=1.12.0",            # 数据库迁移
    "asyncpg>=0.28.0",            # PostgreSQL异步驱动
    
    # Web框架
    "fastapi>=0.100.0",           # API框架
    "uvicorn>=0.24.0",            # ASGI服务器
    "starlette>=0.27.0",          # Web框架基础
    "pydantic>=2.0.0",            # 数据验证
    
    # 认证和安全
    "python-jose>=3.3.0",         # JWT
    "passlib>=1.7.4",             # 密码哈希
    "bcrypt>=4.0.1",              # 加密
    
    # 工具
    "httpx>=0.24.0",              # HTTP客户端
    "loguru>=0.7.0",              # 日志
    "orjson>=3.9.0",              # 快速JSON
    "cachetools>=5.3.2",          # 内存缓存工具
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

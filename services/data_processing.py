"""
大型数据处理服务 - 使用Polars高性能数据处理库
"""
import os
import logging
import asyncio
import hashlib
from typing import Any, Dict, List, Optional, Union, Callable, Tuple
from datetime import datetime
from functools import partial
import polars as pl
import psutil
from pathlib import Path
import tempfile

from core.config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 支持的文件类型
FILE_EXTENSIONS = {
    ".csv": "csv",
    ".json": "json",
    ".parquet": "parquet",
    ".xlsx": "excel",
    ".xls": "excel",
    ".feather": "feather",
    ".arrow": "arrow",
}

# 临时文件目录
TEMP_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "temp")
os.makedirs(TEMP_DIR, exist_ok=True)

# 处理状态缓存
processing_status = {}

class MemoryUsageError(Exception):
    """内存使用超过限制时抛出的异常"""
    pass

class DataProcessor:
    """
    大型数据处理服务，使用Polars进行高效数据处理
    """
    
    def __init__(self, max_memory_usage: Optional[int] = None):
        """
        初始化数据处理器
        
        Args:
            max_memory_usage: 最大内存使用量（字节），None表示不限制，默认为系统总内存的80%
        """
        if max_memory_usage is None:
            # 默认使用系统总内存的80%作为限制
            system_memory = psutil.virtual_memory().total
            max_memory_usage = int(system_memory * 0.8)
            
        self.max_memory_usage = max_memory_usage
        # 配置Polars使用的线程数，默认使用所有可用CPU核心
        self.n_threads = os.cpu_count()
        pl.Config.set_tbl_width_chars(160)  # 设置表格显示宽度
        pl.Config.set_fmt_str_lengths(80)   # 设置字符串显示长度

    def _check_memory_usage(self, file_size: int = 0):
        """
        检查当前内存使用情况，如果超过限制则抛出异常
        
        Args:
            file_size: 预计将要处理的文件大小
            
        Raises:
            MemoryUsageError: 如果内存使用超过限制
        """
        # 获取当前内存使用量
        current_usage = psutil.Process(os.getpid()).memory_info().rss
        
        # 估计处理后的内存使用量（保守估计为文件大小的3倍）
        estimated_usage = current_usage + (file_size * 3)
        
        if estimated_usage > self.max_memory_usage:
            raise MemoryUsageError(
                f"预计内存使用量({estimated_usage / 1024 / 1024:.2f} MB)超过限制({self.max_memory_usage / 1024 / 1024:.2f} MB)"
            )
            
    def _create_unique_id(self, file_path: str) -> str:
        """
        为处理任务创建唯一ID
        
        Args:
            file_path: 文件路径
            
        Returns:
            唯一任务ID
        """
        # 使用文件路径和时间戳创建唯一ID
        hash_input = f"{file_path}_{datetime.now().timestamp()}"
        return hashlib.md5(hash_input.encode()).hexdigest()
            
    async def load_data(
        self, 
        file_path: str, 
        file_type: Optional[str] = None,
        use_lazy: bool = True,
        task_id: Optional[str] = None,
        **kwargs
    ) -> Union[pl.DataFrame, pl.LazyFrame]:
        """
        异步加载数据文件
        
        Args:
            file_path: 文件路径
            file_type: 文件类型（如csv, json）, 如果为None则从文件扩展名推断
            use_lazy: 是否使用LazyFrame以减少内存使用
            task_id: 任务ID，用于记录进度
            **kwargs: 传递给polars读取函数的额外参数
            
        Returns:
            DataFrame或LazyFrame
        """
        if task_id:
            processing_status[task_id] = {"status": "loading", "progress": 0}
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        # 检查内存使用
        file_size = os.path.getsize(file_path)
        self._check_memory_usage(file_size)
        
        # 推断文件类型
        if file_type is None:
            ext = os.path.splitext(file_path)[1].lower()
            if ext not in FILE_EXTENSIONS:
                raise ValueError(f"不支持的文件扩展名: {ext}")
            file_type = FILE_EXTENSIONS[ext]
        
        # 创建一个同步函数，然后在事件循环的线程池中执行
        def load_sync():
            try:
                # 设置多线程读取，提高性能
                if 'n_threads' not in kwargs:
                    kwargs['n_threads'] = self.n_threads
                
                if file_type == "csv":
                    reader = pl.read_csv if not use_lazy else pl.scan_csv
                    return reader(file_path, **kwargs)
                elif file_type == "json":
                    reader = pl.read_json if not use_lazy else pl.scan_json
                    return reader(file_path, **kwargs)
                elif file_type == "parquet":
                    reader = pl.read_parquet if not use_lazy else pl.scan_parquet
                    return reader(file_path, **kwargs)
                elif file_type == "excel":
                    # Excel只支持eager模式
                    if "engine" not in kwargs:
                        kwargs["engine"] = "openpyxl"  # 默认使用openpyxl引擎
                    df = pl.read_excel(file_path, **kwargs)
                    return pl.scan_from_pandas(df) if use_lazy else df
                elif file_type == "feather":
                    reader = pl.read_ipc if not use_lazy else pl.scan_ipc
                    return reader(file_path, **kwargs)
                elif file_type == "arrow":
                    reader = pl.read_ipc if not use_lazy else pl.scan_ipc
                    return reader(file_path, **kwargs)
                else:
                    raise ValueError(f"不支持的文件类型: {file_type}")
            except Exception as e:
                logger.error(f"加载文件 {file_path} 失败: {str(e)}")
                raise
        
        # 使用事件循环的线程池执行IO密集型任务
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(None, load_sync)
        
        if task_id:
            processing_status[task_id] = {"status": "loaded", "progress": 100}
            
        return result
    
    async def process_data(
        self,
        df: Union[pl.DataFrame, pl.LazyFrame],
        operations: List[Dict[str, Any]],
        task_id: Optional[str] = None,
        checkpoint: bool = True,
    ) -> Union[pl.DataFrame, pl.LazyFrame]:
        """
        异步处理数据
        
        Args:
            df: 输入DataFrame或LazyFrame
            operations: 操作列表，每个操作是一个字典，包含操作类型和参数
            task_id: 任务ID，用于记录进度
            checkpoint: 是否在复杂操作后创建checkpoint文件，减少内存使用
            
        Returns:
            处理后的DataFrame或LazyFrame
        """
        if task_id:
            processing_status[task_id] = {"status": "processing", "progress": 0}
            
        # 确定是懒模式还是急切模式
        is_lazy = isinstance(df, pl.LazyFrame)
        result = df
        
        # 操作总数
        total_ops = len(operations)
        
        # 在线程池中执行处理逻辑
        def process_sync():
            nonlocal result
            
            for i, op in enumerate(operations):
                op_type = op.get("type")
                params = op.get("params", {})
                
                try:
                    if op_type == "select":
                        # 选择列
                        columns = params.get("columns", [])
                        if columns:
                            result = result.select(columns)
                    
                    elif op_type == "filter":
                        # 过滤行
                        filter_params = params.get("filter", {})
                        if filter_params:
                            expr = self._build_filter_expression(filter_params)
                            result = result.filter(expr)
                    
                    elif op_type == "sort":
                        # 排序
                        by = params.get("by", [])
                        descending = params.get("descending", False)
                        if by:
                            result = result.sort(by, descending=descending)
                    
                    elif op_type == "group_by":
                        # 分组聚合
                        by = params.get("by", [])
                        aggs = params.get("aggs", {})
                        
                        if by and aggs:
                            agg_exprs = []
                            for col, agg_type in aggs.items():
                                if agg_type == "sum":
                                    agg_exprs.append(pl.col(col).sum().alias(f"sum_{col}"))
                                elif agg_type == "mean":
                                    agg_exprs.append(pl.col(col).mean().alias(f"avg_{col}"))
                                elif agg_type == "count":
                                    agg_exprs.append(pl.col(col).count().alias(f"count_{col}"))
                                elif agg_type == "min":
                                    agg_exprs.append(pl.col(col).min().alias(f"min_{col}"))
                                elif agg_type == "max":
                                    agg_exprs.append(pl.col(col).max().alias(f"max_{col}"))
                                
                            result = result.group_by(by).agg(agg_exprs)
                    
                    elif op_type == "join":
                        # 联接操作需要另一个DataFrame
                        right_df_path = params.get("right_df")
                        on = params.get("on", [])
                        how = params.get("how", "inner")
                        
                        if right_df_path and on:
                            # 加载右侧DataFrame
                            file_type = None
                            ext = os.path.splitext(right_df_path)[1].lower()
                            if ext in FILE_EXTENSIONS:
                                file_type = FILE_EXTENSIONS[ext]
                                
                            # 根据文件类型加载
                            if file_type == "csv":
                                right_df = pl.scan_csv(right_df_path) if is_lazy else pl.read_csv(right_df_path)
                            elif file_type == "parquet":
                                right_df = pl.scan_parquet(right_df_path) if is_lazy else pl.read_parquet(right_df_path)
                            else:
                                # 默认尝试parquet格式
                                try:
                                    right_df = pl.scan_parquet(right_df_path) if is_lazy else pl.read_parquet(right_df_path)
                                except:
                                    right_df = pl.scan_csv(right_df_path) if is_lazy else pl.read_csv(right_df_path)
                                
                            # 执行联接操作
                            result = result.join(right_df, on=on, how=how)
                    
                    elif op_type == "with_column":
                        # 添加派生列
                        name = params.get("name")
                        expr = params.get("expr")
                        
                        if name and expr:
                            # 将字符串表达式转换为Polars表达式
                            if expr.startswith("col("):
                                col_name = expr[4:-1].strip("'\"")
                                result = result.with_column(pl.col(col_name).alias(name))
                            elif "+" in expr or "-" in expr or "*" in expr or "/" in expr:
                                # 简单算术表达式
                                try:
                                    # 将表达式分解为操作数和操作符
                                    parts = []
                                    current = ""
                                    for char in expr:
                                        if char in ['+', '-', '*', '/']:
                                            if current:
                                                parts.append(current.strip())
                                                current = ""
                                            parts.append(char)
                                        else:
                                            current += char
                                    if current:
                                        parts.append(current.strip())
                                    
                                    # 构建表达式
                                    pl_expr = None
                                    op = None
                                    for part in parts:
                                        if part in ['+', '-', '*', '/']:
                                            op = part
                                        else:
                                            try:
                                                # 尝试解析为数字
                                                value = float(part)
                                                if pl_expr is None:
                                                    pl_expr = pl.lit(value)
                                                else:
                                                    if op == '+':
                                                        pl_expr = pl_expr + value
                                                    elif op == '-':
                                                        pl_expr = pl_expr - value
                                                    elif op == '*':
                                                        pl_expr = pl_expr * value
                                                    elif op == '/':
                                                        pl_expr = pl_expr / value
                                            except ValueError:
                                                # 解析为列名
                                                if pl_expr is None:
                                                    pl_expr = pl.col(part)
                                                else:
                                                    if op == '+':
                                                        pl_expr = pl_expr + pl.col(part)
                                                    elif op == '-':
                                                        pl_expr = pl_expr - pl.col(part)
                                                    elif op == '*':
                                                        pl_expr = pl_expr * pl.col(part)
                                                    elif op == '/':
                                                        pl_expr = pl_expr / pl.col(part)
                                    
                                    if pl_expr is not None:
                                        result = result.with_column(pl_expr.alias(name))
                                except Exception as e:
                                    logger.warning(f"解析表达式 '{expr}' 失败: {str(e)}")
                    
                    elif op_type == "explode":
                        # 展开数组列
                        columns = params.get("columns", [])
                        if columns:
                            result = result.explode(columns)
                    
                    elif op_type == "limit":
                        # 限制行数
                        n = params.get("n", 1000)
                        result = result.limit(n)
                        
                    elif op_type == "collect":
                        # 强制计算（仅适用于LazyFrame）
                        if is_lazy:
                            result = result.collect(streaming=True)
                            is_lazy = False
                            
                    elif op_type == "checkpoint":
                        # 创建检查点，将中间结果写入临时文件，然后重新加载
                        # 这有助于减少内存使用
                        if checkpoint and is_lazy:
                            # 生成临时文件路径
                            temp_file = tempfile.NamedTemporaryFile(
                                delete=False, suffix=".parquet", dir=TEMP_DIR
                            )
                            try:
                                # 收集并写入
                                (result.collect(streaming=True)
                                 .write_parquet(temp_file.name))
                                # 重新加载
                                result = pl.scan_parquet(temp_file.name)
                            finally:
                                # 确保关闭文件但不删除
                                temp_file.close()
                    
                    # 更新进度
                    if task_id:
                        progress = int((i + 1) / total_ops * 100)
                        processing_status[task_id] = {"status": "processing", "progress": progress}
                
                except Exception as e:
                    logger.error(f"执行操作 {op_type} 失败: {str(e)}")
                    if task_id:
                        processing_status[task_id] = {"status": "error", "message": str(e)}
                    raise
            
            return result
        
        # 使用事件循环的线程池执行CPU密集型任务
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(None, process_sync)
        
        if task_id:
            processing_status[task_id] = {"status": "completed", "progress": 100}
            
        return result
    
    def _build_filter_expression(self, filter_params: Dict[str, Any]) -> pl.Expr:
        """
        构建过滤表达式
        
        Args:
            filter_params: 过滤参数
            
        Returns:
            Polars表达式
        """
        column = filter_params.get("column")
        operator = filter_params.get("operator", "eq")
        value = filter_params.get("value")
        
        if not column:
            raise ValueError("必须指定列名")
        
        # 创建基础列表达式
        col_expr = pl.col(column)
        
        # 应用操作符
        if operator == "eq":
            return col_expr == value
        elif operator == "neq":
            return col_expr != value
        elif operator == "gt":
            return col_expr > value
        elif operator == "gte":
            return col_expr >= value
        elif operator == "lt":
            return col_expr < value
        elif operator == "lte":
            return col_expr <= value
        elif operator == "contains":
            return col_expr.str.contains(value)
        elif operator == "starts_with":
            return col_expr.str.starts_with(value)
        elif operator == "ends_with":
            return col_expr.str.ends_with(value)
        elif operator == "is_null":
            return col_expr.is_null()
        elif operator == "is_not_null":
            return col_expr.is_not_null()
        elif operator == "in":
            return col_expr.is_in(value)
        else:
            raise ValueError(f"不支持的操作符: {operator}")
    
    async def save_data(
        self,
        df: Union[pl.DataFrame, pl.LazyFrame],
        file_path: str,
        file_type: Optional[str] = None,
        task_id: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        异步保存数据
        
        Args:
            df: 要保存的DataFrame或LazyFrame
            file_path: 保存路径
            file_type: 文件类型，如果为None则从文件扩展名推断
            task_id: 任务ID，用于记录进度
            **kwargs: 传递给polars写入函数的额外参数
            
        Returns:
            保存的文件路径
        """
        if task_id:
            processing_status[task_id] = {"status": "saving", "progress": 0}
            
        # 推断文件类型
        if file_type is None:
            ext = os.path.splitext(file_path)[1].lower()
            if ext not in FILE_EXTENSIONS:
                raise ValueError(f"不支持的文件扩展名: {ext}")
            file_type = FILE_EXTENSIONS[ext]
            
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
        
        # 如果是LazyFrame，先收集
        df_to_save = df
        if isinstance(df, pl.LazyFrame):
            df_to_save = df.collect(streaming=True)
        
        # 创建一个同步函数，然后在事件循环的线程池中执行
        def save_sync():
            try:
                # 设置多线程写入，提高性能
                if 'n_threads' not in kwargs:
                    kwargs['n_threads'] = self.n_threads
                    
                if file_type == "csv":
                    df_to_save.write_csv(file_path, **kwargs)
                elif file_type == "json":
                    df_to_save.write_json(file_path, **kwargs)
                elif file_type == "parquet":
                    df_to_save.write_parquet(file_path, **kwargs)
                elif file_type == "excel":
                    # Polars 0.19.13+支持直接写入Excel
                    df_to_save.write_excel(file_path, **kwargs)
                elif file_type == "feather":
                    df_to_save.write_ipc(file_path, **kwargs)
                elif file_type == "arrow":
                    df_to_save.write_ipc(file_path, **kwargs)
                else:
                    raise ValueError(f"不支持的文件类型: {file_type}")
                
                return file_path
            except Exception as e:
                logger.error(f"保存文件 {file_path} 失败: {str(e)}")
                raise
        
        # 使用事件循环的线程池执行IO密集型任务
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(None, save_sync)
        
        if task_id:
            processing_status[task_id] = {"status": "saved", "progress": 100}
            
        return result
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        if task_id not in processing_status:
            return {"status": "not_found"}
            
        return processing_status[task_id]
    
    async def get_summary_stats(self, df: Union[pl.DataFrame, pl.LazyFrame]) -> Dict[str, Any]:
        """
        获取数据摘要统计信息
        
        Args:
            df: DataFrame或LazyFrame
            
        Returns:
            摘要统计信息
        """
        def get_stats_sync():
            # 如果是LazyFrame，先收集
            if isinstance(df, pl.LazyFrame):
                # 只取前1000行进行统计，避免OOM
                df_stats = df.limit(1000).collect()
            else:
                # 如果DataFrame很大，也只取前1000行
                df_stats = df.head(1000)
            
            result = {
                "row_count": len(df_stats),
                "column_count": len(df_stats.columns),
                "columns": df_stats.columns,
                "dtypes": {col: str(dtype) for col, dtype in zip(df_stats.columns, df_stats.dtypes)},
                "memory_usage": df_stats.estimated_size(),
                "schema": str(df_stats.schema),
            }
            
            # 对数值列计算统计信息
            numeric_stats = {}
            for col in df_stats.columns:
                if df_stats[col].dtype in [pl.Int64, pl.Int32, pl.Float64, pl.Float32]:
                    try:
                        stats = df_stats.select([
                            pl.col(col).min().alias("min"),
                            pl.col(col).max().alias("max"),
                            pl.col(col).mean().alias("mean"),
                            pl.col(col).median().alias("median"),
                            pl.col(col).std().alias("std"),
                            pl.col(col).null_count().alias("null_count")
                        ]).row(0)
                        
                        numeric_stats[col] = {
                            "min": stats["min"],
                            "max": stats["max"],
                            "mean": stats["mean"],
                            "median": stats["median"],
                            "std": stats["std"],
                            "null_count": stats["null_count"]
                        }
                    except:
                        # 如果计算统计信息失败，跳过
                        pass
            
            result["numeric_stats"] = numeric_stats
            
            # 对分类列计算值分布
            categorical_stats = {}
            for col in df_stats.columns:
                if df_stats[col].dtype in [pl.Categorical, pl.Utf8]:
                    try:
                        # 计算值计数
                        value_counts = df_stats.select([
                            pl.col(col).value_counts(sort=True).alias("value_counts")
                        ]).row(0)["value_counts"]
                        
                        # 转换为字典
                        if isinstance(value_counts, pl.Series):
                            values = value_counts.struct.field("values").to_list()
                            counts = value_counts.struct.field("counts").to_list()
                            categorical_stats[col] = {
                                "unique_count": len(values),
                                "top_values": {str(v): c for v, c in zip(values[:10], counts[:10])}
                            }
                    except:
                        # 如果计算统计信息失败，跳过
                        pass
            
            result["categorical_stats"] = categorical_stats
            
            return result
        
        # 使用事件循环的线程池执行CPU密集型任务
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, get_stats_sync)
        
    async def cleanup_temp_files(self, max_age_hours: int = 24):
        """
        清理临时文件
        
        Args:
            max_age_hours: 最大保留时间（小时）
        """
        now = datetime.now()
        
        for file_path in Path(TEMP_DIR).glob("*"):
            if file_path.is_file():
                # 获取文件修改时间
                mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                age_hours = (now - mtime).total_seconds() / 3600
                
                # 删除超过最大保留时间的文件
                if age_hours > max_age_hours:
                    try:
                        file_path.unlink()
                        logger.info(f"已删除临时文件: {file_path}")
                    except Exception as e:
                        logger.error(f"删除临时文件失败: {file_path}, 错误: {str(e)}")

# 创建单例实例
data_processor = DataProcessor() 
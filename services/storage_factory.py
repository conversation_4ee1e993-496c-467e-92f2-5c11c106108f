"""
存储服务工厂
用于根据配置选择使用本地存储或MinIO存储
"""
import logging
from typing import Optional

from core.config import settings
from services.file_storage import FileStorageService
from services.minio_file_storage import MinioFileStorageService

logger = logging.getLogger(__name__)

class StorageFactory:
    """存储服务工厂，根据配置选择适当的存储服务"""
    
    _local_storage: Optional[FileStorageService] = None
    _minio_storage: Optional[MinioFileStorageService] = None
    
    @classmethod
    def get_storage_service(cls):
        """
        获取存储服务实例
        
        根据配置返回本地文件存储或MinIO对象存储
        
        Returns:
            存储服务实例
        """
        # 获取配置的存储类型
        storage_type = getattr(settings, "STORAGE_TYPE", "local").lower()
        
        if storage_type == "minio":
            return cls.get_minio_storage()
        else:
            return cls.get_local_storage()
    
    @classmethod
    def get_local_storage(cls) -> FileStorageService:
        """
        获取本地存储服务实例(单例模式)
        
        Returns:
            本地存储服务实例
        """
        if cls._local_storage is None:
            # 创建本地存储服务
            cls._local_storage = FileStorageService(
                base_storage_path=getattr(settings, "STORAGE_PATH", "storage/files"),
                temp_dir=getattr(settings, "TEMP_DIR", "temp"),
                use_hash_path=True
            )
            logger.info("已初始化本地文件存储服务")
        
        return cls._local_storage
    
    @classmethod
    def get_minio_storage(cls) -> MinioFileStorageService:
        """
        获取MinIO存储服务实例(单例模式)
        
        Returns:
            MinIO存储服务实例
        """
        if cls._minio_storage is None:
            # 构建MinIO端点
            endpoint = f"{settings.MINIO_ENDPOINT}:{settings.MINIO_API_PORT}"
            
            # 创建MinIO存储服务
            cls._minio_storage = MinioFileStorageService(
                endpoint=endpoint,
                access_key=settings.MINIO_ACCESS_KEY,
                secret_key=settings.MINIO_SECRET_KEY,
                bucket_name=settings.MINIO_BUCKET,
                region=getattr(settings, "MINIO_REGION", "us-east-1"),
                use_ssl=getattr(settings, "MINIO_USE_SSL", False),
                temp_dir=getattr(settings, "TEMP_DIR", "temp")
            )
            logger.info(f"已初始化MinIO对象存储服务，端点: {endpoint}")
        
        return cls._minio_storage

# 导出便捷函数
def get_storage_service():
    """
    获取存储服务实例
    
    Returns:
        根据配置返回适当的存储服务实例
    """
    return StorageFactory.get_storage_service() 
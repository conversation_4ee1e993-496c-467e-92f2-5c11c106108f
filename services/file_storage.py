import os
import io
import asyncio
import logging
import hashlib
import aiofiles
import uuid
from typing import Optional, Dict, Any, List, Union, BinaryIO
from datetime import datetime
from pathlib import Path
import mimetypes
import shutil
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import UploadFile

from db.session import get_db
from crud.crud_file import file as file_crud
from schemas.file import FileCreate, FileUpdate
from utils.request_cache import cached_request

logger = logging.getLogger(__name__)

class FileStorageService:
    async def _safe_tell(self, file_obj):
        """安全获取文件位置，支持FastAPI的UploadFile对象"""
        try:
            # 对于内置文件对象，直接调用tell方法
            if hasattr(file_obj, 'tell') and callable(file_obj.tell):
                return file_obj.tell()
                
            # 对于FastAPI的UploadFile对象
            if hasattr(file_obj, 'file') and hasattr(file_obj.file, 'tell'):
                return file_obj.file.tell()
                
            # 无法获取位置时返回0
            return 0
        except Exception as e:
            print(f"获取文件位置失败: {str(e)}")
            return 0

    """文件存储服务，用于处理文件上传、下载和管理"""
    
    def __init__(
        self, 
        base_storage_path: str,
        temp_dir: Optional[str] = None,
        chunk_size: int = 8192,
        use_hash_path: bool = True
    ):
        """初始化文件存储服务
        
        Args:
            base_storage_path: 基础存储路径
            temp_dir: 临时文件目录
            chunk_size: 处理文件时的块大小
            use_hash_path: 是否使用哈希路径存储文件
        """
        self.base_path = os.path.abspath(base_storage_path)
        self.temp_dir = temp_dir or os.path.join(self.base_path, "temp")
        self.chunk_size = chunk_size
        self.use_hash_path = use_hash_path
        
        # 确保目录存在
        os.makedirs(self.base_path, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
    
    async def save_file(
        self, 
        file_content: Union[bytes, BinaryIO, io.BytesIO, UploadFile],
        filename: str,
        directory: Optional[str] = None,
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        is_public: bool = False,
        description: Optional[str] = None,
        owner_id: Optional[int] = None,
        db: Optional[AsyncSession] = None
    ) -> Dict[str, Any]:
        """保存文件
        
        Args:
            file_content: 文件内容(字节或文件对象或FastAPI上传文件)
            filename: 文件名
            directory: 目标子目录
            content_type: 内容类型
            metadata: 附加元数据
            category: 文件分类
            tags: 标签列表
            is_public: 是否公开访问
            description: 文件描述
            owner_id: 所有者ID
            db: 数据库会话，若不提供将自动创建
            
        Returns:
            文件信息
        """
        # 生成唯一标识符
        file_id = str(uuid.uuid4())
        
        # 处理文件名，确保安全
        safe_filename = self._get_safe_filename(filename)
        
        # 推断内容类型
        if content_type is None:
            content_type, _ = mimetypes.guess_type(filename)
            content_type = content_type or "application/octet-stream"
        
        # 从UploadFile获取内容
        if isinstance(file_content, UploadFile):
            # 创建临时文件
            temp_file_path = os.path.join(self.temp_dir, f"temp_{file_id}.bin")
            
            try:
                # 保存上传文件到临时文件
                with open(temp_file_path, "wb") as temp_file:
                    content = await file_content.read()
                    temp_file.write(content)
                
                # 使用临时文件计算哈希
                with open(temp_file_path, "rb") as f:
                    file_hash = self._compute_file_hash_sync(f)
                
                # 重新打开文件进行存储
                file_content = open(temp_file_path, "rb")
            except Exception as e:
                logger.error(f"处理上传文件失败: {str(e)}")
                raise
        else:
            # 计算文件哈希
            file_hash = await self._compute_file_hash(file_content)
        
        # 确定存储路径
        if self.use_hash_path:
            # 使用哈希的前两部分作为目录结构，避免单一目录下文件过多
            hash_dir = os.path.join(file_hash[:2], file_hash[2:4])
            relative_path = os.path.join(hash_dir, file_id + "_" + safe_filename)
        else:
            # 使用日期目录
            date_dir = datetime.now().strftime("%Y/%m/%d")
            relative_path = os.path.join(date_dir, file_id + "_" + safe_filename)
        
        # 如果指定了目录，添加到路径前面
        if directory:
            relative_path = os.path.join(directory, relative_path)
            
        # 完整存储路径
        storage_path = os.path.join(self.base_path, relative_path)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(storage_path), exist_ok=True)
        
        # 保存文件
        file_size = await self._write_file(file_content, storage_path)
        
        # 清理临时资源
        try:
            if isinstance(file_content, UploadFile):
                file_content.file.close()
            elif hasattr(file_content, "close"):
                file_content.close()
                
            # 删除临时文件
            if isinstance(file_content, UploadFile) and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        except Exception as e:
            logger.warning(f"清理临时资源失败: {str(e)}")
        
        # 创建文件访问URL
        file_url = f"/storage/{relative_path}"
        
        # 创建元数据
        file_info = {
            "id": file_id,
            "filename": safe_filename,
            "original_filename": filename,
            "content_type": content_type,
            "size": file_size,
            "file_hash": file_hash,
            "storage_path": relative_path,
            "file_url": file_url,
            "is_public": is_public,
            "category": category,
            "tags": tags,
            "description": description,
            "file_metadata": metadata,
            "owner_id": owner_id
        }
        
        # 保存到数据库
        await self._save_to_database(file_info, db)
        
        return file_info
    
    async def _save_to_database(self, file_info: Dict[str, Any], db: Optional[AsyncSession] = None) -> None:
        """保存文件信息到数据库
        
        Args:
            file_info: 文件信息
            db: 数据库会话，若不提供将自动创建
        """
        close_db = False
        try:
            # 如果没有提供数据库会话，创建一个新的
            if db is None:
                close_db = True
                async for session in get_db():
                    db = session
                    break
            
            # 创建文件创建模式
            file_create = FileCreate(
                filename=file_info["filename"],
                original_filename=file_info["original_filename"],
                storage_path=file_info["storage_path"],
                file_url=file_info["file_url"],
                content_type=file_info["content_type"],
                size=file_info["size"],
                file_hash=file_info["file_hash"],
                is_public=file_info["is_public"],
                category=file_info["category"],
                tags=file_info["tags"],
                description=file_info["description"],
                file_metadata=file_info["file_metadata"],
                owner_id=file_info["owner_id"]
            )
            
            # 保存到数据库
            await file_crud.create(db, obj_in=file_create)
            
        except Exception as e:
            logger.error(f"保存文件信息到数据库失败: {str(e)}")
            raise
        finally:
            # 如果我们创建了会话，则关闭它
            if close_db and db:
                await db.close()
    
    async def _write_file(self, file_content: Union[bytes, BinaryIO, io.BytesIO], path: str) -> int:
        """写入文件内容
        
        Args:
            file_content: 文件内容
            path: 目标路径
            
        Returns:
            文件大小(字节)
        """
        # 安全检查：确保路径在基础目录下
        abs_path = os.path.abspath(path)
        if not abs_path.startswith(self.base_path):
            logger.error(f"安全错误：尝试写入基础目录之外的位置: {path}")
            raise ValueError(f"不允许写入到基础存储目录之外: {path}")
            
        # 确保目录存在
        try:
            os.makedirs(os.path.dirname(path), exist_ok=True)
        except (PermissionError, OSError) as e:
            logger.error(f"创建目录失败: {os.path.dirname(path)}, 错误: {str(e)}")
            raise ValueError(f"无法创建目录: {os.path.dirname(path)}")
        
        total_bytes = 0
        
        try:
            async with aiofiles.open(path, "wb") as f:
                if isinstance(file_content, bytes):
                    await f.write(file_content)
                    total_bytes = len(file_content)
                else:
                    # 重置文件指针(如果是文件对象)
                    if hasattr(file_content, "seek"):
                        file_content.seek(0)
                    
                    # 分块读取写入
                    while True:
                        if hasattr(file_content, "read"):
                            # 同步read方法
                            chunk = file_content.read(self.chunk_size)
                            if not chunk:
                                break
                        else:
                            # 可能是异步迭代器
                            try:
                                chunk = await file_content.read(self.chunk_size)
                                if not chunk:
                                    break
                            except (AttributeError, TypeError):
                                # 不是异步迭代器，也不是同步read
                                logger.error("无法读取文件内容，不支持的类型")
                                raise ValueError("不支持的文件内容类型")
                        
                        await f.write(chunk)
                        total_bytes += len(chunk)
            
            return total_bytes
        except Exception as e:
            logger.error(f"写入文件 {path} 失败: {str(e)}")
            # 如果文件已创建，删除它
            if os.path.exists(path):
                try:
                    os.unlink(path)
                except Exception as del_err:
                    logger.error(f"清理失败的文件时出错: {str(del_err)}")
            raise
    
    def _compute_file_hash_sync(self, file_obj: BinaryIO) -> str:
        """同步计算文件哈希
        
        Args:
            file_obj: 文件对象
            
        Returns:
            哈希值
        """
        sha256 = hashlib.sha256()
        
        # 保存当前位置
        try:
            original_position = file_obj.tell()
        except (AttributeError, IOError):
            original_position = 0
        
        try:
            file_obj.seek(0)
        except (AttributeError, IOError):
            # 如果不能重置位置，返回空哈希
            return ""
        
        # 分块读取更新哈希
        try:
            chunk = file_obj.read(self.chunk_size)
            while chunk:
                sha256.update(chunk)
                chunk = file_obj.read(self.chunk_size)
        except (AttributeError, IOError) as e:
            logger.error(f"读取文件数据失败: {str(e)}")
            return ""
        
        # 恢复位置
        try:
            file_obj.seek(original_position)
        except (AttributeError, IOError):
            pass
        
        return sha256.hexdigest()
    
    async def _compute_file_hash(self, file_content: Union[bytes, BinaryIO, io.BytesIO]) -> str:
        """计算文件内容哈希
        
        Args:
            file_content: 文件内容
            
        Returns:
            哈希值
        """
        sha256 = hashlib.sha256()
        
        if isinstance(file_content, bytes):
            sha256.update(file_content)
        else:
            # 重置文件指针(如果是文件对象)
            if hasattr(file_content, "seek"):
                try:
                    original_position = await self._safe_tell(file_content)
                    file_content.seek(0)
                except Exception as e:
                    logger.warning(f"重置文件指针失败: {str(e)}")
                    original_position = 0
            else:
                original_position = 0
            
            # 分块读取更新哈希
            if hasattr(file_content, "read"):
                # 同步读取
                try:
                    while True:
                        chunk = file_content.read(self.chunk_size)
                        if not chunk:
                            break
                        sha256.update(chunk)
                except Exception as e:
                    logger.error(f"读取文件内容失败: {str(e)}")
            else:
                # 异步读取
                temp_file = os.path.join(self.temp_dir, f"temp_{uuid.uuid4()}.bin")
                try:
                    # 保存到临时文件
                    total_bytes = await self._write_file(file_content, temp_file)
                    
                    # 从临时文件读取并计算哈希
                    async with aiofiles.open(temp_file, "rb") as f:
                        chunk = await f.read(self.chunk_size)
                        while chunk:
                            sha256.update(chunk)
                            chunk = await f.read(self.chunk_size)
                finally:
                    # 删除临时文件
                    if os.path.exists(temp_file):
                        os.unlink(temp_file)
            
            # 恢复文件指针
            if hasattr(file_content, "seek"):
                try:
                    file_content.seek(original_position)
                except Exception as e:
                    logger.warning(f"恢复文件指针失败: {str(e)}")
        
        return sha256.hexdigest()
    
    def _get_safe_filename(self, filename: str) -> str:
        """生成安全的文件名，移除不安全字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            安全的文件名
        """
        # 移除路径信息
        filename = os.path.basename(filename)
        
        # 移除不安全字符
        unsafe_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in unsafe_chars:
            filename = filename.replace(char, '_')
            
        # 限制长度
        if len(filename) > 100:
            name, ext = os.path.splitext(filename)
            filename = name[:100 - len(ext)] + ext
            
        return filename
    
    @cached_request(ttl=300)  # 缓存5分钟
    async def get_file_info(self, file_id: Union[str, UUID], db: Optional[AsyncSession] = None) -> Optional[Dict[str, Any]]:
        """获取文件信息
        
        Args:
            file_id: 文件ID（字符串或UUID对象）
            db: 数据库会话，若不提供将自动创建
            
        Returns:
            文件信息或None
        """
        close_db = False
        try:
            # 转换为UUID对象
            if isinstance(file_id, str):
                try:
                    file_id = UUID(file_id)
                except ValueError:
                    logger.error(f"无效的文件ID格式: {file_id}")
                    return None
            
            # 如果没有提供数据库会话，创建一个新的
            if db is None:
                close_db = True
                async for session in get_db():
                    db = session
                    break
                    
            # 从数据库获取文件信息
            file_obj = await file_crud.get(db, id=file_id)
            
            if not file_obj:
                return None
                
            # 将ORM对象转换为字典
            file_info = {
                "id": str(file_obj.id),
                "filename": file_obj.filename,
                "original_filename": file_obj.original_filename,
                "storage_path": file_obj.storage_path,
                "file_url": file_obj.file_url,
                "content_type": file_obj.content_type,
                "size": file_obj.size,
                "file_hash": file_obj.file_hash,
                "is_public": file_obj.is_public,
                "category": file_obj.category,
                "tags": file_obj.tags,
                "description": file_obj.description,
                "status": file_obj.status,
                "file_metadata": file_obj.file_metadata,
                "owner_id": file_obj.owner_id,
                "created_at": file_obj.created_at.isoformat() if file_obj.created_at else None,
                "updated_at": file_obj.updated_at.isoformat() if file_obj.updated_at else None,
                "expires_at": file_obj.expires_at.isoformat() if file_obj.expires_at else None
            }
            
            return file_info
        except Exception as e:
            logger.error(f"获取文件信息失败: {str(e)}")
            return None
        finally:
            # 如果我们创建了会话，则关闭它
            if close_db and db:
                await db.close()
    
    async def get_file_by_hash(self, file_hash: str, db: Optional[AsyncSession] = None) -> Optional[Dict[str, Any]]:
        """通过哈希获取文件信息
        
        Args:
            file_hash: 文件哈希
            db: 数据库会话，若不提供将自动创建
            
        Returns:
            文件信息或None
        """
        close_db = False
        try:
            # 如果没有提供数据库会话，创建一个新的
            if db is None:
                close_db = True
                async for session in get_db():
                    db = session
                    break
                    
            # 从数据库获取文件信息
            file_obj = await file_crud.get_by_hash(db, file_hash=file_hash)
            
            if not file_obj:
                return None
                
            # 将ORM对象转换为字典
            file_info = {
                "id": str(file_obj.id),
                "filename": file_obj.filename,
                "original_filename": file_obj.original_filename,
                "storage_path": file_obj.storage_path,
                "file_url": file_obj.file_url,
                "content_type": file_obj.content_type,
                "size": file_obj.size,
                "file_hash": file_obj.file_hash,
                "is_public": file_obj.is_public,
                "category": file_obj.category,
                "tags": file_obj.tags,
                "description": file_obj.description,
                "status": file_obj.status,
                "file_metadata": file_obj.file_metadata,
                "owner_id": file_obj.owner_id,
                "created_at": file_obj.created_at.isoformat() if file_obj.created_at else None,
                "updated_at": file_obj.updated_at.isoformat() if file_obj.updated_at else None,
                "expires_at": file_obj.expires_at.isoformat() if file_obj.expires_at else None
            }
            
            return file_info
        except Exception as e:
            logger.error(f"通过哈希获取文件信息失败: {str(e)}")
            return None
        finally:
            # 如果我们创建了会话，则关闭它
            if close_db and db:
                await db.close()
    
    async def delete_file(self, file_id: Union[str, UUID], db: Optional[AsyncSession] = None) -> bool:
        """删除文件
        
        Args:
            file_id: 文件ID（字符串或UUID对象）
            db: 数据库会话，若不提供将自动创建
            
        Returns:
            是否成功删除
        """
        close_db = False
        try:
            # 转换为UUID对象
            if isinstance(file_id, str):
                try:
                    file_id = UUID(file_id)
                except ValueError:
                    logger.error(f"无效的文件ID格式: {file_id}")
                    return False
            
            # 如果没有提供数据库会话，创建一个新的
            if db is None:
                close_db = True
                async for session in get_db():
                    db = session
                    break
            
            # 获取文件信息
            file_obj = await file_crud.get(db, id=file_id)
            if not file_obj:
                return False
            
            # 获取物理文件路径
            file_path = os.path.join(self.base_path, file_obj.storage_path)
            
            # 先软删除数据库记录
            success = await file_crud.remove(db, id=file_id)
            
            if success:
                # 如果数据库删除成功，尝试删除物理文件
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    logger.info(f"已删除物理文件: {file_path}")
                else:
                    logger.warning(f"物理文件不存在，无法删除: {file_path}")
                
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"删除文件失败: {file_id}, 错误: {str(e)}")
            return False
        finally:
            # 如果我们创建了会话，则关闭它
            if close_db and db:
                await db.close()
    
    async def get_files(
        self,
        skip: int = 0,
        limit: int = 100,
        owner_id: Optional[int] = None,
        is_public: Optional[bool] = None,
        category: Optional[str] = None,
        status: Optional[str] = None,
        search: Optional[str] = None,
        db: Optional[AsyncSession] = None
    ) -> Dict[str, Any]:
        """获取文件列表
        
        Args:
            skip: 跳过数量
            limit: 限制数量
            owner_id: 所有者ID过滤
            is_public: 是否公开过滤
            category: 分类过滤
            status: 状态过滤
            search: 搜索关键词
            db: 数据库会话，若不提供将自动创建
            
        Returns:
            文件列表和总数
        """
        close_db = False
        try:
            # 如果没有提供数据库会话，创建一个新的
            if db is None:
                close_db = True
                async for session in get_db():
                    db = session
                    break
            
            # 获取文件列表和总数
            files, total = await file_crud.get_multi_with_filter(
                db, 
                skip=skip, 
                limit=limit,
                owner_id=owner_id,
                is_public=is_public,
                category=category,
                status=status,
                search=search
            )
            
            # 将ORM对象转换为字典列表
            file_list = []
            for file_obj in files:
                file_info = {
                    "id": str(file_obj.id),
                    "filename": file_obj.filename,
                    "original_filename": file_obj.original_filename,
                    "file_url": file_obj.file_url,
                    "content_type": file_obj.content_type,
                    "size": file_obj.size,
                    "category": file_obj.category,
                    "is_public": file_obj.is_public,
                    "status": file_obj.status,
                    "owner_id": file_obj.owner_id,
                    "created_at": file_obj.created_at.isoformat() if file_obj.created_at else None
                }
                file_list.append(file_info)
            
            return {
                "total": total,
                "items": file_list
            }
                
        except Exception as e:
            logger.error(f"获取文件列表失败: {str(e)}")
            return {
                "total": 0,
                "items": []
            }
        finally:
            # 如果我们创建了会话，则关闭它
            if close_db and db:
                await db.close()

# 创建实例，使用项目配置
from core.config import settings

storage_service = FileStorageService(
    base_storage_path=getattr(settings, "STORAGE_PATH", "storage"),
    temp_dir=getattr(settings, "TEMP_DIR", "temp")
) 
"""
MinIO对象存储服务
用于通过MinIO提供文件存储功能
"""
import io
import os
import uuid
import hashlib
import logging
import asyncio
from typing import Optional, Dict, Any, List, Union, BinaryIO
from datetime import datetime, timedelta
from uuid import UUID

from minio import Minio
from minio.error import S3Error
from fastapi import UploadFile
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import settings
from db.session import get_db
from crud.crud_file import file as file_crud
from schemas.file import FileCreate, FileUpdate
from utils.request_cache import cached_request
from utils.upload_file_adapter import adapt_upload_file

logger = logging.getLogger(__name__)

class MinioFileStorageService:
    async def _safe_tell(self, file_obj):
        """安全获取文件位置，支持FastAPI的UploadFile对象"""
        try:
            # 对于内置文件对象，直接调用tell方法
            if hasattr(file_obj, 'tell') and callable(file_obj.tell):
                return file_obj.tell()
                
            # 对于FastAPI的UploadFile对象
            if hasattr(file_obj, 'file') and hasattr(file_obj.file, 'tell'):
                return file_obj.file.tell()
                
            # 无法获取位置时返回0
            return 0
        except Exception as e:
            logger.error(f"获取文件位置失败: {str(e)}")
            return 0

    """MinIO文件存储服务，用于处理文件上传、下载和管理"""
    
    def __init__(
        self, 
        endpoint: str,
        access_key: str,
        secret_key: str,
        bucket_name: str,
        region: str = "us-east-1",
        use_ssl: bool = False,
        temp_dir: str = "temp",
        chunk_size: int = 8192
    ):
        """初始化MinIO文件存储服务
        
        Args:
            endpoint: MinIO端点
            access_key: 访问密钥
            secret_key: 秘密密钥
            bucket_name: 存储桶名称
            region: 区域
            use_ssl: 是否使用SSL
            temp_dir: 临时文件目录
            chunk_size: 处理文件时的块大小
        """
        self.endpoint = endpoint
        self.access_key = access_key
        self.secret_key = secret_key
        self.bucket_name = bucket_name
        self.region = region
        self.use_ssl = use_ssl
        self.temp_dir = temp_dir
        self.chunk_size = chunk_size
        self.bucket_ready = False  # 标记存储桶状态
        
        # 构建基础URL，用于拼接预签名URL
        ssl_type = getattr(settings, "SSL_TYPE", "http").lower()
        # 处理引号问题
        if ssl_type.startswith('"') and ssl_type.endswith('"'):
            ssl_type = ssl_type[1:-1]
        if ssl_type.startswith("'") and ssl_type.endswith("'"):
            ssl_type = ssl_type[1:-1]
            
        if ssl_type not in ["http", "https"]:
            ssl_type = "http"  # 默认为http
        self.base_url = f"{ssl_type}://{endpoint}"
        logger.info(f"MinIO基础URL: {self.base_url}")
        
        # 确保临时目录存在
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # 创建MinIO客户端
        self.client = Minio(
            endpoint=self.endpoint,
            access_key=self.access_key,
            secret_key=self.secret_key,
            region=self.region,
            secure=self.use_ssl
        )
        
        # 不再同步调用_ensure_bucket_exists
        # 我们将在应用启动时的异步方法中初始化
        logger.info("MinIO存储服务初始化，存储桶将在首次使用时初始化")
    
    async def ensure_bucket_exists(self):
        """异步确保存储桶存在，如果不存在则创建"""
        if self.bucket_ready:
            return True
            
        try:
            # 在事件循环中运行同步操作
            loop = asyncio.get_event_loop()
            exists = await loop.run_in_executor(
                None, lambda: self.client.bucket_exists(self.bucket_name)
            )
            
            if not exists:
                # 创建存储桶
                await loop.run_in_executor(
                    None, lambda: self.client.make_bucket(self.bucket_name, location=self.region)
                )
                logger.info(f"已创建MinIO存储桶: {self.bucket_name}")
            
            self.bucket_ready = True
            return True
        except Exception as e:
            logger.error(f"MinIO存储桶初始化失败: {str(e)}")
            return False
    
    # 旧的同步方法保留，但我们不在初始化时调用它
    def _ensure_bucket_exists(self):
        """确保存储桶存在，如果不存在则创建(同步方法)"""
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name, location=self.region)
                logger.info(f"已创建MinIO存储桶: {self.bucket_name}")
            return True
        except S3Error as e:
            logger.error(f"MinIO操作失败: {str(e)}")
            return False
    
    async def save_file(
        self, 
        file_content: Union[bytes, BinaryIO, io.BytesIO, UploadFile],
        filename: str,
        directory: Optional[str] = None,
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        is_public: bool = False,
        description: Optional[str] = None,
        owner_id: Optional[int] = None,
        db: Optional[AsyncSession] = None
    ) -> Dict[str, Any]:
        """保存文件到MinIO
        
        Args:
            file_content: 文件内容(字节或文件对象或FastAPI上传文件)
            filename: 文件名
            directory: 目标子目录
            content_type: 内容类型
            metadata: 附加元数据
            category: 文件分类
            tags: 标签列表
            is_public: 是否公开访问
            description: 文件描述
            owner_id: 所有者ID
            db: 数据库会话，若不提供将自动创建
            
        Returns:
            文件信息
        """
        # 确保存储桶已初始化
        await self.ensure_bucket_exists()
        
        # 生成唯一标识符
        file_id = str(uuid.uuid4())
        
        # 处理文件名，确保安全
        safe_filename = self._get_safe_filename(filename)
        
        # 如果未提供内容类型，尝试猜测
        if content_type is None:
            import mimetypes
            content_type, _ = mimetypes.guess_type(filename)
            content_type = content_type or "application/octet-stream"
        
        # 创建临时文件
        temp_file_path = os.path.join(self.temp_dir, f"temp_{file_id}.bin")
        
        try:
            # 将文件内容写入临时文件
            if isinstance(file_content, UploadFile):
                # 使用适配器模式处理UploadFile，避免tell方法错误
                adapter = await adapt_upload_file(file_content)
                
                # 从适配器读取数据写入临时文件
                with open(temp_file_path, "wb") as f:
                    # 确保从文件开始读取
                    await adapter.seek(0)
                    content = await adapter.read()
                    f.write(content)
            elif isinstance(file_content, bytes):
                with open(temp_file_path, "wb") as f:
                    f.write(file_content)
            else:
                # 检查是否是适配器
                if hasattr(file_content, '_ensure_content') and callable(getattr(file_content, '_ensure_content')):
                    # 已经是适配器，直接使用
                    with open(temp_file_path, "wb") as f:
                        await file_content.seek(0)
                        content = await file_content.read()
                        f.write(content)
                else:
                    # 重置文件指针(如果是文件对象)
                    if hasattr(file_content, "seek"):
                        try:
                            file_content.seek(0)
                        except Exception as e:
                            logger.warning(f"文件指针重置失败: {str(e)}")
                    
                    with open(temp_file_path, "wb") as f:
                        if hasattr(file_content, "read"):
                            # 同步读取
                            chunk = file_content.read(self.chunk_size)
                            while chunk:
                                f.write(chunk)
                                chunk = file_content.read(self.chunk_size)
            
            # 计算文件哈希和大小
            file_hash, file_size = self._get_file_hash_and_size(temp_file_path)
            
            # 构建存储路径
            if directory:
                object_name = f"{directory}/{file_id}_{safe_filename}"
            else:
                # 使用哈希的前4位作为目录结构
                object_name = f"{file_hash[:2]}/{file_hash[2:4]}/{file_id}_{safe_filename}"
            
            # 准备元数据 - 确保所有值都是ASCII编码兼容的
            def safe_encode_metadata(value: str) -> str:
                """安全编码元数据值，避免latin-1编码错误"""
                if not value:
                    return ""
                try:
                    # 尝试编码为latin-1，如果失败则使用base64编码
                    value.encode('latin-1')
                    return value
                except UnicodeEncodeError:
                    # 对于包含非latin-1字符的字符串，使用base64编码
                    import base64
                    encoded = base64.b64encode(value.encode('utf-8')).decode('ascii')
                    return f"base64:{encoded}"

            minio_metadata = {
                "X-Amz-Meta-Original-Filename": safe_encode_metadata(filename),
                "X-Amz-Meta-File-Hash": file_hash,
                "X-Amz-Meta-Is-Public": str(is_public).lower(),
            }

            if category:
                minio_metadata["X-Amz-Meta-Category"] = safe_encode_metadata(category)

            if description:
                minio_metadata["X-Amz-Meta-Description"] = safe_encode_metadata(description)

            if tags:
                safe_tags = [safe_encode_metadata(tag) for tag in tags]
                minio_metadata["X-Amz-Meta-Tags"] = ",".join(safe_tags)

            if owner_id is not None:
                minio_metadata["X-Amz-Meta-Owner-Id"] = str(owner_id)
            
            # 上传文件到MinIO
            try:
                # 在事件循环中异步运行同步操作
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(
                    None,
                    lambda: self.client.fput_object(
                        bucket_name=self.bucket_name,
                        object_name=object_name,
                        file_path=temp_file_path,
                        content_type=content_type,
                        metadata=minio_metadata
                    )
                )
                logger.info(f"文件已上传到MinIO: {object_name}")
            except Exception as e:
                logger.error(f"MinIO上传失败: {str(e)}")
                raise
            
            # 生成文件URL
            if is_public:
                try:
                    # 对于公开文件，生成预签名URL
                    logger.info(f"生成预签名URL - 存储桶: {self.bucket_name}, 对象: {object_name}")

                    # 获取SSL类型配置
                    ssl_type = getattr(settings, "SSL_TYPE", "http").lower()
                    # 处理引号问题
                    if ssl_type.startswith('"') and ssl_type.endswith('"'):
                        ssl_type = ssl_type[1:-1]
                    if ssl_type.startswith("'") and ssl_type.endswith("'"):
                        ssl_type = ssl_type[1:-1]

                    if ssl_type not in ["http", "https"]:
                        ssl_type = "http"  # 默认为http

                    # 异步生成预签名URL
                    presigned_url = await loop.run_in_executor(
                        None,
                        lambda: self.client.presigned_get_object(
                            bucket_name=self.bucket_name,
                            object_name=object_name,
                            expires=timedelta(days=7)  # 7天有效期
                        )
                    )

                    # 如果presigned_url已经是完整URL（包含http://或https://），直接使用
                    if presigned_url.startswith("http://") or presigned_url.startswith("https://"):
                        file_url = presigned_url
                        logger.info(f"使用MinIO SDK生成的完整预签名URL: {file_url}")
                    else:
                        # 如果只返回了路径部分，需要手动拼接完整URL
                        # 从presigned_url中提取查询参数部分
                        if '?' in presigned_url:
                            path_part, query_part = presigned_url.split('?', 1)
                            # 确保path_part不以/开头，避免URL路径重复
                            if path_part.startswith('/'):
                                path_part = path_part[1:]
                            # 重新构建完整URL
                            file_url = f"{ssl_type}://{self.endpoint}/{path_part}?{query_part}"
                        else:
                            # 没有查询参数的情况（不太可能出现在预签名URL中）
                            path_part = presigned_url
                            if path_part.startswith('/'):
                                path_part = path_part[1:]
                            file_url = f"{ssl_type}://{self.endpoint}/{path_part}"

                        logger.info(f"已手动构建完整预签名URL: {file_url}")

                    logger.info(f"最终的预签名URL: {file_url}")
                except Exception as e:
                    logger.error(f"生成预签名URL失败: {str(e)}")
                    # 生成访问URL作为备选
                    file_url = f"/api/v1/files/{file_id}/download"
                    logger.info(f"已回退到API访问URL: {file_url}")
            else:
                # 对于私有文件，生成API访问URL
                file_url = f"/api/v1/files/{file_id}/download"
            
            # 创建文件信息
            file_info = {
                "id": file_id,
                "filename": safe_filename,
                "original_filename": filename,
                "content_type": content_type,
                "size": file_size,
                "file_hash": file_hash,
                "storage_path": object_name,
                "file_url": file_url,
                "is_public": is_public,
                "category": category,
                "tags": tags,
                "description": description,
                "file_metadata": metadata,
                "owner_id": owner_id,
                "storage_type": "minio"  # 标记为MinIO存储
            }
            
            # 保存到数据库
            await self._save_to_database(file_info, db)
            
            return file_info
            
        except Exception as e:
            logger.error(f"保存文件到MinIO失败: {str(e)}")
            raise
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    
    def _get_file_hash_and_size(self, file_path: str) -> tuple:
        """计算文件哈希和大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            (哈希值, 文件大小)的元组
        """
        sha256 = hashlib.sha256()
        file_size = 0
        
        with open(file_path, "rb") as f:
            while True:
                chunk = f.read(self.chunk_size)
                if not chunk:
                    break
                sha256.update(chunk)
                file_size += len(chunk)
                
        return sha256.hexdigest(), file_size
    
    def _get_safe_filename(self, filename: str) -> str:
        """生成安全的文件名，移除不安全字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            安全的文件名
        """
        # 移除路径信息
        filename = os.path.basename(filename)
        
        # 移除不安全字符
        unsafe_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in unsafe_chars:
            filename = filename.replace(char, '_')
            
        # 限制长度
        if len(filename) > 100:
            name, ext = os.path.splitext(filename)
            filename = name[:100 - len(ext)] + ext
            
        return filename
    
    async def _save_to_database(self, file_info: Dict[str, Any], db: Optional[AsyncSession] = None) -> None:
        """保存文件信息到数据库
        
        Args:
            file_info: 文件信息
            db: 数据库会话，若不提供将自动创建
        """
        close_db = False
        try:
            # 如果没有提供数据库会话，创建一个新的
            if db is None:
                close_db = True
                async for session in get_db():
                    db = session
                    break
            
            # 创建文件创建模式
            file_create = FileCreate(
                filename=file_info["filename"],
                original_filename=file_info["original_filename"],
                storage_path=file_info["storage_path"],
                file_url=file_info["file_url"],
                content_type=file_info["content_type"],
                size=file_info["size"],
                file_hash=file_info["file_hash"],
                is_public=file_info["is_public"],
                category=file_info["category"],
                tags=file_info["tags"],
                description=file_info["description"],
                file_metadata=file_info["file_metadata"] or {},
                owner_id=file_info["owner_id"],
                storage_type="minio"
            )
            
            # 保存到数据库
            await file_crud.create(db, obj_in=file_create)
            
        except Exception as e:
            logger.error(f"保存文件信息到数据库失败: {str(e)}")
            raise
        finally:
            # 如果我们创建了会话，则关闭它
            if close_db and db:
                try:
                    await db.close()
                except Exception as e:
                    logger.warning(f"关闭数据库会话失败: {str(e)}")

    @cached_request(ttl=300)  # 缓存5分钟
    async def get_file_info(self, file_id: Union[str, UUID], db: Optional[AsyncSession] = None) -> Optional[Dict[str, Any]]:
        """获取文件信息
        
        Args:
            file_id: 文件ID（字符串或UUID对象）
            db: 数据库会话，若不提供将自动创建
            
        Returns:
            文件信息或None
        """
        close_db = False
        try:
            # 转换为UUID对象
            if isinstance(file_id, str):
                try:
                    file_id = UUID(file_id)
                except ValueError:
                    logger.error(f"无效的文件ID格式: {file_id}")
                    return None
            
            # 如果没有提供数据库会话，创建一个新的
            if db is None:
                close_db = True
                async for session in get_db():
                    db = session
                    break
                    
            # 从数据库获取文件信息
            file_obj = await file_crud.get(db, id=file_id)
            
            if not file_obj:
                return None
                
            # 检查是否是MinIO存储的文件
            if getattr(file_obj, "storage_type", "local") != "minio":
                logger.warning(f"文件ID {file_id} 不是MinIO存储的文件")
                return None
                
            # 将ORM对象转换为字典
            file_info = {
                "id": str(file_obj.id),
                "filename": file_obj.filename,
                "original_filename": file_obj.original_filename,
                "storage_path": file_obj.storage_path,
                "file_url": file_obj.file_url,
                "content_type": file_obj.content_type,
                "size": file_obj.size,
                "file_hash": file_obj.file_hash,
                "is_public": file_obj.is_public,
                "category": file_obj.category,
                "tags": file_obj.tags,
                "description": file_obj.description,
                "status": file_obj.status,
                "file_metadata": file_obj.file_metadata,
                "owner_id": file_obj.owner_id,
                "storage_type": getattr(file_obj, "storage_type", "minio"),
                "created_at": file_obj.created_at.isoformat() if file_obj.created_at else None,
                "updated_at": file_obj.updated_at.isoformat() if file_obj.updated_at else None,
                "expires_at": file_obj.expires_at.isoformat() if file_obj.expires_at else None
            }
            
            # 对于公开文件，确保返回预签名URL
            if file_obj.is_public:
                try:
                    logger.info(f"正在为公开文件 {file_id} 生成预签名URL")
                    
                    # 获取预签名URL
                    # 直接构建完整URL，使用配置中的协议和端点信息
                    ssl_type = getattr(settings, "SSL_TYPE", "http").lower()
                    # 处理引号问题
                    if ssl_type.startswith('"') and ssl_type.endswith('"'):
                        ssl_type = ssl_type[1:-1]
                    if ssl_type.startswith("'") and ssl_type.endswith("'"):
                        ssl_type = ssl_type[1:-1]
                        
                    if ssl_type not in ["http", "https"]:
                        ssl_type = "http"  # 默认为http
                        
                    # 构建预签名URL
                    presigned_url = self.client.presigned_get_object(
                        bucket_name=self.bucket_name,
                        object_name=file_obj.storage_path,
                        expires=timedelta(days=7)  # 7天有效期
                    )
                    
                    # 如果presigned_url已经是完整URL（包含http://或https://），直接使用
                    if presigned_url.startswith("http://") or presigned_url.startswith("https://"):
                        url = presigned_url
                        logger.info(f"使用MinIO SDK生成的完整预签名URL: {url}")
                    else:
                        # 如果只返回了路径部分，需要手动拼接完整URL
                        # 从presigned_url中提取查询参数部分
                        if '?' in presigned_url:
                            path_part, query_part = presigned_url.split('?', 1)
                            # 确保path_part不以/开头，避免URL路径重复
                            if path_part.startswith('/'):
                                path_part = path_part[1:]
                            # 重新构建完整URL
                            url = f"{ssl_type}://{self.endpoint}/{path_part}?{query_part}"
                        else:
                            # 没有查询参数的情况（不太可能出现在预签名URL中）
                            path_part = presigned_url
                            if path_part.startswith('/'):
                                path_part = path_part[1:]
                            url = f"{ssl_type}://{self.endpoint}/{path_part}"
                        
                        logger.info(f"已手动构建完整预签名URL: {url}")
                    
                    file_info["file_url"] = url
                    logger.info(f"文件 {file_id} 的预签名URL: {url}")
                except Exception as e:
                    logger.warning(f"更新预签名URL失败: {str(e)}")
                    # 如果生成失败，保留原有URL
                    logger.info(f"保留原有URL: {file_info['file_url']}")
            
            return file_info
        except Exception as e:
            logger.error(f"获取文件信息失败: {str(e)}")
            return None
        finally:
            # 如果我们创建了会话，则关闭它
            if close_db and db:
                try:
                    await db.close()
                except Exception as e:
                    logger.warning(f"关闭数据库会话失败: {str(e)}")
    
    async def get_file_by_hash(self, file_hash: str, db: Optional[AsyncSession] = None) -> Optional[Dict[str, Any]]:
        """通过哈希获取文件信息
        
        Args:
            file_hash: 文件哈希
            db: 数据库会话，若不提供将自动创建
            
        Returns:
            文件信息或None
        """
        close_db = False
        try:
            # 如果没有提供数据库会话，创建一个新的
            if db is None:
                close_db = True
                async for session in get_db():
                    db = session
                    break
                    
            # 从数据库获取文件信息
            file_obj = await file_crud.get_by_hash(db, file_hash=file_hash)
            
            if not file_obj:
                return None
                
            # 检查是否是MinIO存储的文件
            if getattr(file_obj, "storage_type", "local") != "minio":
                logger.warning(f"哈希为 {file_hash} 的文件不是MinIO存储的文件")
                return None
                
            # 将ORM对象转换为字典
            file_info = {
                "id": str(file_obj.id),
                "filename": file_obj.filename,
                "original_filename": file_obj.original_filename,
                "storage_path": file_obj.storage_path,
                "file_url": file_obj.file_url,
                "content_type": file_obj.content_type,
                "size": file_obj.size,
                "file_hash": file_obj.file_hash,
                "is_public": file_obj.is_public,
                "category": file_obj.category,
                "tags": file_obj.tags,
                "description": file_obj.description,
                "status": file_obj.status,
                "file_metadata": file_obj.file_metadata,
                "owner_id": file_obj.owner_id,
                "storage_type": getattr(file_obj, "storage_type", "minio"),
                "created_at": file_obj.created_at.isoformat() if file_obj.created_at else None,
                "updated_at": file_obj.updated_at.isoformat() if file_obj.updated_at else None,
                "expires_at": file_obj.expires_at.isoformat() if file_obj.expires_at else None
            }
            
            # 对于公开文件，确保返回预签名URL
            if file_obj.is_public:
                try:
                    logger.info(f"为哈希值 {file_hash} 的公开文件生成预签名URL")
                    
                    # 获取预签名URL
                    ssl_type = getattr(settings, "SSL_TYPE", "http").lower()
                    # 处理引号问题
                    if ssl_type.startswith('"') and ssl_type.endswith('"'):
                        ssl_type = ssl_type[1:-1]
                    if ssl_type.startswith("'") and ssl_type.endswith("'"):
                        ssl_type = ssl_type[1:-1]
                        
                    if ssl_type not in ["http", "https"]:
                        ssl_type = "http"  # 默认为http
                        
                    # 构建预签名URL
                    presigned_url = self.client.presigned_get_object(
                        bucket_name=self.bucket_name,
                        object_name=file_obj.storage_path,
                        expires=timedelta(days=7)  # 7天有效期
                    )
                    
                    # 如果presigned_url已经是完整URL（包含http://或https://），直接使用
                    if presigned_url.startswith("http://") or presigned_url.startswith("https://"):
                        url = presigned_url
                        logger.info(f"使用MinIO SDK生成的完整预签名URL: {url}")
                    else:
                        # 如果只返回了路径部分，需要手动拼接完整URL
                        # 从presigned_url中提取查询参数部分
                        if '?' in presigned_url:
                            path_part, query_part = presigned_url.split('?', 1)
                            # 确保path_part不以/开头，避免URL路径重复
                            if path_part.startswith('/'):
                                path_part = path_part[1:]
                            # 重新构建完整URL
                            url = f"{ssl_type}://{self.endpoint}/{path_part}?{query_part}"
                        else:
                            # 没有查询参数的情况（不太可能出现在预签名URL中）
                            path_part = presigned_url
                            if path_part.startswith('/'):
                                path_part = path_part[1:]
                            url = f"{ssl_type}://{self.endpoint}/{path_part}"
                        
                        logger.info(f"已手动构建完整预签名URL: {url}")
                    
                    file_info["file_url"] = url
                except Exception as e:
                    logger.warning(f"更新预签名URL失败: {str(e)}")
            
            return file_info
        except Exception as e:
            logger.error(f"通过哈希获取文件信息失败: {str(e)}")
            return None
        finally:
            # 如果我们创建了会话，则关闭它
            if close_db and db:
                try:
                    await db.close()
                except Exception as e:
                    logger.warning(f"关闭数据库会话失败: {str(e)}")
    
    async def delete_file(self, file_id: Union[str, UUID], db: Optional[AsyncSession] = None) -> bool:
        """删除文件
        
        Args:
            file_id: 文件ID（字符串或UUID对象）
            db: 数据库会话，若不提供将自动创建
            
        Returns:
            是否成功删除
        """
        close_db = False
        try:
            # 转换为UUID对象
            if isinstance(file_id, str):
                try:
                    file_id = UUID(file_id)
                except ValueError:
                    logger.error(f"无效的文件ID格式: {file_id}")
                    return False
            
            # 如果没有提供数据库会话，创建一个新的
            if db is None:
                close_db = True
                async for session in get_db():
                    db = session
                    break
            
            # 获取文件信息
            file_obj = await file_crud.get(db, id=file_id)
            if not file_obj:
                return False
            
            # 检查是否是MinIO存储的文件
            if getattr(file_obj, "storage_type", "local") != "minio":
                logger.warning(f"文件 {file_id} 不是MinIO存储的文件")
                return False
            
            # 先软删除数据库记录
            success = await file_crud.remove(db, id=file_id)
            
            if success:
                # 如果数据库删除成功，尝试从MinIO删除文件
                try:
                    self.client.remove_object(
                        bucket_name=self.bucket_name,
                        object_name=file_obj.storage_path
                    )
                    logger.info(f"已从MinIO删除文件: {file_obj.storage_path}")
                except Exception as e:
                    logger.error(f"从MinIO删除文件失败: {str(e)}")
                    # 即使MinIO删除失败，我们仍然认为操作成功，因为数据库记录已删除
                
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"删除文件失败: {file_id}, 错误: {str(e)}")
            return False
        finally:
            # 如果我们创建了会话，则关闭它
            if close_db and db:
                try:
                    await db.close()
                except Exception as e:
                    logger.warning(f"关闭数据库会话失败: {str(e)}")
    
    async def get_file_content(self, file_id: Union[str, UUID], db: Optional[AsyncSession] = None) -> Optional[tuple]:
        """获取文件内容
        
        Args:
            file_id: 文件ID（字符串或UUID对象）
            db: 数据库会话，若不提供将自动创建
            
        Returns:
            (文件内容, 内容类型)元组或None
        """
        close_db = False
        try:
            # 转换为UUID对象
            if isinstance(file_id, str):
                try:
                    file_id = UUID(file_id)
                except ValueError:
                    logger.error(f"无效的文件ID格式: {file_id}")
                    return None
            
            # 如果没有提供数据库会话，创建一个新的
            if db is None:
                close_db = True
                async for session in get_db():
                    db = session
                    break
            
            # 获取文件信息
            file_obj = await file_crud.get(db, id=file_id)
            if not file_obj:
                return None
            
            # 检查是否是MinIO存储的文件
            if getattr(file_obj, "storage_type", "local") != "minio":
                logger.warning(f"文件 {file_id} 不是MinIO存储的文件")
                return None
            
            # 创建临时文件
            temp_file_path = os.path.join(self.temp_dir, f"temp_{uuid.uuid4()}.bin")
            
            try:
                # 从MinIO下载文件
                self.client.fget_object(
                    bucket_name=self.bucket_name,
                    object_name=file_obj.storage_path,
                    file_path=temp_file_path
                )
                
                # 读取文件内容
                with open(temp_file_path, "rb") as f:
                    content = f.read()
                
                return content, file_obj.content_type
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
        except Exception as e:
            logger.error(f"获取文件内容失败: {str(e)}")
            return None
        finally:
            # 如果我们创建了会话，则关闭它
            if close_db and db:
                try:
                    await db.close()
                except Exception as e:
                    logger.warning(f"关闭数据库会话失败: {str(e)}")
    
    async def get_files(
        self,
        skip: int = 0,
        limit: int = 100,
        owner_id: Optional[int] = None,
        is_public: Optional[bool] = None,
        category: Optional[str] = None,
        status: Optional[str] = None,
        search: Optional[str] = None,
        db: Optional[AsyncSession] = None
    ) -> Dict[str, Any]:
        """获取文件列表
        
        Args:
            skip: 跳过数量
            limit: 限制数量
            owner_id: 所有者ID过滤
            is_public: 是否公开过滤
            category: 分类过滤
            status: 状态过滤
            search: 搜索关键词
            db: 数据库会话，若不提供将自动创建
            
        Returns:
            文件列表和总数
        """
        close_db = False
        try:
            # 如果没有提供数据库会话，创建一个新的
            if db is None:
                close_db = True
                async for session in get_db():
                    db = session
                    break
            
            # 添加MinIO存储类型过滤
            storage_type = "minio"
            
            # 获取文件列表和总数
            files, total = await file_crud.get_multi_with_filter(
                db, 
                skip=skip, 
                limit=limit,
                owner_id=owner_id,
                is_public=is_public,
                category=category,
                status=status,
                search=search,
                storage_type=storage_type
            )
            
            # 将ORM对象转换为字典列表
            file_list = []
            for file_obj in files:
                file_info = {
                    "id": str(file_obj.id),
                    "filename": file_obj.filename,
                    "original_filename": file_obj.original_filename,
                    "file_url": file_obj.file_url,
                    "content_type": file_obj.content_type,
                    "size": file_obj.size,
                    "category": file_obj.category,
                    "is_public": file_obj.is_public,
                    "status": file_obj.status,
                    "owner_id": file_obj.owner_id,
                    "storage_type": getattr(file_obj, "storage_type", "minio"),
                    "created_at": file_obj.created_at.isoformat() if file_obj.created_at else None
                }
                
                # 对于公开文件，更新为最新的预签名URL
                if file_obj.is_public:
                    try:
                        # 为公开文件生成预签名URL
                        logger.info(f"为列表中的公开文件 {file_obj.id} 生成预签名URL")
                        
                        # 获取预签名URL
                        ssl_type = getattr(settings, "SSL_TYPE", "http").lower()
                        # 处理引号问题
                        if ssl_type.startswith('"') and ssl_type.endswith('"'):
                            ssl_type = ssl_type[1:-1]
                        if ssl_type.startswith("'") and ssl_type.endswith("'"):
                            ssl_type = ssl_type[1:-1]
                            
                        if ssl_type not in ["http", "https"]:
                            ssl_type = "http"  # 默认为http
                            
                        # 构建预签名URL
                        presigned_url = self.client.presigned_get_object(
                            bucket_name=self.bucket_name,
                            object_name=file_obj.storage_path,
                            expires=timedelta(days=7)  # 7天有效期
                        )
                        
                        # 处理URL格式
                        if presigned_url.startswith("http://") or presigned_url.startswith("https://"):
                            url = presigned_url
                        else:
                            if '?' in presigned_url:
                                path_part, query_part = presigned_url.split('?', 1)
                                if path_part.startswith('/'):
                                    path_part = path_part[1:]
                                url = f"{ssl_type}://{self.endpoint}/{path_part}?{query_part}"
                            else:
                                path_part = presigned_url
                                if path_part.startswith('/'):
                                    path_part = path_part[1:]
                                url = f"{ssl_type}://{self.endpoint}/{path_part}"
                        
                        # 更新URL
                        file_info["file_url"] = url
                    except Exception as e:
                        logger.warning(f"列表项生成预签名URL失败: {str(e)}")
                        # 失败时保留原URL
                
                file_list.append(file_info)
                
            return {
                "items": file_list,
                "total": total
            }
        except Exception as e:
            logger.error(f"获取文件列表失败: {str(e)}")
            return {"items": [], "total": 0}
        finally:
            # 如果我们创建了会话，则关闭它
            if close_db and db:
                try:
                    await db.close()
                except Exception as e:
                    logger.warning(f"关闭数据库会话失败: {str(e)}")

# 初始化MinIO存储服务单例
minio_storage_service = None 